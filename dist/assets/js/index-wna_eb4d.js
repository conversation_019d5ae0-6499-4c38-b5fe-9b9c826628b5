import{_ as e}from"./_plugin-vue_export-helper-BXFjo1rG.js";import{Q as t,m as r,c as s,o as a,h as o}from"./index-C2bfFjZ1.js";const n={class:"card content-box"};const c=e({},[["render",function(e,c){const d=t("waterMarker");return r((a(),s("div",n,c[0]||(c[0]=[o("span",{class:"text"},"水印指令 🍇🍇🍇🍓🍓🍓",-1)]))),[[d,{text:"Geeker Admin",textColor:"rgba(180, 180, 180, 0.6)"}]])}],["__scopeId","data-v-c1613812"]]);export{c as default};
