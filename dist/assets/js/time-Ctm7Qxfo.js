import{s as e,r as a,e as s,p as t,f as l,a as r,c as o,o as d,h as u,F as i,i as n,t as c,z as p,g as v,w as m,d as h}from"./index-C2bfFjZ1.js";import{_ as y}from"./_plugin-vue_export-helper-BXFjo1rG.js";const f={class:"time-selector"},$={class:"time-table"},b={class:"table-header"},g={class:"row-header"},x=["onMousedown","onMouseover"],M={class:"selected-times"},w={class:"time-groups"},_={class:"day-label"},k={class:"time-tags"},j=y(e({__name:"time",props:{timePeriod:{}},emits:["update:time-period"],setup(e,{emit:y}){const j=e,E=y,I=["周一","周二","周三","周四","周五","周六","周日"],O=a(new Set),P=a(!1),C=a(null),N=(e,a)=>O.value.has(`${e}-${a}`),z=()=>{P.value=!1,C.value=null},A=(e,a)=>{const s=`${e}-${a}`;O.value.has(s)?O.value.delete(s):O.value.add(s)},F=s((()=>{const e={},a=Array.from(O.value).map((e=>{const[a,s]=e.split("-").map(Number);return{day:a,hour:s}})).sort(((e,a)=>e.day-a.day||e.hour-a.hour));let s=null;if(a.forEach((a=>{if(s)if(a.day===s.day&&a.hour===s.end+1)s.end=a.hour;else{const t=I[s.day];e[t]||(e[t]=[]),e[t].push({day:t,time:`${s.start}:00-${s.end+1}:00`}),s={day:a.day,start:a.hour,end:a.hour}}else s={day:a.day,start:a.hour,end:a.hour}})),s){const a=I[s.day];e[a]||(e[a]=[]),e[a].push({day:a,time:`${s.start}:00-${s.end+1}:00`})}return e}));t((()=>{j.timePeriod&&Object.entries(j.timePeriod).forEach((([e,a])=>{a&&a.split("").forEach(((a,s)=>{if("1"===a){const a=I.findIndex((a=>a==={mon:"周一",tues:"周二",wed:"周三",thur:"周四",fri:"周五",sat:"周六",sun:"周日"}[e]));-1!==a&&O.value.add(`${a}-${s}`)}}))}))}));return l(O,(()=>{(()=>{const e={mon:"0".repeat(24),tues:"0".repeat(24),wed:"0".repeat(24),thur:"0".repeat(24),fri:"0".repeat(24),sat:"0".repeat(24),sun:"0".repeat(24)};O.value.forEach((a=>{const[s,t]=a.split("-").map(Number),l=Object.keys(e)[s];e[l]=e[l].substring(0,t)+"1"+e[l].substring(t+1)})),E("update:time-period",e)})()}),{deep:!0}),(e,a)=>{const s=r("el-tag");return d(),o("div",f,[u("div",$,[u("div",b,[a[0]||(a[0]=u("div",{class:"header-cell first-cell"},[u("div",{class:"diagonal-line"}),u("span",{class:"top-text"},"小时"),u("span",{class:"bottom-text"},"星期")],-1)),(d(),o(i,null,n(24,(e=>u("div",{key:e-1,class:"header-cell"},c(e-1),1))),64))]),(d(),o(i,null,n(I,((e,a)=>u("div",{key:e,class:"table-row"},[u("div",g,c(e),1),(d(),o(i,null,n(24,(e=>u("div",{key:`${a}-${e-1}`,class:p(["time-cell",{selected:N(a,e-1)}]),onMousedown:s=>((e,a)=>{P.value=!0,C.value={day:e,hour:a},A(e,a)})(a,e-1),onMouseover:s=>((e,a)=>{if(!P.value)return;O.value.clear();const s=Math.min(C.value.day,e),t=Math.max(C.value.day,e),l=Math.min(C.value.hour,a),r=Math.max(C.value.hour,a);for(let o=s;o<=t;o++)for(let e=l;e<=r;e++)O.value.add(`${o}-${e}`)})(a,e-1),onMouseup:z},null,42,x))),64))]))),64))]),u("div",M,[a[1]||(a[1]=u("div",{class:"section-title"},"已选择的时段",-1)),u("div",w,[(d(!0),o(i,null,n(F.value,((e,a)=>(d(),o("div",{key:a,class:"day-group"},[u("div",_,c(a)+"：",1),u("div",k,[(d(!0),o(i,null,n(e,((e,a)=>(d(),v(s,{key:a,closable:"",onClose:a=>(e=>{const a=I.indexOf(e.day),[s,t]=e.time.split("-").map((e=>parseInt(e)));for(let l=s;l<t;l++)O.value.delete(`${a}-${l}`)})(e)},{default:m((()=>[h(c(e.time),1)])),_:2},1032,["onClose"])))),128))])])))),128))])])])}}}),[["__scopeId","data-v-d965afad"]]);export{j as default};
