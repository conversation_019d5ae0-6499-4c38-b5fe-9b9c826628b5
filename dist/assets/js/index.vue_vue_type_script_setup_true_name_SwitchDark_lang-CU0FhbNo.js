import{s as a,U as e,V as n,g as s,y as i,W as o,X as t,a as c,o as r}from"./index-BE6Fh1xm.js";const l=a({name:"SwitchDark"}),m=a({...l,setup(a){const{switchDark:l}=e(),m=n();return(a,e)=>{const n=c("el-switch");return r(),s(n,{modelValue:i(m).isDark,"onUpdate:modelValue":e[0]||(e[0]=a=>i(m).isDark=a),"inline-prompt":"","active-icon":i(t),"inactive-icon":i(o),onChange:i(l)},null,8,["modelValue","active-icon","inactive-icon","onChange"])}}});export{m as _};
