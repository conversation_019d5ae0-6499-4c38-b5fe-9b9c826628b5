import{d as a}from"./vuedraggable.umd-D4cukam7.js";import e from"./index-DLjK4Szz.js";import t from"./index-DUPuxzvH.js";import{_ as s}from"./_plugin-vue_export-helper-GSmkUi5K.js";import{a as l,c as d,o as i,b as n,w as o,h as r}from"./index-BE6Fh1xm.js";import"./index-kjei_B5n.js";const b={class:"card content-box"},c={class:"xhs-container"},h={class:"xhs-container"};const u=s({components:{draggable:a,xhsDataTable:e,dyDataTable:t},data:()=>({activeTab:"douyin"}),methods:{handleTabClick(){var a,e;"douyin"===this.activeTab&&(null==(a=this.$refs.dyTable)?void 0:a.getList)?this.$refs.dyTable.getList():"dandelion"===this.activeTab&&(null==(e=this.$refs.xhsTable)?void 0:e.getList)&&this.$refs.xhsTable.getList()}}},[["render",function(a,e,t,s,u,T){const m=l("dyDataTable"),f=l("el-tab-pane"),p=l("xhsDataTable"),v=l("el-tabs");return i(),d("div",b,[n(v,{modelValue:u.activeTab,"onUpdate:modelValue":e[0]||(e[0]=a=>u.activeTab=a),style:{width:"100%"},onTabChange:T.handleTabClick},{default:o((()=>[n(f,{label:"星图",name:"douyin"},{default:o((()=>[r("div",c,[n(m,{ref:"dyTable"},null,512)])])),_:1}),n(f,{label:"蒲公英",name:"dandelion",lazy:""},{default:o((()=>[r("div",h,[n(p,{ref:"xhsTable"},null,512)])])),_:1})])),_:1},8,["modelValue","onTabChange"])])}],["__scopeId","data-v-fc4905d9"]]);export{u as default};
