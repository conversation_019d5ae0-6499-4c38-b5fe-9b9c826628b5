import{s as a,u as e,H as i,V as l,aa as t,r as o,a as s,c as n,o as c,h as p,b as u,w as r,d as f,y as d,ab as m,ac as y,ad as b,ae as _,af as h,ag as k,q as C,E as v,ah as x}from"./index-C2bfFjZ1.js";import{u as T,a as A}from"./tabs-B2gAxMzC.js";const P={class:"card content-box"},K={class:"mb30"},V=a({name:"tabs"}),g=a({...V,setup(a){const V=e(),g=i(),w=T(),j=l(),z=A(),M=t("refresh"),N=()=>{setTimeout((()=>{V.meta.isKeepAlive&&z.removeKeepAliveName(V.fullPath),M(!1),C((()=>{V.meta.isKeepAlive&&z.addKeepAliveName(V.fullPath),M(!0)}))}),0)},S=o(""),q=()=>{if(!S.value)return v.warning("请输入标题");w.setTabsTitle(S.value)},E=()=>{j.setGlobalState("maximize",!j.maximize)},G=()=>{V.meta.isAffix||w.removeTabs(V.fullPath)},H=()=>{w.closeMultipleTab(V.fullPath)},O=a=>{w.closeTabsOnSide(V.fullPath,a)},U=()=>{w.closeMultipleTab(),g.push(x)},$=a=>{g.push(`/assembly/tabs/detail/${a}`)};return(a,e)=>{const i=s("el-button"),l=s("el-input"),t=s("el-space");return c(),n("div",P,[e[21]||(e[21]=p("span",{class:"text"}," 标签页操作 🍓🍇🍈🍉",-1)),p("div",K,[u(l,{modelValue:S.value,"onUpdate:modelValue":e[0]||(e[0]=a=>S.value=a),placeholder:"请输入内容",style:{width:"500px"}},{append:r((()=>[u(i,{type:"primary",onClick:q},{default:r((()=>e[8]||(e[8]=[f(" 设置 Tab 标题 ")]))),_:1})])),_:1},8,["modelValue"])]),u(t,{class:"mb30"},{default:r((()=>[u(i,{type:"primary",icon:d(m),onClick:N},{default:r((()=>e[9]||(e[9]=[f(" 刷新当前页 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(y),onClick:E},{default:r((()=>e[10]||(e[10]=[f(" 当前页全屏切换 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(y),onClick:e[1]||(e[1]=a=>O("left"))},{default:r((()=>e[11]||(e[11]=[f(" 关闭左侧标签页 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(y),onClick:e[2]||(e[2]=a=>O("right"))},{default:r((()=>e[12]||(e[12]=[f(" 关闭右侧标签页 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(b),onClick:G},{default:r((()=>e[13]||(e[13]=[f(" 关闭当前页 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(_),onClick:H},{default:r((()=>e[14]||(e[14]=[f(" 关闭其他 ")]))),_:1},8,["icon"]),u(i,{type:"primary",icon:d(h),onClick:U},{default:r((()=>e[15]||(e[15]=[f(" 全部关闭 ")]))),_:1},8,["icon"])])),_:1}),u(t,{class:"mb30"},{default:r((()=>[u(i,{type:"info",icon:d(k),onClick:e[3]||(e[3]=a=>$("1"))},{default:r((()=>e[16]||(e[16]=[f(" 打开详情页1 ")]))),_:1},8,["icon"]),u(i,{type:"info",icon:d(k),onClick:e[4]||(e[4]=a=>$("2"))},{default:r((()=>e[17]||(e[17]=[f(" 打开详情页2 ")]))),_:1},8,["icon"]),u(i,{type:"info",icon:d(k),onClick:e[5]||(e[5]=a=>$("3"))},{default:r((()=>e[18]||(e[18]=[f(" 打开详情页3 ")]))),_:1},8,["icon"]),u(i,{type:"info",icon:d(k),onClick:e[6]||(e[6]=a=>$("4"))},{default:r((()=>e[19]||(e[19]=[f(" 打开详情页4 ")]))),_:1},8,["icon"]),u(i,{type:"info",icon:d(k),onClick:e[7]||(e[7]=a=>$("5"))},{default:r((()=>e[20]||(e[20]=[f(" 打开详情页5 ")]))),_:1},8,["icon"])])),_:1})])}}});export{g as default};
