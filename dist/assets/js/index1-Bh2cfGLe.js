import{k as e,r as a,f as l,a as o,Q as t,c as r,o as s,b as n,w as i,m as c,g as p,d as u,y as d,h as m,a5 as _,E as h,K as b}from"./index-BE6Fh1xm.js";import{d as v}from"./index-kjei_B5n.js";import{_ as g}from"./_plugin-vue_export-helper-GSmkUi5K.js";v().tabs;const f=localStorage.getItem("Authorization"),y=JSON.parse(localStorage.getItem("LOCAL_PERMISSION_KEY"));let x=!1;y.forEach((e=>{"system:userMenu:importMCN"==e&&(x=!0)}));const w=[{label:"机构简称",prop:"keyword",search:!0,hide:!0,editDisplay:!1,addDisplay:!1,rules:[{required:!0,message:"请输入机构简称",trigger:"blur"}]},{label:"机构简称",prop:"mcn_short_name",rules:[{required:!0,message:"请输入机构简称",trigger:"blur"}]},{label:"机构属性",search:!0,prop:"mcn_attribute",type:"select",dicUrl:"http://gitlab.yinlimedia.com:8970/admin/mcnCustomizeSelect",dicMethod:"get",dicQuery:{},dicHeaders:{Authorization:f},dicFormatter:e=>{let a=[];return e.data.list.forEach(((e,l)=>{a.push({label:e,value:l})})),a},hide:!0,display:!1},{label:"属性",prop:"mcn_attribute"},{label:"类型",prop:"type",hide:!0},{prop:"external_contracting_parties",label:"对外签约主体",hide:!0},{label:"办公地点",hide:!0,prop:"office_location"},{prop:"internal_contact_person",label:"集采对接人"},{prop:"outsourced_execution_service",label:"外包执行服务类目/明细"},{prop:"supplier_contact_person",label:"供应商联系人"},{prop:"phone",label:"联系电话"},{prop:"wechat",label:"微信号"},{prop:"service_team_members",label:"服务团队人数"},{prop:"outsourced_execution_price",label:"执行外包参考报价"},{prop:"outsourced_execution_balance",label:"执行外包结算账期"},{prop:"history_cooperation_customer",label:"引力历史合作客户"},{prop:"history_cooperation_product",label:"历史合作产品"},{prop:"total_cooperation_num",label:"过往累计合作次数"},{prop:"total_cooperation_money",label:"过往累计合作金额"},{prop:"history_collaboration_reference",label:"引力历史合作参考"},{prop:"supplement_policy",label:"补充说明"},{prop:"updated_at",label:"信息更新时间",addDisplay:!1,editDisplay:!1}],C={class:"card content-box"},z={class:"mediaList"},S={slot:"footer",class:"dialog-footer"},R=g({__name:"index1",setup(v){const g=a({}),f=a({}),y=a([]),R=a(!1),D=a({}),E={index:!1,gridBtn:!1,labelWidth:140,addBtn:x,align:"center",headerAlign:"center",searchEnter:!0,border:!0,menu:!0,dialogDrag:!0,searchSpan:8,stripe:!0,menuType:"icon",searchMenuPosition:"right",searchIcon:!0,column:w};let M=[],k=a([]),O=a(!1);const j=()=>{O.value=!1,k.value=[],uploadRef.value.clearFiles()},I=e=>{let a=e.raw;M.push(a)},L=()=>{M.length>0?M.forEach((async a=>{let l=new FormData;var o;l.append("file",a),R.value=!0,(o=l,e.post("/importMcnCustomizeMedia",o)).then((e=>{R.value=!1,990==e.code&&(O.value=!1,h.success("上传成功"),V())}))})):h.warning("请选择文件")},U=()=>{k.value=[],O.value=!0,uploadRef.value.clearFiles()},V=()=>{R.value=!0;const a=Object.assign({page:g.value.currentPage,page_size:g.value.pageSize},D.value);y.value=[],(a=>e.post("/mcnCustomizeList",a))(a).then((e=>{const a=e.data;R.value=!1,g.value.total=a.count;const l=a.list;y.value=l}))},A=(a,l,o)=>{var t;(t=Object.assign(a),e.post("/mcnCustomizeAdd",t)).then((()=>{h.success("新增成功"),l(),V()})).catch((()=>{o()}))},F=(a,l,o,t)=>{var r;(r=Object.assign(a),e.post("/mcnCustomizeEdit",r)).then((()=>{h.success("修改成功"),o(),V()})).catch((()=>{t()}))},B=a=>{b.confirm("此操作将永久删除该用户, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{let l={id:a.id};var o;(o=l,e.post("/mcnCustomizeDel",o)).then((e=>{990==e.code&&(h.success("删除成功"),V())}))}))},N=(e,a)=>{a&&a(),D.value=e,g.value.currentPage=1,V()},P=()=>{V(),h.success("刷新成功")};return l((()=>O.value),((e,a)=>{e||(k.value=[])})),(e,a)=>{const l=o("el-button"),h=o("avue-crud"),b=o("el-form-item"),v=o("UploadFilled"),x=o("el-icon"),w=o("el-upload"),D=o("el-form"),M=o("el-dialog"),K=t("permission");return s(),r("div",C,[n(h,{ref:"crud",option:d(E),page:g.value,"onUpdate:page":a[0]||(a[0]=e=>g.value=e),"table-loading":R.value,onOnLoad:V,onRowUpdate:F,onRowSave:A,onRowDel:B,onRefreshChange:P,onSearchReset:N,onSearchChange:N,modelValue:f.value,"onUpdate:modelValue":a[1]||(a[1]=e=>f.value=e),data:y.value},{"menu-left":i((()=>[c((s(),p(l,{type:"primary",icon:"upload",onClick:U},{default:i((()=>a[4]||(a[4]=[u("导入机构")]))),_:1})),[[K,"system:userMenu:importMCNs"]])])),_:1},8,["option","page","table-loading","modelValue","data"]),n(M,{title:"导入文件",modelValue:d(O),"onUpdate:modelValue":a[3]||(a[3]=e=>_(O)?O.value=e:O=e),"append-to-body":!0,width:"40%"},{default:i((()=>[m("div",null,[n(D,{"label-width":"80px"},{default:i((()=>[n(b,{label:"模板下载"},{default:i((()=>[m("div",z,[n(l,{type:"primary",class:"el-button",onClick:a[2]||(a[2]=e=>{return a="https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/mcn_customize_new.xlsx",void(window.location.href=a);var a})},{default:i((()=>a[5]||(a[5]=[u("下载机构模板 ")]))),_:1})])])),_:1}),n(b,{label:"导入"},{default:i((()=>[n(w,{class:"upload-demo",ref:"uploadRef",limit:1,"auto-upload":!1,"file-list":d(k),"on-remove":e.handleRemoveKL,"on-change":I,"on-exceed":e.onExceed,accept:".xlsx,.xls",drag:"",action:"#"},{default:i((()=>[n(x,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:i((()=>[n(v)])),_:1}),a[6]||(a[6]=m("div",{class:"el-upload__text"},[u("将文件拖到此处，或"),m("em",null,"点击上传")],-1))])),_:1},8,["file-list","on-remove","on-exceed"])])),_:1})])),_:1})]),m("span",S,[n(l,{onClick:j},{default:i((()=>a[7]||(a[7]=[u("取 消")]))),_:1}),n(l,{type:"primary",onClick:L},{default:i((()=>a[8]||(a[8]=[u("确 定")]))),_:1})])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-7176321a"]]);export{R as default};
