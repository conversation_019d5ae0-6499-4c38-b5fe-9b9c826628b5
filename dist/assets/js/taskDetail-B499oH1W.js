import{u as e,H as a,r as l,f as t,e as u,a as o,c as r,o as s,h as d,n as i,b as v,w as p,y as m,g as n,d as _,t as c,F as f,E as k}from"./index-C2bfFjZ1.js";import{q as y,r as g}from"./index-P0VwV2wd.js";import h from"./xtTaskDetailStatus1-Dk9hY2Yi.js";import b from"./xtTaskDetailStatus2-BkbMmqdq.js";import S from"./xtTaskDetailStatus3-DFRVYmFs.js";import{_ as V}from"./_plugin-vue_export-helper-BXFjo1rG.js";import"./addProject-PkgUBNHm.js";const w={class:"px-3"},D={key:0,class:"overflow-y-auto overflow-x-hidden",style:{height:"74vh"}},x={key:1,class:"overflow-y-auto overflow-x-hidden",style:{height:"74vh"}},I={key:2,class:"overflow-y-auto overflow-x-hidden",style:{height:"74vh"}},j={class:"flex"},A={key:0,class:"flex items-center"},L=["src"],C={class:"ml-2"},U={key:1},J={class:"dialog-footer"},N=V({__name:"taskDetail",props:["platform","type","id","isShow"],emits:["closeDrawer"],setup(V,{expose:N,emit:O}){e(),a(),l(null);const q=l(null),R=l(null),E=l(null),H=l(null),K=l(""),T=l(""),F=l(1),B=l(!1),P=l(null),z=V,G=O;let M=l(""),Q=l(""),W=l("");const X=l(1);z&&(X.value=z.platform,Q.value=z.type,M.value=z.id,W.value=z.isShow),t((()=>z.platform),(e=>{X.value=e})),t((()=>z.type),(e=>{Q.value=e,re(M.value)})),t((()=>z.id),(e=>{M.value=e,re(M.value)}));const Y=e=>{G("closeDrawer",e)},Z=l({hasAccount:0,platform_uid:"",kol_name:"",price:"",rebate_ratio:.2,homepage:"",placement_type:1}),$={1:{label:"1-20S视频",value:"price_1_20"},2:{label:"21-60S视频",value:"price_20_60"},3:{label:"60S以上视频",value:"price_60"},4:{label:"图文笔记一口价",value:"redbook_graphic"},5:{label:"视频笔记一口价",value:"redbook_video"}},ee={platform_uid:[{required:!0,message:"请输入媒体ID",trigger:"blur"}],project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"}]},ae={kol_name:[{required:!0,message:"请输入达人昵称",trigger:"blur"}],platform_uid:[{required:!0,message:"请输入媒体ID",trigger:"blur"}],price:[{required:!0,message:"请输入金额",trigger:"blur"}],rebate_ratio:[{required:!0,message:"请输入服务费返点",trigger:"click"}]};u((()=>{let e="";return"add"==Q.value?e="新增":"edit"==Q.value?e="编辑":"info"==Q.value&&(e="详情"),e}));const le=e=>{1==T.value.status&&"info"==Q.value?F.value=1:1==T.value.status&&"info"==Q.value?3===e||(F.value=e):2==T.value.status&&"info"==Q.value&&(F.value=e)},te=l({task_name:"",media_platform:"",task_date:[],marketing_target:"",other_demand:"",project_id:"",task_type:"1"}),ue=()=>{B.value=!0},oe=async()=>{let e=K.value,a=Z.value.placement_type;q.value&&(Z.value.hasAccount||P.value||P.value.kol_name?(await q.value.validate(((l,t)=>{if(l){try{Z.value.hasAccount?e.push({kol_name:Z.value.kol_name,platform_uid:Z.value.hasAccount?"":Z.value.platform_uid,price:Z.value.price,rebate_ratio:Z.value.rebate_ratio?Z.value.rebate_ratio:0,homepage:Z.value.homepage?Z.value.homepage:"",placement_type:a}):e.push({kol_name:P.value.kol_name,platform_uid:P.value.platform_uid,price:P.value[$[a].value],rebate_ratio:P.value.rebate_ratio?P.value.rebate_ratio:0,placement_type:a})}catch(u){}localStorage.setItem("kolList",JSON.stringify(e)),B.value=!1}})),K.value=JSON.parse(JSON.stringify(e))):k.warning("请先添加达人"))},re=e=>{y({id:e}).then((e=>{T.value=e.data,te.value=e.data,X.value=e.data.platform,te.value.task_date=[e.data.task_create_time,e.data.task_end_time],K.value=e.data.kol_data&&e.data.kol_data.length>0?e.data.kol_data:e.data.kol_select_list,localStorage.setItem("taskDetail",JSON.stringify(e.data)),localStorage.setItem("kolList",JSON.stringify(K.value)),1==e.data.status&&"info"==Q.value?F.value=1:1==e.data.status&&"edit"==Q.value?F.value=2:2==e.data.status?F.value=3:3==e.data.status&&(F.value=4),1==e.data.status&&"reEdit1"==Q.value&&(F.value=1,K.value=e.data.kol_select_list),2==e.data.status&&"reEdit2"==Q.value&&(F.value=2,K.value=e.data.kol_data)}))},se=()=>{P.value=!0;let e={keyword:Z.value.platform_uid,platform:X.value};g(e).then((e=>{P.value=e.data.list.length>0&&e.data.list[0]}))};return t((()=>z.isShow),(e=>{e&&"add"==Q.value&&(K.value=JSON.parse(localStorage.getItem("kolList"))?JSON.parse(localStorage.getItem("kolList")):[],R.value.projectSearchList),M.value&&re(M.value)})),"add"==Q.value?K.value=JSON.parse(localStorage.getItem("kolList"))?JSON.parse(localStorage.getItem("kolList")):[]:re(M.value),N({status1Ref:R,status2Ref:E,status3Ref:H}),(e,a)=>{const l=o("el-step"),t=o("el-steps"),u=o("el-radio"),k=o("el-radio-group"),y=o("el-form-item"),g=o("el-input"),V=o("el-button"),N=o("el-input-number"),O=o("el-option"),z=o("el-select"),G=o("el-form"),te=o("el-dialog");return s(),r("div",w,[d("div",null,[v(t,{active:F.value-1,"finish-status":"success",simple:"",style:{"margin-top":"20px"}},{default:p((()=>[v(l,{title:"选号",onClick:a[0]||(a[0]=e=>le(1))}),v(l,{title:"建联",onClick:a[1]||(a[1]=e=>le(2))}),v(l,{title:"下单",onClick:a[2]||(a[2]=e=>le(3))})])),_:1},8,["active"])]),1==F.value?(s(),r("div",D,[v(h,{ref_key:"status1Ref",ref:R,type:m(Q),id:m(M),platform:X.value,taskDetail:T.value,kolList:K.value,isShow:m(W),onAddKolHandler:ue,onCloseDrawer:Y},null,8,["type","id","platform","taskDetail","kolList","isShow"])])):i("",!0),2==F.value?(s(),r("div",x,[v(b,{ref_key:"status2Ref",ref:E,type:m(Q),id:m(M),platform:X.value,taskDetail:T.value,kolList:K.value,onAddKolHandler:ue,onCloseDrawer:Y},null,8,["type","id","platform","taskDetail","kolList"])])):i("",!0),3==F.value||4==F.value?(s(),r("div",I,[v(S,{ref_key:"status3Ref",ref:H,type:m(Q),id:m(M),platform:X.value,taskDetail:T.value,onAddKolHandler:ue,onCloseDrawer:Y},null,8,["type","id","platform","taskDetail"])])):i("",!0),v(te,{modelValue:B.value,"onUpdate:modelValue":a[14]||(a[14]=e=>B.value=e),title:"添加达人"},{footer:p((()=>[d("span",J,[v(V,{onClick:a[13]||(a[13]=e=>B.value=!1)},{default:p((()=>a[28]||(a[28]=[_("取消")]))),_:1}),v(V,{type:"primary",onClick:oe},{default:p((()=>a[29]||(a[29]=[_(" 确定 ")]))),_:1})])])),default:p((()=>[v(G,{model:Z.value,ref_key:"addFormRef",ref:q,"label-width":"160px",rules:Z.value.hasAccount?ae:ee},{default:p((()=>{var e,l;return[v(y,{label:"是否有媒体账号"},{default:p((()=>[v(k,{modelValue:Z.value.hasAccount,"onUpdate:modelValue":a[3]||(a[3]=e=>Z.value.hasAccount=e)},{default:p((()=>[v(u,{label:0},{default:p((()=>a[15]||(a[15]=[_("是")]))),_:1}),v(u,{label:1},{default:p((()=>a[16]||(a[16]=[_("否")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(null==(e=Z.value)?void 0:e.hasAccount)?i("",!0):(s(),n(y,{key:0,label:"媒体ID或昵称",prop:"platform_uid"},{default:p((()=>{var e;return[d("div",j,[v(g,{class:"mr-2",modelValue:Z.value.platform_uid,"onUpdate:modelValue":a[4]||(a[4]=e=>Z.value.platform_uid=e),onInput:a[5]||(a[5]=e=>P.value=!0),clearable:"",placeholder:"请输入媒体ID"},null,8,["modelValue"]),v(V,{class:"ml-2",onClick:se},{default:p((()=>a[17]||(a[17]=[_("搜索")]))),_:1})]),d("div",null,[X.value&&5==X.value?(s(),n(k,{key:0,modelValue:Z.value.placement_type,"onUpdate:modelValue":a[6]||(a[6]=e=>Z.value.placement_type=e)},{default:p((()=>[v(u,{label:1},{default:p((()=>a[18]||(a[18]=[_("1-20S视频")]))),_:1}),v(u,{label:2},{default:p((()=>a[19]||(a[19]=[_("21-60S视频")]))),_:1}),v(u,{label:3},{default:p((()=>a[20]||(a[20]=[_("60S以上视频")]))),_:1})])),_:1},8,["modelValue"])):i("",!0),X.value&&106==X.value?(s(),n(k,{key:1,modelValue:Z.value.placement_type,"onUpdate:modelValue":a[7]||(a[7]=e=>Z.value.placement_type=e)},{default:p((()=>[v(u,{label:4},{default:p((()=>a[21]||(a[21]=[_("图文笔记一口价")]))),_:1}),v(u,{label:5},{default:p((()=>a[22]||(a[22]=[_("视频笔记一口价")]))),_:1})])),_:1},8,["modelValue"])):i("",!0)]),(null==(e=P.value)?void 0:e.kol_name)&&Z.value.platform_uid?(s(),r("div",A,[d("img",{src:P.value.kol_photo,width:"50",class:"rounded-full",alt:""},null,8,L),d("div",C,c(P.value.kol_name)+" - 返点（"+c(100*P.value.rebate_ratio)+"%） 金额 （"+c(P.value[$[Z.value.placement_type].value])+"） ",1)])):0==P.value&&Z.value.platform_uid?(s(),r("div",U,"暂无符合数据，建议手动添加")):i("",!0)]})),_:1})),1==(null==(l=Z.value)?void 0:l.hasAccount)?(s(),r(f,{key:1},[v(y,{label:"昵称",prop:"kol_name"},{default:p((()=>[v(g,{modelValue:Z.value.kol_name,"onUpdate:modelValue":a[8]||(a[8]=e=>Z.value.kol_name=e),placeholder:"请填写达人昵称"},null,8,["modelValue"])])),_:1}),v(y,{label:"金额",prop:"price"},{default:p((()=>[v(g,{modelValue:Z.value.price,"onUpdate:modelValue":a[9]||(a[9]=e=>Z.value.price=e),placeholder:"请输入金额"},null,8,["modelValue"])])),_:1}),v(y,{label:"服务费返点",prop:"rebate_ratio"},{default:p((()=>[v(N,{modelValue:Z.value.rebate_ratio,"onUpdate:modelValue":a[10]||(a[10]=e=>Z.value.rebate_ratio=e),precision:2,step:.1,max:10},null,8,["modelValue"])])),_:1}),v(y,{label:"主页链接"},{default:p((()=>[v(g,{modelValue:Z.value.homepage,"onUpdate:modelValue":a[11]||(a[11]=e=>Z.value.homepage=e),placeholder:"请填写主页链接"},null,8,["modelValue"])])),_:1}),v(y,{label:"合作形式"},{default:p((()=>[v(z,{style:{width:"200px"},modelValue:Z.value.placement_type,"onUpdate:modelValue":a[12]||(a[12]=e=>Z.value.placement_type=e)},{default:p((()=>[X.value&&5==X.value?(s(),r(f,{key:0},[(s(),n(O,{key:1,label:"1-20S视频",value:1},{default:p((()=>a[23]||(a[23]=[_("1-20S视频")]))),_:1})),(s(),n(O,{key:2,label:"21-60S视频",value:2},{default:p((()=>a[24]||(a[24]=[_("21-60S视频")]))),_:1})),(s(),n(O,{key:3,label:"60S以上视频",value:3},{default:p((()=>a[25]||(a[25]=[_("60S以上视频")]))),_:1}))],64)):i("",!0),X.value&&106==X.value?(s(),r(f,{key:1},[(s(),n(O,{key:4,label:"图文笔记一口价",value:4},{default:p((()=>a[26]||(a[26]=[_("图文笔记一口价")]))),_:1})),(s(),n(O,{key:5,label:"视频笔记一口价",value:5},{default:p((()=>a[27]||(a[27]=[_("视频笔记一口价")]))),_:1}))],64)):i("",!0)])),_:1},8,["modelValue"])])),_:1})],64)):i("",!0)]})),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-790d0746"]]);export{N as default};
