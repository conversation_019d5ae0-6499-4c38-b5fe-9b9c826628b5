import{s as e,r as l,e as a,a$ as o,a as t,c as u,o as n,b as s,aD as c,w as i,y as d,g as r,an as p,h as v,F as m,i as f,a0 as b,t as V,d as _}from"./index-C2bfFjZ1.js";import{_ as h}from"./_plugin-vue_export-helper-BXFjo1rG.js";const x={class:"icon-box"},k={class:"icon-list"},y=["onClick"],C=e({name:"SelectIcon"}),g=h(e({...C,props:{iconValue:{default:""},title:{default:"请选择图标"},clearable:{type:Boolean,default:!0},placeholder:{default:"请选择图标"}},emits:["update:iconValue"],setup(e,{emit:_}){const h=l(e.iconValue),C=l(!1),g=()=>C.value=!0,w=_,I=l(),U=()=>{h.value="",w("update:iconValue",""),setTimeout((()=>I.value.blur()),0)},j=l(""),L=o,O=a((()=>{if(!j.value)return o;let e={};for(const l in L)l.toLowerCase().indexOf(j.value.toLowerCase())>-1&&(e[l]=L[l]);return e}));return(e,l)=>{const a=t("el-button"),o=t("el-input"),_=t("el-scrollbar"),T=t("el-empty"),$=t("el-dialog");return n(),u("div",x,[s(o,c({ref_key:"inputRef",ref:I,modelValue:h.value,"onUpdate:modelValue":l[0]||(l[0]=e=>h.value=e)},e.$attrs,{placeholder:e.placeholder,clearable:e.clearable,onClear:U,onClick:g}),{append:i((()=>[s(a,{icon:d(L)[e.iconValue]},null,8,["icon"])])),_:1},16,["modelValue","placeholder","clearable"]),s($,{modelValue:C.value,"onUpdate:modelValue":l[2]||(l[2]=e=>C.value=e),title:e.placeholder,top:"50px",width:"66%"},{default:i((()=>[s(o,{modelValue:j.value,"onUpdate:modelValue":l[1]||(l[1]=e=>j.value=e),placeholder:"搜索图标",size:"large","prefix-icon":p},null,8,["modelValue","prefix-icon"]),Object.keys(O.value).length?(n(),r(_,{key:0},{default:i((()=>[v("div",k,[(n(!0),u(m,null,f(O.value,(e=>(n(),u("div",{key:e,class:"icon-item",onClick:l=>(e=>{C.value=!1,h.value=e.name,w("update:iconValue",e.name),setTimeout((()=>I.value.blur()),0)})(e)},[(n(),r(b(e))),v("span",null,V(e.name),1)],8,y)))),128))])])),_:1})):(n(),r(T,{key:1,description:"未搜索到您要找的图标~"}))])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-64957f80"]]),w={class:"card content-box"},I=e({name:"selectIcon"}),U=h(e({...I,setup(e){const a=l("");return(e,l)=>{const o=t("el-descriptions-item"),c=t("el-descriptions");return n(),u("div",w,[l[5]||(l[5]=v("span",{class:"text"}," 图标选择器 🍓🍇🍈🍉",-1)),s(g,{"icon-value":a.value,"onUpdate:iconValue":l[0]||(l[0]=e=>a.value=e)},null,8,["icon-value"]),s(c,{title:"配置项 📚",column:1,border:""},{default:i((()=>[s(o,{label:"iconValue"},{default:i((()=>l[1]||(l[1]=[_(' 双向绑定的icon值，使用示例：v-model:icon-value="iconValue" ')]))),_:1}),s(o,{label:"title"},{default:i((()=>l[2]||(l[2]=[_(" 弹窗标题 ")]))),_:1}),s(o,{label:"clearable"},{default:i((()=>l[3]||(l[3]=[_(" 是否可清空，默认为 true ")]))),_:1}),s(o,{label:"placeholder"},{default:i((()=>l[4]||(l[4]=[_(" 输入框占位文本 ")]))),_:1})])),_:1})])}}}),[["__scopeId","data-v-88c50756"]]);export{U as default};
