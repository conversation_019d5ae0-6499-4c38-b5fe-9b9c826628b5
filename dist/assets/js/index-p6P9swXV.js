import{P as e}from"./index-DT-_VLMe.js";import{k as a,r as l,aA as t,f as r,a as o,Q as s,c as u,o as i,b as n,w as d,h as c,y as p,aO as m,g as _,n as v,m as b,d as f,a5 as g,E as h,K as y,aB as D,aC as x}from"./index-C2bfFjZ1.js";import{d as k}from"./index-WDAssdXv.js";import{a as w}from"./daochu-BLWkvq5l.js";import{_ as I}from"./_plugin-vue_export-helper-BXFjo1rG.js";const E=e=>{const l=e=>{if(!e||0===e.length)return"";return e.sort(((e,a)=>a.youxianji-e.youxianji))[0]};let t=[{url:"/self_resource/get",youxianji:99,chaxun:"查询所有"},{url:"/self_resource/get/is_owner",youxianji:98,chaxun:"查询自己"},{url:"/self_resource/get/2",youxianji:97,chaxun:"查询媒介所有"},{url:"/self_resource/get/2/is_owner",youxianji:96,chaxun:"查询媒介自己"}];let r=(()=>{const e=JSON.parse(localStorage.getItem("LOCAL_API_KEY")||"[]"),a=t.filter((a=>e.includes("/admin"+a.url)));return localStorage.setItem("daochu_api",l(a).chaxun),l(a).url})();return(null==e?void 0:e.updated_at)?a.get(r+"?updated_at="+e.updated_at[0]+"&updated_at="+e.updated_at[1],e):a.get(r,e)};k().tabs;let C=l([]);localStorage.getItem("Authorization");const B=JSON.parse(localStorage.getItem("LOCAL_PERMISSION_KEY"));let j=!1;B.forEach((e=>{"system:userMenu:importMCN3"==e&&(j=!0)}));const A=l([{label:"平台",prop:"platform_type",search:!0,editDisabled:!0,type:"select",dicData:[{label:"抖音",value:1},{label:"小红书",value:2},{label:"B站",value:4},{label:"快手",value:3},{label:"视频号",value:5},{label:"公众号",value:6}],rules:[{required:!0,message:"请选择平台",trigger:"blur"}]},{prop:"mcn_id",label:"机构简称",search:!0,hide:!0,addDisplay:!1,editDisplay:!1,type:"select",dicData:C.value,props:{label:"mcn_short_name",value:"mcn_id"}},{label:"MCN机构简称",prop:"mcn_short_name",formatter:(e,a,l,t)=>l||"-",addDisplay:!1,editDisplay:!1},{label:"达人昵称",search:!0,formatter:(e,a,l,t)=>l||"-",rules:[{required:!0,message:"请输入达人昵称",trigger:"blur"}],prop:"kol_name"},{label:"平台号",prop:"backend_id",formatter:(e,a,l,t)=>l||"-",search:!0,editDisabled:!0},{prop:"platform_uid",label:"后台ID",search:!0,editDisabled:!0,formatter:(e,a,l,t)=>l||"-"},{prop:"contact_information",label:"联系方式",addDisplay:!0,formatter:(e,a,l,t)=>l||"-",rules:[{required:!0,message:"请输入联系方式",trigger:"blur"}]},{prop:"rebate_ratio",label:"返点比例",addDisplay:!0,editDisplay:!0,editDisabled:!0,append:"%",controls:!0,formatter:(e,a,l)=>l?parseFloat(100*l).toFixed(0)+"%":"-",rules:[{required:!0,message:"请输入返点比例",trigger:"blur"},{validator:(e,a,l)=>{isNaN(a)?l(new Error("请输入数字")):l()}},{validator:(e,a,l)=>{a<0?l(new Error("返点比例必须大于等于0")):l()}},{validator:(e,a,l)=>{a>100?l(new Error("不得大于100")):l()}}]},{prop:"media_name",label:"媒介人员",formatter:(e,a,l,t)=>l||"-",addDisplay:!0,editDisplay:!0,rules:[{required:!0,message:"请输入媒介人员",trigger:"blur"}]},{prop:"remark",label:"备注"},{prop:"updated_at",label:"更新时间",addDisplay:!1,editDisplay:!1},{label:"更新时间",prop:"updated_at",type:"daterange",align:"center",search:!0,hide:!0,searchRange:!0,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",startPlaceholder:"开始时间",endPlaceholder:"结束时间",addDisplay:!1,editDisplay:!1},{label:"是否可挂车",prop:"is_can_hang_car",type:"select",clearable:!1,value:0,dicData:[{label:"是",value:1},{label:"否",value:2},{label:"未知",value:0}]},{label:"是否可纯佣合作",prop:"is_can_pure_commission",type:"select",clearable:!1,dicData:[{label:"是",value:1},{label:"否",value:2},{label:"未知",value:0}],value:0},{label:"是否接受供稿直发",prop:"is_accept_submission",type:"select",clearable:!1,dicData:[{label:"是",value:1},{label:"否",value:2},{label:"未知",value:0}],value:0},{label:"是否接受CPM结算",prop:"is_accept_cpm",type:"select",clearable:!1,dicData:[{label:"是",value:1},{label:"否",value:2},{label:"未知",value:0}],value:0},{label:"达人属性",prop:"tag_type",search:!0,type:"select",clearable:!0,dicData:[{label:"KOL",value:0},{label:"明星",value:1},{label:"KOC",value:2}]}]);var S;a.get("/self_resource/mcn/medias",S).then((e=>{C.value=e.data,A.value.forEach((e=>{"mcn_id"==e.prop&&(e.dicData=C.value)}))}));const M={class:"card content-box"},O={class:"header-with-icon"},V={class:"header-with-icon"},F={class:"form-item-with-icon"},Y={class:"form-item-with-icon"},N={class:"mediaList"},P={__name:"index",setup(k){const I=l({}),C=l({}),B=l([]),S=l(!1),P=l({}),R={index:!1,labelWidth:140,addBtn:j,align:"center",headerAlign:"center",border:!0,searchEnter:!0,addBtnText:"新增达人",menu:!0,delBtn:!1,editBtn:!1,dialogDrag:!0,searchSpan:8,stripe:!0,menuType:"icon",searchMenuPosition:"right",searchIcon:!0,column:A.value};let L=l([]);const U=t();let q=l(!1);const z=l(null),K=l(1),T=l({}),J=l(!1),W=l(null),Q=l(!1),G=l(0),H=l(!1),X=e=>{G.value=0,Q.value=!0,q.value=!1;let l=new FormData;l.append("file",e.raw),(e=>a.post("/self_resource/upload/2",e))(l).then((e=>{990==e.code?(L.value=[],G.value=100,$()):(L.value=[],Q.value=!1,h.error(e.message||"上传失败")),H.value=!0})).catch((e=>{L.value=[],Q.value=!1,h.error(e.message||"上传失败")}))},Z=()=>{L.value=[],q.value=!0,z.value.clearFiles()},$=()=>{S.value=!0;const e=Object.assign({page:I.value.currentPage,page_size:I.value.pageSize},P.value);B.value=[],E(e).then((e=>{const a=e.data;S.value=!1,I.value.total=a.count;const l=a.list;B.value=l}))},ee=(e,l,t)=>{if(!e.platform_uid&&!e.backend_id)return h.error("请填写后台ID或者平台号！"),void t();const r={...e};r.rebate_ratio&&(r.rebate_ratio=parseFloat(r.rebate_ratio)/100),(e=>a.post("/self_resource/add/2",e))(Object.assign(r)).then((()=>{h.success("新增成功"),l(),$()})).catch((()=>{t()}))},ae=(e,l,t,r)=>{const o={...e};o.rebate_ratio&&(o.rebate_ratio=parseFloat(o.rebate_ratio)/100),(e=>a.post("/self_resource/update/2",e))(Object.assign(o)).then((()=>{h.success("修改成功"),t(),$()})).catch((()=>{r()}))},le=e=>{y.confirm("此操作将永久删除该条数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{(e=>a.post("/self_resource/delete",e))({id:e.id}).then((e=>{990==e.code&&(h.success("删除成功"),$())}))}))},te=async()=>{await w(re.value)&&h.success("导出成功")};let re=l({});const oe=(e,a)=>{a&&a(),re.value=e,P.value=e,I.value.currentPage=1,$()},se=()=>{$(),h.success("刷新成功")};r((()=>q.value),((e,a)=>{e||(L.value=[])})),r(U.messages,(()=>{if(0===U.messages.length)return;let e=U.messages[U.messages.length-1];"self_resource_upload_progress"==e.event&&(G.value=(+e.data.index/+e.data.total*100).toFixed(0)),"self_resource_upload_complete"==e.event&&(Q.value=!1,$(),L.value=[],D({title:"提示",message:x("span",{},[x("span",{style:"color: gray"},"解析完成，共"),x("span",{style:"color: black"},e.data.success_num+e.data.error_num),x("span",{style:"color: gray"},"条数据，成功"),x("span",{style:"color: green"},e.data.success_num),x("span",{style:"color: gray"},"条，失败"),x("span",{style:"color: red"},e.data.error_num),x("span",{style:"color: gray"},"条，")]),type:"success",duration:3e3}))}),{deep:!0});const ue=()=>{0!==L.value.length?X(L.value[0]):h.warning("请先上传文件")},ie=()=>{h.warning("只能上传一个文件"),z.value.clearFiles(),L.value=[]},ne=()=>{q.value=!1,$()};return(a,l)=>{const t=o("el-button"),r=o("el-icon"),h=o("el-tooltip"),y=o("el-input"),D=o("avue-crud"),x=o("el-form-item"),k=o("UploadFilled"),w=o("el-upload"),E=o("el-form"),j=o("el-dialog"),A=s("permission"),P=s("loading");return i(),u("div",M,[n(D,{ref_key:"crud",ref:W,option:p(R),page:I.value,"onUpdate:page":l[2]||(l[2]=e=>I.value=e),"table-loading":S.value,onOnLoad:$,onRowUpdate:ae,onRowSave:ee,onRowDel:le,onRefreshChange:se,onSearchReset:oe,onSearchChange:oe,modelValue:C.value,"onUpdate:modelValue":l[3]||(l[3]=e=>C.value=e),data:B.value},{"menu-left":d((()=>[b((i(),_(t,{type:"primary",icon:"upload",onClick:Z},{default:d((()=>l[7]||(l[7]=[f(" 媒介批量导入 ")]))),_:1})),[[A,"system:userMenu:importMCNs3"]]),n(t,{type:"primary",icon:"download",onClick:te},{default:d((()=>l[8]||(l[8]=[f(" 媒介导出 ")]))),_:1})])),menu:d((({size:e,row:a,index:l})=>[a.is_owner?(i(),_(t,{key:0,link:"",type:"primary",icon:"Edit",onClick:e=>(e=>{const a={...e};null!==a.rebate_ratio&&void 0!==a.rebate_ratio&&(a.rebate_ratio=(100*a.rebate_ratio).toFixed(0)),W.value&&W.value.rowEdit(a)})(a)},null,8,["onClick"])):v("",!0),a.is_owner?(i(),_(t,{key:1,link:"",type:"primary",icon:"Delete",onClick:e=>(e=>{le(e)})(a)},null,8,["onClick"])):v("",!0)])),"backend_id-header":d((()=>[c("span",O,[l[9]||(l[9]=c("span",null,"平台号",-1)),n(h,{effect:"dark",content:"媒体平台前端展示的各平台号，例如：抖音号/小红书号/快手号/B站号/视频号",placement:"top"},{default:d((()=>[n(r,{class:"header-icon"},{default:d((()=>[n(p(m))])),_:1})])),_:1})])])),"platform_uid-header":d((()=>[c("span",V,[l[10]||(l[10]=c("span",null,"后台ID",-1)),n(h,{effect:"dark",content:"各媒体平台对应的推广后台ID，例如：星图ID/蒲公英ID/磁力聚星ID/火花ID/腾讯互选ID",placement:"top"},{default:d((()=>[n(r,{class:"header-icon"},{default:d((()=>[n(p(m))])),_:1})])),_:1})])])),"backend_id-form":d((({type:e,disabled:a})=>[c("div",F,[n(h,{effect:"dark",content:"媒体平台前端展示的各平台号，例如：抖音号/小红书号/快手号/B站号/视频号",placement:"top"},{default:d((()=>[n(r,{class:"form-item-icon"},{default:d((()=>[n(p(m))])),_:1})])),_:1}),n(y,{disabled:a,modelValue:C.value.backend_id,"onUpdate:modelValue":l[0]||(l[0]=e=>C.value.backend_id=e),placeholder:"平台号和后台ID必须填写一个"},null,8,["disabled","modelValue"])])])),"platform_uid-form":d((({type:e,disabled:a})=>[c("div",Y,[n(h,{effect:"dark",content:"各媒体平台对应的推广后台ID，例如:星图ID/蒲公英ID/磁力聚星ID/火花ID/腾讯互选ID",placement:"top"},{default:d((()=>[n(r,{class:"form-item-icon"},{default:d((()=>[n(p(m))])),_:1})])),_:1}),n(y,{disabled:a,modelValue:C.value.platform_uid,"onUpdate:modelValue":l[1]||(l[1]=e=>C.value.platform_uid=e),placeholder:"平台号和后台ID必须填写一个"},null,8,["disabled","modelValue"])])])),_:1},8,["option","page","table-loading","modelValue","data"]),n(j,{title:"批量导入","close-on-click-modal":!1,"append-to-body":!0,modelValue:p(q),"onUpdate:modelValue":l[6]||(l[6]=e=>g(q)?q.value=e:q=e)},{footer:d((()=>[n(t,{onClick:l[5]||(l[5]=e=>g(q)?q.value=!1:q=!1)},{default:d((()=>l[13]||(l[13]=[f("取 消")]))),_:1}),K.value<3?(i(),_(t,{key:0,type:"primary",onClick:ue},{default:d((()=>l[14]||(l[14]=[f("下一步")]))),_:1})):v("",!0),3===K.value?(i(),_(t,{key:1,type:"primary",onClick:ne},{default:d((()=>l[15]||(l[15]=[f("确 定")]))),_:1})):v("",!0)])),default:d((()=>[b((i(),_(E,{rules:T.value},{default:d((()=>[n(x,{label:"模板下载"},{default:d((()=>[c("div",N,[n(t,{type:"primary",class:"el-button",onClick:l[4]||(l[4]=e=>{return a="https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E5%AA%92%E4%BB%8B%E4%B8%8A%E4%BC%A0%E8%BE%BE%E4%BA%BA%E6%A8%A1%E7%89%88NEW.xlsx",void(window.location.href=a);var a})},{default:d((()=>l[11]||(l[11]=[f(" 模板下载 ")]))),_:1})])])),_:1}),n(x,{label:"上传文件",prop:"upload_path"},{default:d((()=>[n(w,{style:{width:"300px"},limit:1,ref_key:"uploadRef",ref:z,"auto-upload":!1,"on-change":X,accept:".xlsx,.xls","file-list":p(L),"on-exceed":ie,drag:"",action:"#"},{default:d((()=>[n(r,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:d((()=>[n(k)])),_:1}),l[12]||(l[12]=c("div",{class:"el-upload__text"},[f("将文件拖到此处，或"),c("em",null,"点击上传")],-1))])),_:1},8,["file-list"])])),_:1})])),_:1},8,["rules"])),[[P,J.value]])])),_:1},8,["modelValue"]),n(e,{show:Q.value,percentage:G.value,text:"文件解析中..."},null,8,["show","percentage"])])}}},R=I(P,[["__scopeId","data-v-0879426e"]]);export{R as default};
