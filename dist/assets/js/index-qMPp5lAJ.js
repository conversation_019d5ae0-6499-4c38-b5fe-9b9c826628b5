import{r as e,a6 as l,a,Q as t,c as n,o as i,h as u,m as s,b as o,w as r,d,y as m,an as c,g as p,B as g,v,aC as _,t as f,z as y,E as h,K as b}from"./index-BE6Fh1xm.js";import{y as k,i as x,j as C,l as w}from"./index-BxStTrC5.js";import{_ as z}from"./index.vue_vue_type_script_setup_true_lang-BE3RwO9D.js";import{_ as V}from"./_plugin-vue_export-helper-GSmkUi5K.js";const j={class:"card content-box"},q={class:"request_box"},S={class:"request_box_left"},U={class:"request_box-item",style:{display:"flex","justify-content":"center","align-items":"center"}},A={class:"request_box_input"},F={class:"flex justify-end padding-5-10"},T={slot:"footer",class:"dialog-footer"},E={class:"content_box"},I={class:"content_box","element-loading-text":"loading...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},P=["href"],$={class:"pagina_box"},H=["id"],W={style:{"font-size":"12px",color:"#999"}},B=["src"],D=V({__name:"index",emits:["updateRoute"],setup(V,{emit:D}){const J=e(""),K=e(),L=e();e();const N=e(null);e(null),e("oceanengine");const O=e(!1),Q=e(!1),R=e(1);e("");const X=e({}),G=e(0),M=e("添加脚本"),Y=e(!1),Z=e([]),ee=e(110),le=e(!1),ae=e(!1),te=e({search:"",page:1,page_size:10,type:2}),ne=e([]);function ie(){ae.value=!0,setTimeout((()=>{le.value=!0}),500)}function ue(){ae.value=!1}function se(){let e;const l=navigator.userAgent.toLowerCase();if(l.includes("chrome")||l.includes("chromium"))e="chrome://extensions/";else if(l.includes("firefox"))e="about:addons";else{if(!l.includes("edge"))return;e="edge://extensions"}const a=document.createElement("textarea");a.value=e,document.body.appendChild(a),a.select();try{document.execCommand("copy");h.success("链接已复制到剪贴板")}catch(t){}document.body.removeChild(a)}function oe(){window.open("https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/monkey_js/dhdgffkkebhmkfjojejmpbldmpobfkfo_5.3.3_chrome.zzzmh.cn.zip")}JSON.parse(localStorage.getItem("sidebar")||"[]").map((e=>{e.child&&e.child.map((e=>{e.url}))}));const re=async()=>{Y.value=!0,Z.value=[],await k(te.value).then((e=>{let l=e.data;Y.value=!1,Z.value=l.plugins,ee.value=l.plugins_count,G.value=G.value++}))};re();const de=l({plugins_name:"",link:"",yuntu_url:"",img:"",type:2});l({brand_id:"",account_id:"",advertiser_id:"",brand_name:""});const me={plugins_name:[{required:!0,message:"请输入脚本名称",trigger:"blur"},{max:200,message:"脚本名称不能超过200个字符",trigger:"blur"}],link:[{required:!0,message:"请输入脚本链接",trigger:"blur"},{max:200,message:"脚本链接不能超过200个字符",trigger:"blur"},{type:"url",message:"请输入正确的链接",trigger:"blur"}],yuntu_url:[{required:!0,message:"请输入聚光链接",trigger:"blur"},{max:200,message:"链接不能超过200个字符",trigger:"blur"},{type:"url",message:"请输入正确的链接",trigger:"blur"}],img:[{required:!0,message:"请上传功能引导图",trigger:"blur"}]},ce=(e,l,a)=>{e&&e.data.file&&(de.img=e.data.file,a.value=a,setTimeout((()=>{N.value.validateField("img")})))},pe=e=>!0,ge=(e,l)=>{de.img="",l.value=l,setTimeout((()=>{N.value.validateField("img")}))},ve=({file:e,onSuccess:l,onError:a,onProgress:t})=>{const n=new FormData;n.append("file",e),x(n).then((a=>{l(a,e)})).catch((e=>{a(e)}))},_e=(e,l)=>{l.length>1&&l.splice(0,1),l.value=l},fe=(e,l)=>{var a;2==e?(M.value="编辑脚本",de.plugins_name=l.plugins_name,de.link=l.link,de.yuntu_url=l.yuntu_url,de.img=l.img,de.plugin_id=l.id,l.img&&(ne.value=[{name:(a=l.img,a.length>30?a.substring(0,30)+"...":a)}])):M.value="添加脚本",O.value=!0},ye=()=>{de.plugins_name="",de.link="",de.yuntu_url="",de.img="",ne.value=[],O.value=!1},he=()=>{N.value.validate((e=>{if(!e)return!1;C([de]).then((e=>{if(990==e.code){let e=de.plugin_id?"编辑成功":"添加成功";h({message:e,type:"success"}),Y.value=!1,de.plugins_name="",de.link="",de.yuntu_url="",de.img="",delete de.plugin_id,ne.value=[],O.value=!1,re()}else h({message:"添加失败",type:"error"})}))}))},be=e=>{te.value.page_size=e,re()},ke=e=>{te.value.page=e,re()},xe=()=>{te.value.page=1,re()};return(e,l)=>{var k;const x=a("el-input"),C=a("el-icon"),V=a("el-button"),D=a("el-form-item"),G=a("el-upload"),Ce=a("el-form"),we=a("el-dialog"),ze=a("el-space"),Ve=a("el-tour-step"),je=a("el-tour"),qe=a("el-table-column"),Se=a("el-table"),Ue=a("CircleCloseFilled"),Ae=a("el-drawer"),Fe=t("permission"),Te=t("loading");return i(),n("div",j,[u("div",q,[u("div",S,[u("div",U,[l[8]||(l[8]=u("div",{class:"request_box_label",style:{"padding-right":"20px"}},"脚本名称",-1)),u("div",A,[o(x,{modelValue:te.value.search,"onUpdate:modelValue":l[0]||(l[0]=e=>te.value.search=e),clearable:"",placeholder:"请输入脚本名称"},null,8,["modelValue"])])])]),u("div",F,[o(V,{type:"primary",onClick:xe,class:"el-button ml1 mt1 el-button-middle"},{default:r((()=>[o(C,null,{default:r((()=>[o(m(c))])),_:1}),l[9]||(l[9]=d("搜索"))])),_:1}),s((i(),p(V,{type:"primary",onClick:l[1]||(l[1]=e=>fe(1)),class:"el-button ml1 mt1"},{default:r((()=>[o(C,null,{default:r((()=>[o(m(g))])),_:1}),l[10]||(l[10]=d("添加脚本"))])),_:1})),[[Fe,"system:report:tampermonkeyAddScript"]])]),o(we,{title:M.value,modelValue:O.value,"onUpdate:modelValue":l[5]||(l[5]=e=>O.value=e),width:"30%","before-close":ye},{default:r((()=>[o(Ce,{model:de,ref_key:"scriptForm",ref:N,rules:me},{default:r((()=>[o(D,{prop:"plugins_name",label:"脚本名称"},{default:r((()=>[o(x,{modelValue:de.plugins_name,"onUpdate:modelValue":l[2]||(l[2]=e=>de.plugins_name=e)},null,8,["modelValue"])])),_:1}),o(D,{prop:"link",label:"脚本链接"},{default:r((()=>[o(x,{modelValue:de.link,"onUpdate:modelValue":l[3]||(l[3]=e=>de.link=e)},null,8,["modelValue"])])),_:1}),o(D,{prop:"yuntu_url",label:"聚光链接"},{default:r((()=>[o(x,{modelValue:de.yuntu_url,"onUpdate:modelValue":l[4]||(l[4]=e=>de.yuntu_url=e)},null,8,["modelValue"])])),_:1}),o(D,{prop:"img",label:"功能引导"},{default:r((()=>[o(G,{accept:"image/gif","file-list":ne.value,class:"upload-demo","http-request":ve,"on-success":ce,"on-change":_e,"on-remove":ge,"before-upload":pe},{default:r((()=>[o(V,{type:"primary"},{default:r((()=>l[11]||(l[11]=[d("上传引导图")]))),_:1})])),_:1},8,["file-list"])])),_:1})])),_:1},8,["model"]),u("div",T,[o(V,{onClick:ye},{default:r((()=>l[12]||(l[12]=[d("取 消")]))),_:1}),o(V,{type:"primary",onClick:he},{default:r((()=>l[13]||(l[13]=[d("确 定")]))),_:1})])])),_:1},8,["title","modelValue"])]),u("div",E,[s(u("div",null,[u("span",{class:"cursor-pointer purple text-fs-14 padding-5-10",onClick:ie}," 如何安装油猴脚本？ ")],512),[[v,!ae.value]]),s(u("div",null,[o(ze,null,{default:r((()=>[o(V,{ref_key:"ref1",ref:K,onClick:oe},{default:r((()=>l[14]||(l[14]=[d("下载安装包")]))),_:1},512),o(V,{ref_key:"ref2",ref:L,type:"primary",onClick:se},{default:r((()=>l[15]||(l[15]=[d("管理扩展程序")]))),_:1},512)])),_:1}),o(je,{modelValue:le.value,"onUpdate:modelValue":l[6]||(l[6]=e=>le.value=e),onClose:ue,class:"tour"},{default:r((()=>{var e,a;return[o(Ve,{target:null==(e=K.value)?void 0:e.$el,placement:"right",title:"下载安装包并解压"},{default:r((()=>l[16]||(l[16]=[u("img",{style:{width:"240px"},src:"https://cdn.yinlimedia.com/media/yuntu_plugins_files/202408/08/<EMAIL>",alt:"tour.png"},null,-1),u("div",null,"点击“下载安装包”按钮，立即开始下载",-1)]))),_:1},8,["target"]),o(Ve,{"content-style":"width: 60%;",placement:"right",target:null==(a=L.value)?void 0:a.$el,title:"打开扩展程序管理页面"},{default:r((()=>l[17]||(l[17]=[u("img",{src:"https://cdn.yinlimedia.com/media/yuntu_plugins_files/202408/09/ezgif-5-a146ecf70a-20240809151521.gif",alt:""},null,-1),u("div",null," 点击“管理扩展程序”按钮复制链接到浏览器地址栏，点击回车打开扩展程序管理页面，打开右上角的“开发者模式”，点击“加载已解压的扩展程序”按钮，选择解压后的安装包，即可完成安装。 ",-1)]))),_:1},8,["target"])]})),_:1},8,["modelValue"])],512),[[v,ae.value]])]),s((i(),n("div",I,[(i(),p(Se,{"header-cell-style":{color:"#666",fontWeight:600,textAlign:"center"},"cell-style":{textAlign:"center"},key:null==(k=Z.value)?void 0:k.id,ref:"table",size:"small",data:Z.value,border:"",fit:"",class:"commonTable",style:{width:"100%"}},{default:r((()=>[o(qe,{prop:"plugins_name",label:"脚本名称",width:"480"}),o(qe,{prop:"link",label:"脚本地址","min-width":480},{default:r((e=>[o(m(_),{content:e.row.link,placement:"top-start",effect:"dark","show-after":200},{default:r((()=>{return[u("a",{href:e.row.link,target:"_blank",class:"text-blue-500 padding-5-10"},f((l=e.row.link,J.value===l?l:l.length>70?l.substring(0,70)+"...":l)),9,P)];var l})),_:2},1032,["content"])])),_:1}),o(qe,{label:"操作",width:"400px",fixed:"right"},{default:r((e=>[o(V,{style:{"font-size":"12px"},type:"text",size:"small",onClick:l=>{return a=e.row,R.value=t,X.value=a,void(Q.value=!0);var a,t}},{default:r((()=>l[18]||(l[18]=[d("功能引导")]))),_:2},1032,["onClick"]),s((i(),p(V,{style:{"font-size":"12px"},type:"text",size:"small",onClick:l=>fe(2,e.row)},{default:r((()=>l[19]||(l[19]=[d("编辑脚本")]))),_:2},1032,["onClick"])),[[Fe,"system:report:tampermonkeyAddScript"]]),o(V,{style:{"font-size":"12px"},type:"text",size:"small",onClick:l=>{return a=null==e?void 0:e.row,void window.open(a.yuntu_url,"_blank");var a}},{default:r((()=>l[20]||(l[20]=[d("跳转聚光")]))),_:2},1032,["onClick"]),s((i(),p(V,{style:{"font-size":"12px"},type:"text",size:"small",onClick:l=>{return a=null==e?void 0:e.row,void b.confirm(`确定删除“${a.plugins_name}“吗？`).then((()=>{w(a.id).then((e=>{990==e.code?(h({message:"删除成功",type:"success"}),re()):h({message:"删除失败",type:"error"})}))})).catch((()=>{}));var a}},{default:r((()=>l[21]||(l[21]=[d("删除脚本")]))),_:2},1032,["onClick"])),[[Fe,"system:report:tampermonkeyAddScript"]])])),_:1})])),_:1},8,["data"])),u("div",$,[o(z,{onHandleSizeChange:be,onHandleCurrentChange:ke,total:ee.value,currentPage:te.value.page,"page-size":te.value.page_size,layout:"sizes,next,total, prev, pager,jumper"},null,8,["total","currentPage","page-size"])])])),[[Te,Y.value]]),o(Ae,{modelValue:Q.value,"onUpdate:modelValue":l[7]||(l[7]=e=>Q.value=e),size:"60%"},{header:r((({close:e,titleId:l,titleClass:a})=>[u("h4",{id:l,class:y(a)},"功能引导",10,H),o(C,{class:"el-icon--left",onClick:e},{default:r((()=>[o(Ue)])),_:2},1032,["onClick"])])),default:r((()=>[u("div",null,[u("p",W,f(X.value.plugins_name),1),u("img",{src:X.value.img,alt:""},null,8,B)])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-c701a062"]]);export{D as default};
