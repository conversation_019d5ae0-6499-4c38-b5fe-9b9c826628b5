import{u as a,r as e,f as l,p as i,a as s,c as d,o as t,h as n,C as r,b as v,w as c,d as u,a5 as _,y as m,t as p,F as o,i as b}from"./index-BE6Fh1xm.js";import{n as x}from"./detail-CsDhBsY3.js";import{_ as g}from"./_plugin-vue_export-helper-GSmkUi5K.js";const h={class:"author-page-content"},f={class:"author-overview"},y={class:"card-panel module-card composite"},k={class:"card-panel-body"},w={class:"flex space-between align-center"},D={style:{flex:"2"}},E={style:{flex:"7"}},N={class:"base-info-list-wrapper mt1"},R={class:"inline-expand-box-wrapper"},V={class:"inline-expand__content"},U={class:"info-content flex"},j={class:"info-item"},q={class:"info-item__value"},z={class:"info-item"},C={class:"info-item__value"},F={class:"info-card-list"},I={class:"info-card"},T={class:"info-card__content"},A={class:"recent-core-data-wrapper"},B={class:"carousel-box"},G={class:"data-list flex"},H={class:"data-item"},J={class:"data-item__value REDNumber"},K={class:"data-item__benchmark"},L={style:{color:"rgba(0, 0, 0, 0.85)",margin:"0px 4px"}},M={class:"data-item"},O={class:"data-item__value REDNumber"},P={class:"data-item__benchmark"},Q={style:{color:"rgba(0, 0, 0, 0.85)",margin:"0px 4px"}},S={class:"data-item flex"},W={class:"data-item__value REDNumber"},X={class:"data-item__benchmark"},Y={style:{color:"rgba(0, 0, 0, 0.85)",margin:"0px 4px"}},Z={class:"data-item__extra pos-trans"},$={class:"extra-item"},aa={class:"extra-item__value REDNumber"},ea={class:"extra-item"},la={class:"extra-item__value REDNumber"},ia={class:"extra-item"},sa={class:"extra-item__value REDNumber"},da={class:"extra-item"},ta={class:"extra-item__value REDNumber"},na={class:"data-item",style:{width:"78px"}},ra={class:"data-item__value REDNumber"},va={class:"carousel-box"},ca={class:"data-list flex"},ua={class:"data-item",style:{width:"78px"}},_a={class:"data-item__value REDNumber"},ma={class:"data-item"},pa={class:"data-item__value REDNumber"},oa={class:"data-item"},ba={class:"data-item__value REDNumber"},xa={class:"data-item"},ga={class:"data-item__value REDNumber"},ha={class:"data-item"},fa={class:"data-item__value REDNumber"},ya=g({__name:"xiaohongshuView",setup(g){const ya=a().query.platform_uid;e({});let ka=e(3),wa=e(1),Da=e(1),Ea=e(1),Na=e({});l([()=>ka.value,()=>wa.value,()=>Da.value,()=>Ea.value],(a=>{Ra()}));const Ra=()=>x({platform_uid:ya,note_type:ka.value,date_type:wa.value,business_type:Da.value,flow_type:Ea.value}).then((a=>{Na.value=a.data}));return i((()=>{Ra()})),(a,e)=>{const l=s("el-radio-button"),i=s("el-radio-group"),x=s("el-option"),g=s("el-select"),ya=s("el-carousel-item"),Ra=s("el-carousel");return t(),d("div",h,[n("div",f,[n("div",y,[e[36]||(e[36]=r('<div class="title-wrapper" data-v-83a8eeb6><div class="title" data-v-83a8eeb6>近期传播表现</div><span class="desc" data-v-83a8eeb6></span><div class="operation" data-v-83a8eeb6></div></div><div class="sub-title" data-v-83a8eeb6><div class="divider el-divider el-divider--horizontal" data-v-83a8eeb6></div></div>',2)),n("div",k,[n("div",w,[n("div",D,[v(i,{modelValue:m(Da),"onUpdate:modelValue":e[0]||(e[0]=a=>_(Da)?Da.value=a:Da=a)},{default:c((()=>[v(l,{label:"1"},{default:c((()=>e[4]||(e[4]=[u("日常笔记")]))),_:1}),v(l,{label:"2"},{default:c((()=>e[5]||(e[5]=[u("合作笔记")]))),_:1})])),_:1},8,["modelValue"])]),n("div",E,[v(g,{style:{width:"180px"},modelValue:m(ka),"onUpdate:modelValue":e[1]||(e[1]=a=>_(ka)?ka.value=a:ka=a),class:"select-input"},{default:c((()=>[v(x,{label:"图文",value:1}),v(x,{label:"视频",value:2}),v(x,{label:"图文+视频",value:3})])),_:1},8,["modelValue"]),v(g,{style:{width:"180px"},modelValue:m(wa),"onUpdate:modelValue":e[2]||(e[2]=a=>_(wa)?wa.value=a:wa=a),class:"select-input"},{default:c((()=>[v(x,{label:"近30日",value:1}),v(x,{label:"近90日",value:2})])),_:1},8,["modelValue"]),v(g,{style:{width:"180px"},modelValue:m(Ea),"onUpdate:modelValue":e[3]||(e[3]=a=>_(Ea)?Ea.value=a:Ea=a),class:"select-input"},{default:c((()=>[v(x,{label:"全流量",value:1}),v(x,{label:"仅自然流量",value:2})])),_:1},8,["modelValue"])])]),n("div",N,[n("div",R,[n("div",V,[n("div",U,[n("div",j,[e[6]||(e[6]=n("span",{class:"info-item__label has-tooltip"},"发布笔记",-1)),n("span",q,p(m(Na).note_num)+"篇",1),e[7]||(e[7]=n("div",{class:"d-divider d-divider-vertical",style:{display:"inline-block",height:"16px",background:"rgba(0, 0, 0, 0.15)",margin:"0px 12px","vertical-align":"middle"}},null,-1))]),n("div",z,[e[8]||(e[8]=n("span",{class:"info-item__label has-tooltip"},"内容类目及占比",-1)),n("span",C,[(t(!0),d(o,null,b(m(Na).note_type_category,(a=>(t(),d("span",null,p(a.contentTag)+" ( "+p(a.percent)+"% ) ",1)))),256))])])])])])]),n("div",F,[n("div",I,[e[35]||(e[35]=n("div",{class:"info-card__title"},"近期核心表现",-1)),n("div",T,[n("div",A,[v(Ra,{trigger:"click",autoplay:!1,height:"150px"},{default:c((()=>[v(ya,null,{default:c((()=>[n("div",B,[n("div",G,[n("div",H,[n("div",null,[e[11]||(e[11]=n("div",{class:"data-item__label"},[n("span",null,"曝光中位数")],-1)),n("div",J,p(m(Na).pv_mddian_num),1),n("div",K,[e[9]||(e[9]=u(" 超过")),n("span",L,p(m(Na).pv_mddian_beyong_rate)+"%",1),e[10]||(e[10]=u("同类博主 "))])])]),e[23]||(e[23]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",M,[n("div",null,[e[14]||(e[14]=n("div",{class:"data-item__label"},[n("span",null,"阅读中位数")],-1)),n("div",O,p(m(Na).read_mddian_num),1),n("div",P,[e[12]||(e[12]=u(" 超过")),n("span",Q,p(m(Na).read_mddian_beyong_rate)+"%",1),e[13]||(e[13]=u("同类博主 "))])])]),e[24]||(e[24]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",S,[n("div",null,[e[17]||(e[17]=n("div",{class:"data-item__label"},[n("span",null,"互动中位数")],-1)),n("div",W,p(m(Na).interaction_num),1),n("div",X,[e[15]||(e[15]=u(" 超过")),n("span",Y,p(m(Na).interaction_beyond_rate)+"%",1),e[16]||(e[16]=u("同类博主 "))])]),n("div",Z,[n("div",$,[e[18]||(e[18]=n("span",{class:"extra-item__label"},"中位点赞量",-1)),n("span",aa,p(m(Na).like_median_num),1)]),n("div",ea,[e[19]||(e[19]=n("span",{class:"extra-item__label"},"中位收藏量",-1)),n("span",la,p(m(Na).collect_median_num),1)]),n("div",ia,[e[20]||(e[20]=n("span",{class:"extra-item__label"},"中位评论量",-1)),n("span",sa,p(m(Na).comment_median_num),1)]),n("div",da,[e[21]||(e[21]=n("span",{class:"extra-item__label"},"中位分享量",-1)),n("span",ta,p(m(Na).share_median_num),1)])])]),e[25]||(e[25]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",na,[n("div",null,[e[22]||(e[22]=n("div",{class:"data-item__label"},[n("span",null,"互动率")],-1)),n("div",ra,p(m(Na).interaction_rate)+"%",1)])])])])])),_:1}),v(ya,null,{default:c((()=>[n("div",va,[n("div",ca,[n("div",ua,[n("div",null,[e[26]||(e[26]=n("div",{class:"data-item__label"},[n("span",null,"互动率")],-1)),n("div",_a,p(m(Na).interaction_rate)+"%",1)])]),e[31]||(e[31]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",ma,[n("div",null,[e[27]||(e[27]=n("div",{class:"data-item__label"},[n("span",null,"视频完播率")],-1)),n("div",pa,p(m(Na).video_fullview_rate)+"%",1)])]),e[32]||(e[32]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",oa,[n("div",null,[e[28]||(e[28]=n("div",{class:"data-item__label"},[n("span",null,"图文3秒阅读率")],-1)),n("div",ba,p(m(Na).picture_3s_view_rate)+"%",1)])]),e[33]||(e[33]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",xa,[n("div",null,[e[29]||(e[29]=n("div",{class:"data-item__label"},[n("span",null,"千赞笔记比例")],-1)),n("div",ga,p(m(Na).thousand_like_rate)+"%",1)])]),e[34]||(e[34]=n("div",{class:"d-divider d-divider-vertical",style:{height:"60px",background:"rgba(0, 0, 0, 0.06)",margin:"0px 15px","flex-shrink":"0"}},null,-1)),n("div",ha,[n("div",null,[e[30]||(e[30]=n("div",{class:"data-item__label"},[n("span",null,"百赞笔记比例")],-1)),n("div",fa,p(m(Na).hundred_like_rate)+"%",1)])])])])])),_:1})])),_:1})])])])])])])])])}}},[["__scopeId","data-v-83a8eeb6"]]);export{ya as default};
