import{d as e}from"./vuedraggable.umd-D4cukam7.js";import{_ as l}from"./index.vue_vue_type_script_setup_true_lang-BE3RwO9D.js";import{k as a,c as t,d as s,f as u,g as i,h as n,i as d,j as o,l as c}from"./business-CcSWCctm.js";import{_ as r}from"./_plugin-vue_export-helper-GSmkUi5K.js";import{r as v,f as m,a as p,Q as h,c as f,o as g,b as _,w as k,h as b,m as y,g as w,d as x,F as C,i as V,t as j,z,at as A,n as E,y as U,E as S}from"./index-BE6Fh1xm.js";const K={key:0,class:"content_box","element-loading-text":"loading...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},H={key:0},O={key:1},P={class:"define-template cursor"},F=["onClick"],I={class:"fz12"},Q={class:"flex justify-center mt-1"},R={class:"pagina_box"},T={slot:"footer",class:"dialog-footer mt-2"},B={class:"flex justify-end mt-2"},D={class:"flex justify-start items-center mb-2"},J={slot:"footer",class:"dialog-footer mt-2"},M={class:"flex justify-end mt-2"},N={class:"border-div"},W={class:"item-list"},X={class:"item-title"},q={class:"flex"},G={class:"title-header header-height"},L={id:"items",class:"items-box"},Y={class:"flex align-center header-item"},Z={class:"flex align-center justify-start"},$={class:"save-template"},ee=r({__name:"submitReport",props:["dialogVisible","kolSelectConfirm"],emits:["cancelAddKol","confirmAddKol"],setup(r,{emit:ee}){const le=r,ae=v({task_name:"",status:"",page:1,page_size:10}),te=v(1),se=v(!1);v("");const ue=v([]),ie=v(0),ne=v([]),de=ee,oe=v(!1),ce=v(!1);v(!1);const re=v(!1),ve=v([]),me=v("add"),pe=v({}),he=v(""),fe=v(""),ge=v([]),_e=v(""),ke=v(""),be=v([]),ye=e=>{de("cancelAddKol")},we=()=>{de("cancelAddKol")},xe=()=>{let e={kol_arr:ne.value,isXTradio:te.value};de("confirmAddKol",e)},Ce=()=>{oe.value=!0,d().then((e=>{oe.value=!1,pe.value=Array.isArray(e.data)?e.data:Object.values(e.data),be.value=new Array(pe.value.length).fill([]),pe.value.forEach((e=>{e.checked=!1,e.detail&&e.detail.forEach((e=>{e.checked=!1}))}))}))},Ve=e=>{ae.value.page_size=e,Ue()},je=e=>{ae.value.page=e,Ue()},ze=()=>{ce.value=!0},Ae=()=>{re.value=!1,he.value="",ve.value=[]},Ee=()=>{re.value=!0,me.value="add",fe.value="",he.value="",ve.value=[],pe.value=[],Ce()},Ue=()=>{se.value=!0,o({page:ae.value.page,page_size:ae.value.page_size,status:"",task_name:""}).then((e=>{ue.value=e.data,ue.value.list.map((e=>{e.show=!1})),se.value=!1,ie.value=e.data.count}))},Se=()=>{n(ae.value).then((e=>{ge.value=e.data.list}))},Ke=()=>{let e={};ve.value.map((l=>{e[l.value]=l.parent?l.parent+l.label:l.label})),he.value&&ve.value.length>0&&Object.keys(e).length>0?(oe.value=!0,"add"==me.value?u({template_name:he.value,custom_json:e}).then((e=>{S.success("添加成功"),re.value=!1,oe.value=!1,Se(),He()})):i({template_name:he.value,custom_json:e,template_id:fe.value}).then((e=>{S.success("编辑成功"),re.value=!1,oe.value=!1,Se(),He()}))):S.warning("请填写完整信息")},He=()=>{ve.value=[],he.value="",pe.value.forEach(((e,l)=>{e.checked=!1,e.detail&&e.detail.forEach((e=>{e.checked=!1})),be.value[l]=[]}))},Oe=()=>{_e.value?s({task_name:_e.value,upload_path:ke.value}).then((e=>{S.success("提报成功"),ce.value=!1,de("cancelAddKol")})):S.warning("请填写任务名称")};return m((()=>le.dialogVisible),(e=>{if(e&&(Ue(),Se(),Ce(),le.kolSelectConfirm&&le.kolSelectConfirm.length>0)){let e=le.kolSelectConfirm.map((e=>({platform_uid:e.platform_uid,kol_name:e.kol_name})));c({kols:e}).then((e=>{ke.value=e.data.file_name}))}})),(s,u)=>{const i=p("el-button"),n=p("el-table-column"),d=p("Edit"),o=p("el-icon"),c=p("Delete"),r=p("el-popconfirm"),v=p("Plus"),m=p("el-popover"),ee=p("el-table"),te=p("el-empty"),ne=p("el-dialog"),de=p("el-input"),ke=p("el-checkbox"),Ce=p("QuestionFilled"),Ue=p("el-tooltip"),He=p("el-checkbox-group"),Pe=p("el-col"),Fe=p("Close"),Ie=p("el-row"),Qe=h("loading");return g(),f(C,null,[_(ne,{title:"提报任务",modelValue:le.dialogVisible,"onUpdate:modelValue":u[0]||(u[0]=e=>le.dialogVisible=e),width:"60%","before-close":ye},{default:k((()=>[b("div",null,[_(i,{type:"primary",class:"float-right mb-2",onClick:ze,style:{"margin-bottom":"20px"},size:"small"},{default:k((()=>u[6]||(u[6]=[x("创建")]))),_:1}),ue.value&&ue.value.list&&ue.value.list.length>0?y((g(),f("div",K,[y((g(),w(ee,{"header-cell-style":{color:"#666",fontWeight:600,textAlign:"center"},"cell-style":{textAlign:"center"},key:s.key,ref:"table",size:"small",data:ue.value.list,border:"",fit:"",class:"commonTable",style:{width:"100%"}},{default:k((()=>[(g(!0),f(C,null,V(ue.value.table,((e,l)=>(g(),w(n,{label:e.name,align:"center"},{default:k((l=>["status"==e.value?(g(),f("div",H,j(0==l.row[e.value]?"进行中":"已完成"),1)):(g(),f("div",O,j(l.row[e.value]?l.row[e.value]:"-"),1))])),_:2},1032,["label"])))),256)),_(n,{label:"操作",fixed:"right","min-width":100,align:"center"},{default:k((e=>[_(m,{width:"200"},{reference:k((()=>[b("span",{style:A({cursor:1==e.row.status?"pointer":"not-allowed"}),class:z(1==e.row.status?"purple":"grey")},"选择模版并下载",6)])),default:k((()=>[b("div",null,[(g(!0),f(C,null,V(ge.value,((l,s)=>(g(),f("div",P,[b("div",{class:"templateItem",onClick:t=>{return s=l.id,u=e.row.id,void(1==e.row.status?(se.value=!0,a({pitch_id:u,template_id:s}).then((e=>{se.value=!1,window.open(e.data),S.success("下载成功")}))):S.warning("任务还在进行中..."));var s,u}},[b("div",I,j(l.template_name),1)],8,F),_(o,null,{default:k((()=>[_(d,{onClick:e=>(e=>{re.value=!0,me.value="edit",fe.value=e.id,he.value=e.template_name;try{ve.value=[];const l=JSON.parse(e.custom_json);pe.value.forEach(((e,a)=>{if(e.checked=!1,be.value[a]=[],e.detail){const t=e.detail.filter((e=>void 0!==l[e.value]));be.value[a]=t.map((e=>e.value)),t.length===e.detail.length?e.checked=!0:0===t.length?e.checked=!1:e.checked="-",t.forEach((l=>{ve.value.push({...l,parent:e.name?e.name+" / ":"",checked:!0})}))}}))}catch(l){S.error("加载模板数据失败")}})(l)},null,8,["onClick"])])),_:2},1024),_(r,{title:"确定要删除该自定义设置吗？",onConfirm:e=>{return a=l.id,void t({template_id:a}).then((e=>{S.success("删除成功"),Se()}));var a}},{reference:k((()=>[_(o,{class:"ml-2"},{default:k((()=>[_(c)])),_:1})])),_:2},1032,["onConfirm"])])))),256)),b("div",Q,[b("div",{class:"cursor fz12 add-template",onClick:Ee},[u[7]||(u[7]=b("span",{class:"purple"},"添加模板",-1)),_(o,{class:"purple"},{default:k((()=>[_(v)])),_:1})])])])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[Qe,se.value]]),b("div",R,[_(l,{onHandleSizeChange:Ve,onHandleCurrentChange:je,total:ie.value,currentPage:ae.value.page,"page-size":ae.value.page_size,layout:"total, next, prev, pager,jumper"},null,8,["total","currentPage","page-size"])])])),[[Qe,se.value]]):(g(),w(te,{key:1,description:"暂无数据"}))]),b("span",T,[b("div",B,[_(i,{onClick:we},{default:k((()=>u[8]||(u[8]=[x("取 消")]))),_:1}),_(i,{type:"primary",onClick:xe},{default:k((()=>u[9]||(u[9]=[x("确 定")]))),_:1})])])])),_:1},8,["modelValue"]),_(ne,{title:"提报任务名称",modelValue:ce.value,"onUpdate:modelValue":u[3]||(u[3]=e=>ce.value=e),width:"50%"},{default:k((()=>[b("div",D,[u[10]||(u[10]=b("span",{class:"mr-2"}," 提报任务名称",-1)),_(de,{type:"text",modelValue:_e.value,"onUpdate:modelValue":u[1]||(u[1]=e=>_e.value=e),style:{width:"300px"},placeholder:"请输入提报任务名称"},null,8,["modelValue"])]),b("span",J,[b("div",M,[_(i,{onClick:u[2]||(u[2]=e=>ce.value=!1)},{default:k((()=>u[11]||(u[11]=[x("取 消")]))),_:1}),_(i,{type:"primary",onClick:Oe},{default:k((()=>u[12]||(u[12]=[x("确 定")]))),_:1})])])])),_:1},8,["modelValue"]),_(ne,{title:"自定义设置",top:"5vh","close-on-click-modal":!1,"append-to-body":!0,modelValue:re.value,"onUpdate:modelValue":u[5]||(u[5]=e=>re.value=e),width:"60%"},{footer:k((()=>[b("span",$,[u[15]||(u[15]=b("span",{class:"template-name"},[b("span",{style:{color:"red"}},"*"),x("模版名称 ")],-1)),_(de,{class:"name-input",type:"text",placeholder:"请输入内容",modelValue:he.value,"onUpdate:modelValue":u[4]||(u[4]=e=>he.value=e),maxlength:"10","show-word-limit":""},null,8,["modelValue"])]),_(i,{onClick:Ae},{default:k((()=>u[16]||(u[16]=[x("取 消")]))),_:1}),_(i,{type:"primary",onClick:Ke},{default:k((()=>u[17]||(u[17]=[x("确 定")]))),_:1})])),default:k((()=>[y((g(),f("div",null,[b("div",N,[_(Ie,{gutter:20},{default:k((()=>[_(Pe,{span:16},{default:k((()=>[b("div",W,[(g(!0),f(C,null,V(pe.value,((e,l)=>(g(),f(C,{key:l},[b("div",X,[_(ke,{indeterminate:"-"===e.checked,modelValue:e.checked,"onUpdate:modelValue":l=>e.checked=l,onChange:e=>((e,l)=>{const a=pe.value[l];if(a&&a.detail){if(a.detail.forEach((l=>{l.checked=e})),e){be.value[l]=a.detail.map((e=>e.value));const e=a.name,t=a.detail.map((l=>({...l,parent:e?e+" / ":"",label:l.label,value:l.value,checked:!0})));ve.value=[...ve.value.filter((e=>!a.detail.some((l=>l.value===e.value)))),...t]}else{be.value[l]=[];const e=new Set(a.detail.map((e=>e.value)));ve.value=ve.value.filter((l=>!e.has(l.value)))}a.checked=e}})(e,l)},{default:k((()=>[x(j(e.name),1)])),_:2},1032,["indeterminate","modelValue","onUpdate:modelValue","onChange"])]),_(He,{modelValue:be.value[l],"onUpdate:modelValue":e=>be.value[l]=e,onChange:e=>((e,l)=>{const a=pe.value[l];a.detail.forEach((l=>{l.checked=e.includes(l.value)}));let t=new Set(a.detail.map((e=>e.value)));ve.value=ve.value.filter((e=>!t.has(e.value)));const s=a.name,u=a.detail.filter((l=>e.includes(l.value))).map((e=>({...e,parent:s?s+" / ":"",label:e.label,value:e.value,checked:!0})));ve.value=[...ve.value,...u],e.length===a.detail.length?a.checked=!0:0===e.length?a.checked=!1:a.checked="-"})(e,l)},{default:k((()=>[(g(!0),f(C,null,V(e.detail,(e=>(g(),w(ke,{key:e.value,class:"mt-1 mb-1",label:e.value},{default:k((()=>[b("div",q,[x(j(e.label)+" ",1),e.tips?(g(),w(Ue,{key:0,class:"box-item",effect:"dark",content:e.tips,placement:"top",trigger:"hover"},{default:k((()=>[_(o,{size:16,color:"#999"},{default:k((()=>[_(Ce)])),_:1})])),_:2},1032,["content"])):E("",!0)])])),_:2},1032,["label"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])],64)))),128))])])),_:1}),_(Pe,{span:8},{default:k((()=>[u[14]||(u[14]=b("div",{class:"title-header-right"},[b("span",{class:"px-15"},"已选项目")],-1)),b("div",null,[b("div",G,[b("div",L,[_(U(e),{list:ve.value,animation:"300","item-key":"item",options:{sort:!0},"drag-class":"pointer"},{item:k((({element:e})=>[b("div",Y,[b("div",Z,[u[13]||(u[13]=b("span",{style:{margin:"0 5px 0"}},[b("svg",{t:"1695635715941",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2925",width:"16",height:"16"},[b("path",{d:"M66.488889 211.781818h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485 0-27.397172-22.768485-49.648485-50.980202-49.648485H66.488889C38.341818 112.484848 15.508687 134.723232 15.508687 162.133333s22.833131 49.648485 50.980202 49.648485z m891.009293 248.242424H66.488889C38.277172 460.024242 15.508687 482.262626 15.508687 509.672727s22.768485 49.648485 50.980202 49.648485h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485-0.012929-27.410101-22.923636-49.648485-50.993131-49.648485z m0 351.63798H66.488889c-28.134141 0-50.980202 22.238384-50.980202 49.648485s22.833131 49.648485 50.980202 49.648485h891.022222c28.198788 0 50.980202-22.238384 50.980202-49.648485-0.012929-27.397172-22.781414-49.648485-50.993131-49.648485z m0 0","p-id":"2926",fill:"#cdcdcd"})])],-1)),b("span",null,j(e.parent?e.parent+e.label:e.label),1)]),b("span",null,[_(o,null,{default:k((()=>[_(Fe,{onClick:e=>{return l=s.index,void ve.value.splice(l,1);var l}},null,8,["onClick"])])),_:2},1024)])])])),_:1},8,["list"])])])])])),_:1})])),_:1})])])),[[Qe,oe.value]])])),_:1},8,["modelValue"])],64)}}},[["__scopeId","data-v-e4e40ced"]]);export{ee as default};
