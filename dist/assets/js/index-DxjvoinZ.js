import{s as e,H as a,r as l,a6 as r,a as i,c as s,o as t,b as n,h as o,y as p,w as u,aQ as d,d as m,aj as c,ab as b,Z as _,ai as f,_ as v,ak as y,K as g,E as k}from"./index-BE6Fh1xm.js";import{u as w}from"./useHandleData-Bxz7m2iB.js";import{I as h,u as j}from"./index-CGZHvcNq.js";import{_ as C}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-Dk8XGRxJ.js";import{T}from"./index-YtmDwW09.js";import{_ as x}from"./UserDrawer.vue_vue_type_script_setup_true_name_UserDrawer_lang-ZNZztpLf.js";import{h as L,a as I,g as q,j as N,d as P,r as D,e as H,B as $,c as A,f as F}from"./user-BL6MRcTt.js";import"./_plugin-vue_export-helper-GSmkUi5K.js";import"./index-C9PKVjyH.js";import"./sortable.esm-DeWNWKFU.js";import"./Imgs-xShYOrto.js";import"./upload-BAU8flRh.js";const O={class:"main-box"},R={class:"table-box"},U=e({name:"useTreeFilter"}),V=e({...U,setup(e){const U=a(),V=()=>{U.push("/proTable/useTreeFilter/detail/123456?params=detail-page")},B=l(),E=r({departmentId:"1"}),K=e=>{k.success("请注意查看请求参数变化 🤔"),B.value.pageable.pageNum=1,E.departmentId=e},Q=r([{type:"index",label:"#",width:80},{prop:"username",label:"用户姓名",width:120,search:{el:"input"}},{prop:"gender",label:"性别",width:120,sortable:!0,enum:L,search:{el:"select"},fieldNames:{label:"genderLabel",value:"genderValue"}},{prop:"idCard",label:"身份证号"},{prop:"email",label:"邮箱"},{prop:"address",label:"居住地址"},{prop:"status",label:"用户状态",width:120,sortable:!0,tag:!0,enum:I,search:{el:"select"},fieldNames:{label:"userLabel",value:"userStatus"}},{prop:"createTime",label:"创建时间",width:180},{prop:"operation",label:"操作",width:330,fixed:"right"}]),S=async()=>{g.confirm("确认导出用户数据?","温馨提示",{type:"warning"}).then((()=>{var e;return j(H,"用户列表",null==(e=B.value)?void 0:e.searchParam)}))},Z=l(null),z=()=>{var e,a;const l={title:"用户",tempApi:H,importApi:$,getTableList:null==(e=B.value)?void 0:e.getTableList};null==(a=Z.value)||a.acceptParams(l)},G=l(null),J=(e,a={})=>{var l,r;const i={title:e,isView:"查看"===e,row:{...a},api:"新增"===e?A:"编辑"===e?F:void 0,getTableList:null==(l=B.value)?void 0:l.getTableList};null==(r=G.value)||r.acceptParams(i)};return(e,a)=>{const l=i("el-button");return t(),s("div",O,[n(T,{label:"name",title:"部门列表(单选)","request-api":p(q),"default-value":E.departmentId,onChange:K},null,8,["request-api","default-value"]),o("div",R,[n(C,{ref_key:"proTable",ref:B,columns:Q,"request-api":p(N),"init-param":E,"search-col":{xs:1,sm:1,md:2,lg:3,xl:3}},{tableHeader:u((()=>[n(l,{type:"primary",icon:p(f),onClick:a[0]||(a[0]=e=>J("新增"))},{default:u((()=>a[1]||(a[1]=[m("新增用户")]))),_:1},8,["icon"]),n(l,{type:"primary",icon:p(v),onClick:z},{default:u((()=>a[2]||(a[2]=[m("批量添加用户")]))),_:1},8,["icon"]),n(l,{type:"primary",icon:p(y),onClick:S},{default:u((()=>a[3]||(a[3]=[m("导出用户数据")]))),_:1},8,["icon"]),n(l,{type:"primary",plain:"",onClick:V},{default:u((()=>a[4]||(a[4]=[m("To 平级详情页面")]))),_:1})])),operation:u((e=>[n(l,{type:"primary",link:"",icon:p(d),onClick:a=>J("查看",e.row)},{default:u((()=>a[5]||(a[5]=[m("查看")]))),_:2},1032,["icon","onClick"]),n(l,{type:"primary",link:"",icon:p(c),onClick:a=>J("编辑",e.row)},{default:u((()=>a[6]||(a[6]=[m("编辑")]))),_:2},1032,["icon","onClick"]),n(l,{type:"primary",link:"",icon:p(b),onClick:a=>(async e=>{var a;await w(D,{id:e.id},`重置【${e.username}】用户密码`),null==(a=B.value)||a.getTableList()})(e.row)},{default:u((()=>a[7]||(a[7]=[m("重置密码")]))),_:2},1032,["icon","onClick"]),n(l,{type:"primary",link:"",icon:p(_),onClick:a=>(async e=>{var a;await w(P,{id:[e.id]},`删除【${e.username}】用户`),null==(a=B.value)||a.getTableList()})(e.row)},{default:u((()=>a[8]||(a[8]=[m("删除")]))),_:2},1032,["icon","onClick"])])),_:1},8,["columns","request-api","init-param"]),n(x,{ref_key:"drawerRef",ref:G},null,512),n(h,{ref_key:"dialogRef",ref:Z},null,512)])])}}});export{V as default};
