import{s as e,a6 as l,r as a,a as p,g as t,o,w as r,b as m,h as c,c as u,F as d,i as s,m as n,d as _,v as i,E as f}from"./index-C2bfFjZ1.js";import{D as V}from"./business-D2QRQb4T.js";import{_ as g}from"./_plugin-vue_export-helper-BXFjo1rG.js";const b={class:"cpm_cpe"},v={class:"cpm_cpe"},h={class:"cpm_cpe"},j={class:"cpm_cpe"},x=e({name:"UserDrawer"}),y=g(e({...x,setup(e,{expose:g}){const x=l({project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],custom_name:[{required:!0,message:"请选择客户",trigger:"blur"}]}),y=a([]),w=a(!1),U=a({isView:!1,title:"",row:{}}),Y=l({project_name:"",custom_name:"",cpe_max_price:0,cpe_min_price:0,cpm_max_price:0,cpm_min_price:0,project_demand:"",project_budget:0}),C=a();return g({acceptParams:e=>{U.value=e,w.value=!0}}),(e,l)=>{const a=p("el-input"),g=p("el-form-item"),D=p("el-option"),k=p("el-select"),M=p("el-col"),P=p("el-row"),z=p("el-date-picker"),q=p("el-form"),S=p("el-button"),E=p("el-drawer");return o(),t(E,{modelValue:w.value,"onUpdate:modelValue":l[11]||(l[11]=e=>w.value=e),"destroy-on-close":!0,size:"450px",title:`${U.value.title}项目`},{footer:r((()=>[m(S,{onClick:l[9]||(l[9]=e=>w.value=!1)},{default:r((()=>l[20]||(l[20]=[_("取消")]))),_:1}),n(m(S,{type:"primary",onClick:l[10]||(l[10]=e=>(async e=>{e&&await e.validate(((e,l)=>{if(e){Y.project_create_time=y.value[0],Y.project_end_time=y.value[1];let e=JSON.parse(JSON.stringify(Y));V(e).then((e=>{990==e.code&&(f.success("添加成功！"),projectAddDrawer.value=!1,getProjectList(),Y.project_name="",Y.custom_name="",Y.cpe_max_price=0,Y.cpe_min_price=0,Y.cpm_max_price=0,Y.cpm_min_price=0,Y.project_demand=0,Y.project_budget=0,y.value=[],Y.custom_id="",Y.usc_code="",Y.cust_abbr="")}))}}))})(C.value))},{default:r((()=>l[21]||(l[21]=[_("创建项目")]))),_:1},512),[[i,!U.value.isView]])])),default:r((()=>[m(q,{model:Y,rules:x,ref_key:"ruleForm",ref:C,"label-width":"auto",style:{"max-width":"700px",height:"100%",display:"flex","flex-direction":"column","justify-content":"space-between"},class:"form-style"},{default:r((()=>[l[16]||(l[16]=c("span",{class:"form-span sle"},"基本信息",-1)),m(g,{label:"项目名称",prop:"project_name"},{default:r((()=>[m(a,{modelValue:Y.project_name,"onUpdate:modelValue":l[0]||(l[0]=e=>Y.project_name=e),placeholder:"请输入项目名称"},null,8,["modelValue"])])),_:1}),l[17]||(l[17]=c("span",{class:"form-span"},"客户信息",-1)),m(g,{label:"客户名称",prop:"custom_name"},{default:r((()=>[m(k,{modelValue:Y.custom_name,"onUpdate:modelValue":l[1]||(l[1]=e=>Y.custom_name=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请搜索客户简称/全称/集团名称","remote-method":e.remoteMethod,loading:e.loading2,onChange:e.handleSelectChange},{default:r((()=>[(o(!0),u(d,null,s(e.options,(e=>(o(),t(D,{key:e.custom_id,label:e.custom_name,value:e.custom_name},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading","onChange"])])),_:1}),l[18]||(l[18]=c("span",{class:"form-span"},"考核要求",-1)),m(g,{label:"KPI要求",required:""},{default:r((()=>[m(P,{gutter:0},{default:r((()=>[l[12]||(l[12]=c("div",{class:"cpmcpe"},[c("div",null,"CPM"),c("div",null,"千次播放成本")],-1)),m(M,{span:6},{default:r((()=>[c("span",b,[m(g,{prop:"cpm_min_price"},{default:r((()=>[m(a,{type:"number",modelValue:Y.cpm_min_price,"onUpdate:modelValue":l[2]||(l[2]=e=>Y.cpm_min_price=e),placeholder:"0",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])])),_:1}),l[13]||(l[13]=c("span",null,"-",-1)),m(M,{span:6},{default:r((()=>[c("span",v,[m(g,{prop:"cpm_max_price"},{default:r((()=>[m(a,{type:"number",modelValue:Y.cpm_max_price,"onUpdate:modelValue":l[3]||(l[3]=e=>Y.cpm_max_price=e),placeholder:"0"},null,8,["modelValue"])])),_:1})])])),_:1})])),_:1}),m(P,{gutter:0},{default:r((()=>[l[14]||(l[14]=c("span",{class:"cpmcpe"},[c("div",null,"CPE"),c("div",null,"单次互动成本")],-1)),m(M,{span:6},{default:r((()=>[c("span",h,[m(g,{prop:"cpe_min_price"},{default:r((()=>[m(a,{type:"number",modelValue:Y.cpe_min_price,"onUpdate:modelValue":l[4]||(l[4]=e=>Y.cpe_min_price=e),placeholder:"0"},null,8,["modelValue"])])),_:1})])])),_:1}),l[15]||(l[15]=c("span",null,"-",-1)),m(M,{span:6},{default:r((()=>[c("span",j,[m(g,{prop:"cpe_max_price"},{default:r((()=>[m(a,{type:"number",modelValue:Y.cpe_max_price,"onUpdate:modelValue":l[5]||(l[5]=e=>Y.cpe_max_price=e),placeholder:"0"},null,8,["modelValue"])])),_:1})])])),_:1})])),_:1})])),_:1}),l[19]||(l[19]=c("span",{class:"form-span"},"项目要求",-1)),m(g,{label:"项目要求"},{default:r((()=>[m(a,{modelValue:Y.project_demand,"onUpdate:modelValue":l[6]||(l[6]=e=>Y.project_demand=e),placeholder:"请输入项目要求"},null,8,["modelValue"])])),_:1}),m(g,{label:"项目总预算",prop:"project_budget"},{default:r((()=>[m(a,{type:"number",modelValue:Y.project_budget,"onUpdate:modelValue":l[7]||(l[7]=e=>Y.project_budget=e),placeholder:"请输入项目总预算"},null,8,["modelValue"])])),_:1}),m(g,{label:"项目周期"},{default:r((()=>[m(z,{modelValue:y.value,"onUpdate:modelValue":l[8]||(l[8]=e=>y.value=e),format:"YYYY-MM-DD",align:"right","value-format":"YYYY-MM-DD",type:"daterange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",size:e.size},null,8,["modelValue","size"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-c9dcdfaf"]]);export{y as default};
