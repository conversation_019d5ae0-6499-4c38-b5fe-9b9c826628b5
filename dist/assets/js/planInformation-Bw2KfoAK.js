import{s as e,r as a,a6 as t,f as i,p as d,q as l,a as n,c as r,o as s,h as o,b as m,w as p,y as g,aO as _,F as u,i as c,g as v,t as f,d as y,n as b,m as h,z as S,$ as w,v as V,E as D}from"./index-BE6Fh1xm.js";import O from"./time-ChyUUY53.js";import k from"./target-CXco6UTx.js";import{g as x}from"./temp-api-B36KNeYM.js";import{_ as J}from"./_plugin-vue_export-helper-GSmkUi5K.js";import"./setupRequest-C4Opp8Oc.js";const N={class:"plan-information"},j={id:"marketing",class:"content-section",ref:"marketingRef"},I={class:"section-content"},T={class:"info-box"},U={id:"basic-info",class:"content-section",ref:"planInfoRef"},A={class:"section-content",style:{width:"50%"}},R={class:"advertiser-option"},B={class:"advertiser-name"},Y={class:"advertiser-id"},z={class:"setting-content"},F={id:"bidding-strategy",class:"content-section",ref:"targetRef"},M={class:"section-content"},$=J(e({__name:"planInformation",props:{campaignData:{}},emits:["update:campaignData"],setup(e,{expose:J,emit:$}){const q=e,C=a(null),E=a(null),P=a(!1),G=a({mon:"000000000000000000000000",tues:"000000000000000000000000",wed:"000000000000000000000000",thur:"000000000000000000000000",fri:"000000000000000000000000",sat:"000000000000000000000000",sun:"000000000000000000000000"}),H=t({advertiser_id:"",campaign_name:"",enable:1,promotion_target:1,dateType:"0",date:[],time_period:{mon:"",tues:"",wed:"",thur:"",fri:"",sat:"",sun:""},placement:1,marketing_target:4,time_type:0,start_time:"",expire_time:"",bidding_strategies:[]}),K=a(!1),L=a([]),Q=a(!1),W=async(e="")=>{Q.value=!0;try{const a={key_word:e,is_owner:!0},t=await x(a);t&&t.data&&t.data.list?L.value=t.data.list:L.value=[]}catch(a){D.error("获取广告主账号失败"),L.value=[]}finally{Q.value=!1}},X=e=>{W(e||"")};i([()=>L.value.length,()=>q.campaignData.campaign.advertiser_id],(([e,a])=>{if(e>0&&a){const e=a.toString();L.value.find((a=>a.advertiser_id===e));H.advertiser_id=e}}),{immediate:!0});const Z=()=>{if(q.campaignData&&q.campaignData.campaign){const a=q.campaignData.campaign;H.advertiser_id=a.advertiser_id||"",H.campaign_name=a.campaign_name||"",H.enable=void 0===a.enable?1:a.enable,H.placement=a.placement||1,H.marketing_target=a.marketing_target||4,H.promotion_target=a.promotion_target||1,H.time_type=void 0!==a.time_type?a.time_type:0,H.dateType=void 0!==a.time_type?String(a.time_type):"0",1===a.time_type&&a.start_time&&a.expire_time&&(H.date=[a.start_time,a.expire_time]),a.time_period&&((e=a.time_period)&&!Object.values(e).every((e=>"000000000000000000000000"===e||""===e)))?(H.time_period=a.time_period,G.value=a.time_period,P.value=!0):(H.time_period={mon:"000000000000000000000000",tues:"000000000000000000000000",wed:"000000000000000000000000",thur:"000000000000000000000000",fri:"000000000000000000000000",sat:"000000000000000000000000",sun:"000000000000000000000000"},G.value=H.time_period,P.value=!1),Array.isArray(a.bidding_strategies)&&a.bidding_strategies.length>0?H.bidding_strategies=JSON.parse(JSON.stringify(a.bidding_strategies)).map((e=>({...e,campaign_day_budget:e.campaign_day_budget?e.campaign_day_budget/100:0}))):H.bidding_strategies=[{bidding_strategy:a.bidding_strategy||3,limit_day_budget:a.limit_day_budget||1,campaign_day_budget:a.campaign_day_budget?a.campaign_day_budget/100:0,time_period_type:a.time_period_type||0,optimize_target:a.optimize_target||0,constraint_type:void 0===a.constraint_type?101:a.constraint_type,smart_switch:a.smart_switch||0,search_flag:a.search_flag||0,pacing_mode:a.pacing_mode||1,intelligent_expansion:a.intelligent_expansion||0}]}var e},ee=()=>{P.value=!P.value},ae=e=>{if(Object.values(e).every((e=>"111111111111111111111111"===e||"000000000000000000000000"===e||""===e))){const e={mon:"000000000000000000000000",tues:"000000000000000000000000",wed:"000000000000000000000000",thur:"000000000000000000000000",fri:"000000000000000000000000",sat:"000000000000000000000000",sun:"000000000000000000000000"};G.value=e,H.time_period=e}else G.value=e,H.time_period=e},te=e=>e.getTime()<Date.now()-864e5,ie=e=>{K.value&&JSON.stringify(H.bidding_strategies)!==JSON.stringify(e)&&(H.bidding_strategies=JSON.parse(JSON.stringify(e)))};return i((()=>q.campaignData),(e=>{if(!K.value)return void Z();JSON.stringify((null==e?void 0:e.campaign)||{})!==JSON.stringify(H)&&Z()}),{deep:!0,immediate:!0}),d((async()=>{await W(),Z(),l((()=>{K.value=!0}))})),J({validate:async()=>{if(!C.value)return!1;try{if(await C.value.validate(),E.value){if(!(await E.value.validate()))return!1;if(E.value.getBiddingStrategies){const e=E.value.getBiddingStrategies();Array.isArray(e)&&e.length>0&&(H.bidding_strategies=JSON.parse(JSON.stringify(e)))}}return!0}catch(e){return!1}},getFormData:()=>{var e,a,t;let i=H.bidding_strategies;if(null==(e=E.value)?void 0:e.getBiddingStrategies){const e=E.value.getBiddingStrategies();Array.isArray(e)&&e.length>0&&(i=JSON.parse(JSON.stringify(e)).map((e=>({...e,campaign_day_budget:Math.round(100*e.campaign_day_budget)}))))}const d={plan:{advertiser_id:H.advertiser_id,campaign_name:H.campaign_name,enable:H.enable,placement:H.placement,marketing_target:H.marketing_target,promotion_target:H.promotion_target,time_type:parseInt(H.dateType),bidding_strategies:i}};if(Object.values(H.time_period).every((e=>"000000000000000000000000"===e||""===e))||(d.plan.time_period=H.time_period),"1"===H.dateType&&2===(null==(a=H.date)?void 0:a.length)&&(d.plan.start_time=H.date[0],d.plan.expire_time=H.date[1]),null==(t=q.campaignData)?void 0:t.campaign){const e=q.campaignData.campaign;for(const a in e)void 0===d.plan[a]&&"bidding_strategies"!==a&&"time_period"!==a&&(d.plan[a]=e[a])}return d}}),(e,a)=>{const t=n("el-icon"),i=n("el-option"),d=n("el-select"),l=n("el-form-item"),D=n("el-input"),x=n("el-switch"),J=n("InfoFilled"),$=n("el-tooltip"),K=n("el-button"),W=n("el-radio"),Z=n("el-radio-group"),de=n("el-date-picker"),le=n("el-form");return s(),r("div",N,[o("div",j,[a[7]||(a[7]=o("div",{class:"section-title"},"营销诉求",-1)),o("div",I,[o("div",T,[m(t,null,{default:p((()=>[m(g(_))])),_:1}),a[6]||(a[6]=o("div",{class:"info-content"},[o("div",{class:"info-title"},"产品种草")],-1))])])],512),o("div",U,[a[13]||(a[13]=o("div",{class:"section-title"},"计划基本信息",-1)),o("div",A,[m(le,{model:H,"label-width":"120px",ref_key:"formRef",ref:C},{default:p((()=>[m(l,{label:"广告主账号信息"},{default:p((()=>[m(d,{modelValue:H.advertiser_id,"onUpdate:modelValue":a[0]||(a[0]=e=>H.advertiser_id=e),filterable:"",remote:"","remote-method":X,placeholder:"请输入账户昵称或ID搜索",loading:Q.value,style:{width:"100%"},"value-key":"advertiser_id",disabled:""},{default:p((()=>[(s(!0),r(u,null,c(L.value,(e=>(s(),v(i,{key:e.advertiser_id,label:`${e.advertiser_name} (ID: ${e.advertiser_id})`,value:e.advertiser_id},{default:p((()=>[o("div",R,[o("div",B,f(e.advertiser_name),1),o("div",Y,"ID: "+f(e.advertiser_id),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),m(l,{label:"产品名称"},{default:p((()=>[m(D,{modelValue:H.campaign_name,"onUpdate:modelValue":a[1]||(a[1]=e=>H.campaign_name=e),placeholder:"请输入产品名称"},null,8,["modelValue"])])),_:1}),m(l,{label:"默认状态"},{default:p((()=>[m(x,{modelValue:H.enable,"onUpdate:modelValue":a[2]||(a[2]=e=>H.enable=e),"active-value":1,"inactive-value":0},null,8,["modelValue"]),m($,{content:"计划创建后的默认关闭/开启状态",placement:"top"},{default:p((()=>[m(t,{style:{"margin-left":"5px",color:"#555"}},{default:p((()=>[m(J)])),_:1})])),_:1})])),_:1}),m(l,{label:"广告类型"},{default:p((()=>[m(K,{type:"primary"},{default:p((()=>a[8]||(a[8]=[y("信息流推广")]))),_:1})])),_:1}),m(l,{label:"推广标的"},{default:p((()=>[m(Z,{modelValue:H.promotion_target,"onUpdate:modelValue":a[3]||(a[3]=e=>H.promotion_target=e)},{default:p((()=>[m(W,{label:1},{default:p((()=>a[9]||(a[9]=[y("笔记")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),m(l,{label:"投放日期"},{default:p((()=>[m(Z,{modelValue:H.dateType,"onUpdate:modelValue":a[4]||(a[4]=e=>H.dateType=e)},{default:p((()=>[m(W,{label:"0"},{default:p((()=>a[10]||(a[10]=[y("长期投放")]))),_:1}),m(W,{label:"1"},{default:p((()=>a[11]||(a[11]=[y("自定义")]))),_:1})])),_:1},8,["modelValue"]),"1"===H.dateType?(s(),v(de,{key:0,style:{"margin-left":"10px"},modelValue:H.date,"onUpdate:modelValue":a[5]||(a[5]=e=>H.date=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD","disabled-date":te},null,8,["modelValue"])):b("",!0)])),_:1}),m(l,{label:"高级设置"},{default:p((()=>[o("div",{class:"setting-header",onClick:ee},[a[12]||(a[12]=o("span",null,"展示投放时间段等设置",-1)),m(t,{class:S({"is-active":P.value})},{default:p((()=>[m(g(w))])),_:1},8,["class"])]),h(o("div",z,[m(O,{"time-period":G.value,"onUpdate:timePeriod":ae},null,8,["time-period"])],512),[[V,P.value]])])),_:1})])),_:1},8,["model"])])],512),o("div",F,[a[14]||(a[14]=o("div",{class:"section-title"},"目标和出价方式",-1)),o("div",M,[m(k,{ref_key:"targetComponentRef",ref:E,"target-data":q.campaignData.campaign,"bidding-strategies":H.bidding_strategies||[],"is-edit":!0,"onUpdate:biddingStrategies":ie},null,8,["target-data","bidding-strategies"])])],512)])}}}),[["__scopeId","data-v-5120aa5a"]]);export{$ as default};
