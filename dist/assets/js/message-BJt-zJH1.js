import{D as e,r as s,k as a}from"./index-C2bfFjZ1.js";const l="/assets/png/msg01-CVG7czTN.png",t=e("messageStore",(()=>{const e=s([]),a=s([]),l=s(1),t=s(void 0);return{messageList:e,unreadMessage:t,fillMessageList:(s,t)=>{1===t&&(a.value=s,l.value=1,e.value.length&&2===l.value?e.value=[...s,e.value].slice(0,5):e.value=s.slice(0,5))},updateUnreadMessage:e=>{t.value=(e||0)<=99?e:"99+"}}})),u=e=>a.get("/bulletinList",e),g=e=>a.get("/bulletinRead",e),i=e=>a.post("/operateBulletin",e);export{l as _,g as a,u as g,i as o,t as u};
