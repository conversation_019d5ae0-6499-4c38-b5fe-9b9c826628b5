import{s as e,r as a,a as s,c as l,o,h as t,b as n}from"./index-BE6Fh1xm.js";import{_ as u}from"./_plugin-vue_export-helper-GSmkUi5K.js";const r={class:"card content-box"},c=e({name:"menu1"}),p=u(e({...c,setup(e){const u=a("");return(e,a)=>{const c=s("el-input");return o(),l("div",r,[a[1]||(a[1]=t("span",{class:"text"},"我是menu1 🍓🍇🍈🍉",-1)),n(c,{modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),placeholder:"测试缓存"},null,8,["modelValue"])])}}}),[["__scopeId","data-v-cce5338a"]]);export{p as default};
