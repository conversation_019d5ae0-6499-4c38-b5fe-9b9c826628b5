import{u as e,H as l,r as a,p as t,f as o,a as i,c as r,o as u,h as n,b as d,w as s,d as p,m as v,y as c,F as _,i as m,g as f,v as y,t as k,n as b,E as g,q as h}from"./index-C2bfFjZ1.js";import{r as w,y as x,m as j,a as V,o as C,p as q,z as S,B as z,q as U}from"./business-D2QRQb4T.js";import I from"./addKolDialog-Dw79baty.js";import L from"./submitReport-DP2c2Bbs.js";import"./_plugin-vue_export-helper-BXFjo1rG.js";import"./vuedraggable.umd-DGOfhQCR.js";import"./index.vue_vue_type_script_setup_true_lang-Ds10gtGP.js";const A={id:"star-layout-body-content"},R={id:"layout-content"},D={id:"publish-reconstruct"},K={class:"module-primary-step-box"},F={class:"module-primary-box"},J={class:"publish-reconstruct module-primary-group"},N={id:"project",class:"module-primary"},O={class:"module-primary-legend flex justify-between"},B={class:"module-primary-action"},E={class:"module-secondary-group"},T={class:"project-selector justify-between flex items-center"},H={class:"project-filter-list"},M={class:"project-filter-item"},X={key:0,class:"project-filter-item-content"},G={key:1},P={class:"project-filter-item flex items-center mt-1"},Q={key:0,class:"project-filter-item-content"},W={key:1},Y={class:"project-item"},Z={class:"project-info"},$={class:"text-sm text-gray-500 mt-1"},ee={class:"mt-1"},le={id:"category",class:"module-primary"},ae={class:"module-primary-legend flex justify-between"},te={class:"module-secondary-group"},oe={class:"font-semibold"},ie={style:{"min-width":"1050px"}},re={class:"search-div-photo flex items-center"},ue=["src"],ne={class:"ml-4 font-semibold text-15"},de={class:"cursor"},se=["href"],pe={class:"text-xs text-slate-400"},ve={class:"flex items-center justify-start"},ce={class:"w-32 cursor overlap"},_e={class:"colorBlock"},me={class:"text-xs text-slate-400 mt-2"},fe={class:"text-sm"},ye={class:"text-xs text-slate-400 mt-2"},ke={id:"category",class:"module-primary"},be={class:"module-secondary-group"},ge={class:"project-selector"},he={class:"project-filter-list"},we={class:"project-filter-item"},xe={class:"project-filter-item-content"},je={class:"view-footer"},Ve={class:"footer-action"},Ce={__name:"selectAuthor",setup(Ce){const qe=e(),Se=l();a([]);const ze=a(""),Ue=a(""),Ie=a(""),Le=a(""),Ae=a({}),Re=a(null);a([]),a([]),a("");const De=a(),Ke=a(""),Fe=a(""),Je=a(""),Ne=a(!1),Oe=a(!1),Be=a(!1),Ee=a(!1),Te=a([]),He=a({project_id:"",task_name:"",promotion_platforms_genres:1,task_type:1,settlement_method:1}),Me={1:"完成",2:"采集中",3:"采集失败"},Xe={1:"指派",2:"招募",3:"投稿"},Ge=a([]),Pe=a([]),Qe=a([]),We=a([]),Ye=[{label:"1-20S视频",value:1},{label:"21-60S视频",value:2},{label:"60S以上视频",value:71}],Ze=()=>{var e,l;Ue.value&&ze.value?Ae.value&&(null==(e=Ae.value)?void 0:e.project.id)&&(null==(l=Ae.value)?void 0:l.task.id)&&(Be.value=!0):g({message:"请先选择项目或任务",type:"warning"})},$e=()=>{We.value.length>0?Ee.value=!0:g({message:"请选择要提报的达人",type:"warning"})},el=e=>{1==e.isXTradio?(e.kol_arr.map((e=>{let l=!0;Te.value&&Te.value.length>0&&Te.value.map((a=>{e.platform_uid==a.platform_uid&&e.cooperation_type==a.cooperation_type&&(l=!1)})),l?(Te.value||(Te.value=[]),Te.value.push({kol_name:e.kol_name,platform_uid:e.platform_uid,kol_type:e.kol_type,kol_price:e.kol_price,cooperation_type:e.cooperation_type,kol_num:e.kol_num,kol_photo:e.kol_photo,price_1_20:e.price_1_20,price_20_60:e.price_20_60,price_60:e.price_60,rebate_ratio:e.rebate_ratio,collection_status:1}),w({platform_uid:e.platform_uid}).then((l=>{Te.value.map((a=>{a.platform_uid==e.platform_uid&&(a.history_point_lists=l.data,Re.value&&Re.value.toggleRowSelection(a,void 0))}))}))):g({message:"该达人已存在",type:"warning"})})),Be.value=!1):(e.kol_arr.map((e=>{let l=!0;Te.value&&Te.value.length>0&&Te.value.map((a=>{e.platform_uid==a.platform_uid&&e.cooperation_type==a.cooperation_type&&(l=!1)})),l?(Te.value||(Te.value=[]),Te.value.push({platform_uid:e.platform_uid,kol_name:"",kol_type:1,kol_price:0,cooperation_type:1,kol_num:1,kol_photo:"",price_1_20:0,price_20_60:0,price_60:0,rebate_ratio:0,collection_status:2}),ll(e.platform_uid,e.cooperation_type,!0)):g({message:"该达人已存在",type:"warning"})})),Te.value.length>0&&Te.value.map((l=>{e.kol_arr.map((e=>{e.platform_uid==l.platform_uid&&e.cooperation_type==l.cooperation_type&&Re.value&&Re.value.toggleRowSelection(l,void 0)}))})),Te.value.map((l=>{e.kol_arr.map((e=>{e==l.platform_uid&&Re.value.toggleRowSelection(l,void 0)}))}))),Be.value=!1},ll=(e,l,a)=>{U({platform_uid:e,task_id:Ue.value,project_id:ze.value,cooperation_type:l}).then((e=>{2!=e.data.collection_status&&(clearInterval(De.value),De.value=null),Te.value.map((l=>{l.platform_uid==e.data.platform_uid&&(l.collection_status=e.data.collection_status,l.collection_msg=e.data.collection_msg,l.price_1_20=e.data.price_1_20,l.price_20_60=e.data.price_1_20,l.price_60=e.data.price_60,l.kol_name=e.data.kol_name,l.kol_photo=e.data.kol_photo,l.process_kol_id=e.data.process_kol_id,1==l.cooperation_type?l.kol_price=l.price_1_20*l.kol_num:2==l.cooperation_type?l.kol_price=l.price_20_60*l.kol_num:71==l.cooperation_type&&(l.kol_price=l.price_60*l.kol_num))})),a&&g({message:"正在采集中",type:"success"})}))},al=()=>{Oe.value=!0},tl=e=>{We.value=e},ol=()=>{Ee.value=!1,Be.value=!1},il=e=>{e?(Ne.value=!1,x({project_name:e}).then((e=>{Ge.value=e.data.lists}))):Ge.value=[]},rl=()=>{Oe.value=!1},ul=()=>{Qe.value.map((e=>{e.id==Je.value&&(Fe.value=e.name)}))},nl=()=>{Ge.value.map((e=>{e.project_id==ze.value&&(Le.value=e.project_name)})),(null==ze?void 0:ze.value)&&dl()},dl=()=>{z({project_id:ze.value}).then((e=>{Pe.value=e.data?e.data:[]}))},sl=()=>{Pe.value.map((e=>{e.id==Ue.value&&(Ie.value=e.task_name,vl(Ue.value))}))},pl=e=>{e?h((()=>{e.forEach(((e,l)=>{Te.value.map((l=>{l.platform_uid==e.platform_uid&&l.cooperation_type==e.cooperation_type&&Re.value.toggleRowSelection(e,void 0)}))}))})):Re.value.clearSelection()},vl=e=>{V({task_id:e}).then((e=>{if(null==e.data)return g({message:e.msg,type:"error"}),closeCurrentTab(),void Se.push({path:"/business/task",query:{key:"4-3",task_id:qe.query.id}});if(Ae.value=e.data,"add"==qe.query.page_type||"info"==qe.query.page_type||"edit"==qe.query.page_type)Te.value=e.data.kols;else{let l=[];Te.value.map((a=>{l=e.data.kols.filter((e=>a.platform_uid!=e.platform_uid||a.platform_uid==e.platform_uid&&a.cooperation_type!=e.cooperation_type))})),Te.value=Te.value.concat(...l)}(e.data.process_before.alliance_personnel||e.data.process_before.alliance_personnel_id)&&(Fe.value=e.data.process_before.alliance_personnel,Je.value=e.data.process_before.alliance_personnel_id),e.data.task&&(Ue.value=e.data.task.id,Ie.value=e.data.task.task_name,ze.value=e.data.task.project_id),pl(Te.value)}))},cl=()=>{Se.go(-1)},_l=e=>{let l=[],a=!0;if(!Ue.value)return void g.error("请选择任务！");if(2==e&&0==We.value.length)return void g.error("提交请选择达人！");if(!(2!=e||Fe.value&&Je.value))return void g.error("提交请选择建联信息！");We.value.map((t=>{if(2==e&&1!=t.collection_status)return a=!1,void g.error("达人信息采集中/采集失败，无法提交！");l.map((e=>{if(t.platform_uid==e.platform_uid&&t.cooperation_type==e.cooperation_type)return a=!1,void g.error("提交有重复达人请检查！")})),l.push({kol_name:t.kol_name,platform_uid:t.platform_uid,cooperation_type:t.cooperation_type,kol_price:t.kol_price?t.kol_price:0,rebate_ratio:t.rebate_ratio?t.rebate_ratio:0,kol_num:t.kol_num,kol_photo:t.kol_photo,price_1_20:t.price_1_20,price_20_60:t.price_20_60,price_60:t.price_60,process_kol_id:t.process_kol_id})}));let t={button_type:e,status:2==e?1:0,project_id:ze.value,task_id:Ue.value,task_name:Ie.value,alliance_personnel_id:Je.value?Je.value:void 0,alliance_personnel:Fe.value?Fe.value:void 0};l.length>0&&(t.kols=l),a&&q(t).then((l=>{990==l.code&&(1==e?(g.success("保存成功！"),Se.push({path:"/business/task",query:{key:"4-3",status:0}})):(g.success("提交成功！"),Se.push({path:"/business/task",query:{key:"4-3",status:1}})))}))};return t((()=>{j({role_id:94}).then((e=>{Qe.value=e.data})),qe.query.id?vl(qe.query.id):(Te.value=JSON.parse(localStorage.getItem("kolList"))?JSON.parse(localStorage.getItem("kolList")):[],pl(Te.value),Te.value.map((e=>{e.collection_status=1,w({platform_uid:e.platform_uid}).then((l=>{e.history_point_lists=l.data}))}))),qe.query.page_type&&(Ke.value=qe.query.page_type),x({}).then((e=>{Ge.value=e.data.lists}))})),o((()=>{var e;return null==(e=Te.value)?void 0:e.length}),(()=>{if(Te.value){Te.value.filter((e=>2==e.collection_status)).map((e=>{De.value=setInterval((()=>{ll(e.platform_uid,e.cooperation_type)}),3e4)}))}})),(e,l)=>{var a,t,o,h,w,x,j,V,q,z,U,Ce,Se,Le,De,Fe,ll,pl,vl,ml,fl,yl,kl,bl,gl,hl,wl;const xl=i("el-step"),jl=i("el-steps"),Vl=i("el-button"),Cl=i("el-option"),ql=i("el-select"),Sl=i("el-input"),zl=i("el-tag"),Ul=i("el-table-column"),Il=i("el-input-number"),Ll=i("More"),Al=i("el-icon"),Rl=i("el-tooltip"),Dl=i("el-table"),Kl=i("el-popover"),Fl=i("el-popconfirm"),Jl=i("el-form-item"),Nl=i("el-form"),Ol=i("el-dialog");return u(),r("div",A,[n("div",R,[n("div",D,[n("div",K,[d(jl,{style:{margin:"0 auto"},active:1,class:"module-primary-step","align-center":""},{default:s((()=>[d(xl,{description:"任务和达人选择"}),d(xl,{description:"达人建联"}),d(xl,{description:"下单审核信息填写"}),d(xl,{description:"达人审核"})])),_:1})]),n("div",F,[n("div",J,[n("div",N,[n("legend",O,[l[11]||(l[11]=n("span",null,"选择任务",-1)),n("div",B,[d(Vl,{onClick:al,disabled:"info"==Ke.value,type:"primary"},{default:s((()=>l[10]||(l[10]=[p(" 创建新任务 ")]))),_:1},8,["disabled"])])]),n("div",E,[n("div",T,[n("div",H,[n("div",M,[l[12]||(l[12]=n("div",{class:"project-filter-item-title"},"项目名称",-1)),"talentList"==(null==(t=null==(a=c(qe))?void 0:a.query)?void 0:t.path)?(u(),r("div",X,[d(ql,{style:{width:"200px"},modelValue:ze.value,"onUpdate:modelValue":l[0]||(l[0]=e=>ze.value=e),onChange:nl,filterable:"",remote:"","reserve-keyword":"",placeholder:"请选择项目名称","remote-method":il,loading:Ne.value,disabled:"talentList"!==(null==(h=null==(o=c(qe))?void 0:o.query)?void 0:h.path)},{default:s((()=>[(u(!0),r(_,null,m(Ge.value,(e=>(u(),f(Cl,{key:e.id,label:e.project_name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading","disabled"])])):(u(),r("div",G,[d(Sl,{type:"text",value:null==(x=null==(w=Ae.value)?void 0:w.task)?void 0:x.project_name,disabled:""},null,8,["value"])]))]),n("div",P,[l[13]||(l[13]=n("div",{class:"project-filter-item-title"},"任务名称",-1)),"talentList"==(null==(V=null==(j=c(qe))?void 0:j.query)?void 0:V.path)?(u(),r("div",Q,[d(ql,{style:{width:"200px"},modelValue:Ue.value,"onUpdate:modelValue":l[1]||(l[1]=e=>Ue.value=e),onChange:sl,placeholder:"请选择任务名称",disabled:"talentList"!==(null==(z=null==(q=c(qe))?void 0:q.query)?void 0:z.path)},{default:s((()=>[(u(!0),r(_,null,m(Pe.value,(e=>(u(),f(Cl,{key:null==e?void 0:e.id,label:null==e?void 0:e.task_name,value:null==e?void 0:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])):(u(),r("div",W,[d(Sl,{type:"text",value:null==(Ce=null==(U=Ae.value)?void 0:U.task)?void 0:Ce.task_name,disabled:""},null,8,["value"])]))])]),v(n("div",Y,[n("div",Z,[n("p",null,k((null==(Le=null==(Se=Ae.value)?void 0:Se.task)?void 0:Le.task_name)?null==(Fe=null==(De=Ae.value)?void 0:De.task)?void 0:Fe.task_name:Ie.value),1),n("p",$,[p(" 任务ID:"+k((null==(pl=null==(ll=Ae.value)?void 0:ll.task)?void 0:pl.id)?null==(ml=null==(vl=Ae.value)?void 0:vl.task)?void 0:ml.id:Ue.value)+" ",1),v(n("span",null,"创建时间:"+k(null==(yl=null==(fl=Ae.value)?void 0:fl.task)?void 0:yl.created_at),513),[[y,null==(bl=null==(kl=Ae.value)?void 0:kl.task)?void 0:bl.created_at]])]),n("span",ee,[d(zl,{class:"mr-2",type:"success",size:"small"},{default:s((()=>l[14]||(l[14]=[p("指派")]))),_:1}),d(zl,{size:"small"},{default:s((()=>l[15]||(l[15]=[p("抖音短视频")]))),_:1})])])],512),[[y,(null==(hl=null==(gl=Ae.value)?void 0:gl.task)?void 0:hl.task_name)||Ue.value]])])])]),n("div",le,[n("legend",ae,[l[18]||(l[18]=n("span",null,"达人选择",-1)),n("span",null,[d(Vl,{onClick:Ze,disabled:"info"==Ke.value},{default:s((()=>l[16]||(l[16]=[p(" 添加达人 ")]))),_:1},8,["disabled"]),d(Vl,{onClick:$e,type:"primary",disabled:"info"==Ke.value},{default:s((()=>l[17]||(l[17]=[p(" 创建提报数据并下载 ")]))),_:1},8,["disabled"])])]),n("div",te,[n("div",oe,"已选达人 "+k(null==(wl=We.value)?void 0:wl.length),1),n("div",ie,[d(Dl,{data:Te.value,ref_key:"kolListRef",ref:Re,style:{width:"100%"},"header-cell-style":{textAlign:"center",backgroundColor:"#ffffff",color:"#606266"},"cell-style":{textAlign:"center"},onSelectionChange:tl},{default:s((()=>[v(d(Ul,{type:"selection",width:"55"},null,512),[[y,"info"!=Ke.value]]),d(Ul,{prop:"kol_name",label:"达人名称",width:"200"},{default:s((e=>{var l,a,t,o;return[n("div",re,[(null==(l=e.row)?void 0:l.kol_photo)?(u(),r("img",{key:0,src:null==(a=e.row)?void 0:a.kol_photo,width:"45",height:"45",alt:""},null,8,ue)):b("",!0),n("span",ne,k(null==(t=e.row)?void 0:t.kol_name),1)]),p(" ID:"+k(null==(o=e.row)?void 0:o.platform_uid),1)]})),_:1}),d(Ul,{prop:"name",label:"服务类型",width:"200"},{default:s((e=>[d(ql,{style:{width:"160px"},modelValue:e.row.cooperation_type,"onUpdate:modelValue":l=>e.row.cooperation_type=l,onChange:l=>{var a;1==(a=e.row).cooperation_type?a.kol_price=a.price_1_20*a.kol_num:2==a.cooperation_type?a.kol_price=a.price_20_60*a.kol_num:71==a.cooperation_type&&(a.kol_price=a.price_60*a.kol_num)},disabled:"info"==Ke.value},{default:s((()=>[(u(),r(_,null,m(Ye,(e=>d(Cl,{value:e.value,label:e.label},null,8,["value","label"]))),64))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange","disabled"])])),_:1}),d(Ul,{prop:"name",label:"数量",width:"150"},{default:s((e=>[d(Il,{modelValue:e.row.kol_num,"onUpdate:modelValue":l=>e.row.kol_num=l,disabled:"info"==Ke.value,onChange:l=>{var a;1==(a=e.row).cooperation_type?a.kol_price=a.price_1_20*a.kol_num:2==a.cooperation_type?a.kol_price=a.price_20_60*a.kol_num:71==a.cooperation_type&&(a.kol_price=a.price_60*a.kol_num)},min:1,label:"描述文字"},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])),_:1}),d(Ul,{prop:"name",label:"价格",width:"150"},{default:s((e=>{var l;return[n("div",null,"¥"+k(null==(l=e.row)?void 0:l.kol_price),1)]})),_:1}),d(Ul,{prop:"name",label:"历史返点"},{default:s((e=>[d(Kl,{placement:"top",width:800,trigger:"hover",content:"this is content, this is content, this is content"},{reference:s((()=>{var l;return[n("span",de,[p(k(100*(null==(l=e.row)?void 0:l.rebate_ratio))+"% ",1),d(Al,null,{default:s((()=>[d(Ll)])),_:1})])]})),default:s((()=>{var l;return[d(Dl,{data:null==(l=e.row)?void 0:l.history_point_lists,width:"400",fit:"","header-cell-style":{textAlign:"center",backgroundColor:"#ffffff",color:"#606266"}},{default:s((()=>[d(Ul,{property:"demand_name",label:"视频信息",width:"200"},{default:s((e=>{var l,a;return[d(Rl,{content:null==(l=null==e?void 0:e.row)?void 0:l.title,placement:"bottom",effect:"light"},{default:s((()=>{var l,a;return[n("a",{href:null==(l=null==e?void 0:e.row)?void 0:l.video_url,target:"_blank",class:"text-sky-600 w-40 cursor overlap"},k(null==(a=null==e?void 0:e.row)?void 0:a.title),9,se)]})),_:2},1032,["content"]),n("div",pe,k(null==(a=null==e?void 0:e.row)?void 0:a.release_time),1)]})),_:1}),d(Ul,{label:"任务信息",width:"220"},{default:s((e=>{var l,a,t;return[n("div",ve,[d(Rl,{content:null==(l=null==e?void 0:e.row)?void 0:l.title,placement:"bottom",effect:"light"},{default:s((()=>{var l,a;return[n("span",ce,k((null==(l=null==e?void 0:e.row)?void 0:l.demand_name)?null==(a=null==e?void 0:e.row)?void 0:a.demand_name:"无"),1)]})),_:2},1032,["content"]),n("span",_e,k(Xe[null==(a=null==e?void 0:e.row)?void 0:a.demand_type]),1)]),n("div",me,"任务ID:"+k(null==(t=null==e?void 0:e.row)?void 0:t.demand_id.toString()),1)]})),_:1}),d(Ul,{property:"rebate_ratio",label:"客户信息",width:"180",align:"center"},{default:s((e=>{var l,a,t;return[n("div",fe,k((null==(l=null==e?void 0:e.row)?void 0:l.account_name)?null==(a=null==e?void 0:e.row)?void 0:a.account_name:"无"),1),n("div",ye,"账户:"+k(null==(t=null==e?void 0:e.row)?void 0:t.star_id.toString()),1)]})),_:1}),d(Ul,{property:"rebate_ratio",label:"返点信息",align:"center"},{default:s((e=>{var l,a;return[p(k((null==(l=null==e?void 0:e.row)?void 0:l.rebate_ratio)?null==(a=null==e?void 0:e.row)?void 0:a.rebate_ratio:0)+"% ",1)]})),_:1}),d(Ul,{property:"media_name",label:"媒介人员",align:"center"},{default:s((e=>{var l,a;return[p(k((null==(l=null==e?void 0:e.row)?void 0:l.media_name)?null==(a=null==e?void 0:e.row)?void 0:a.media_name:"无"),1)]})),_:1})])),_:2},1032,["data"])]})),_:2},1024)])),_:1}),d(Ul,{prop:"name",label:"采集状态"},{default:s((e=>{var l;return[p(k(Me[null==(l=null==e?void 0:e.row)?void 0:l.collection_status]),1)]})),_:1}),d(Ul,{prop:"name",label:"操作"},{default:s((e=>[d(Fl,{title:"确定删除当前达人么?",onConfirm:l=>{var a,t;return((e,l)=>{let a=We.value.findIndex((l=>l.platform_uid==e));l&&C({task_id:Ae.value.task.id,process_kol_id:l}).then((e=>{g({message:"删除成功",type:"success"})})),Te.value.splice(a,1),localStorage.setItem("kolList",JSON.stringify(Te.value))})(null==(a=e.row)?void 0:a.platform_uid,null==(t=e.row)?void 0:t.process_kol_id)},onCancel:l[2]||(l[2]=()=>{})},{reference:s((()=>[d(Vl,{size:"small",disabled:"info"==Ke.value},{default:s((()=>l[19]||(l[19]=[p("删除")]))),_:1},8,["disabled"])])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])])])]),n("div",ke,[l[21]||(l[21]=n("legend",{class:"module-primary-legend"},"建联人员",-1)),n("div",be,[n("div",ge,[n("div",he,[n("div",we,[l[20]||(l[20]=n("div",{class:"project-filter-item-title"},"建联人员",-1)),n("div",xe,[d(ql,{style:{width:"200px"},disabled:"info"==Ke.value,modelValue:Je.value,"onUpdate:modelValue":l[3]||(l[3]=e=>Je.value=e),filterable:"",onChange:ul,loading:Ne.value,placeholder:"请选择建联人员"},{default:s((()=>[(u(!0),r(_,null,m(Qe.value,(e=>(u(),f(Cl,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["disabled","modelValue","loading"])])])])])])])])]),n("div",je,[l[25]||(l[25]=n("div",{class:"footer-info"},null,-1)),n("div",Ve,[d(Vl,{onClick:cl},{default:s((()=>l[22]||(l[22]=[p(" 取消 ")]))),_:1}),v(d(Vl,{type:"primary",onClick:l[4]||(l[4]=e=>_l(1)),disabled:"info"==Ke.value},{default:s((()=>l[23]||(l[23]=[p(" 保存 ")]))),_:1},8,["disabled"]),[[y,"info"!=Ke.value]]),v(d(Vl,{type:"primary",onClick:l[5]||(l[5]=e=>_l(2)),disabled:"info"==Ke.value},{default:s((()=>l[24]||(l[24]=[p(" 提交 ")]))),_:1},8,["disabled"]),[[y,"info"!=Ke.value]])])])])]),d(Ol,{modelValue:Oe.value,"onUpdate:modelValue":l[9]||(l[9]=e=>Oe.value=e),width:"50%"},{footer:s((()=>[d(Vl,{type:"primary",onClick:l[8]||(l[8]=l=>(e.ruleForm,void(He.value.project_id&&He.value.task_name?S(He.value).then((e=>{990==e.code&&(g.success("创建成功！"),Oe.value=!1,ze.value&&dl())})):g.warning("请填写项目信息或任务信息"))))},{default:s((()=>l[35]||(l[35]=[p("创建任务")]))),_:1}),d(Vl,{onClick:rl},{default:s((()=>l[36]||(l[36]=[p("取消")]))),_:1})])),default:s((()=>[d(Nl,{model:He.value,rules:e.rule,ref:"ruleForm","label-width":"auto",style:{"max-width":"700px"},class:"form-style"},{default:s((()=>[l[26]||(l[26]=n("span",{class:"form-span"},"基本信息",-1)),d(Jl,{label:"所属项目",prop:"project_id"},{default:s((()=>[d(ql,{style:{width:"200px"},modelValue:He.value.project_id,"onUpdate:modelValue":l[6]||(l[6]=e=>He.value.project_id=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请选择项目名称","remote-method":il,loading:Ne.value},{default:s((()=>[(u(!0),r(_,null,m(Ge.value,(e=>(u(),f(Cl,{key:e.id,label:e.project_name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),d(Jl,{label:"*任务名称",prop:"task_name"},{default:s((()=>[d(Sl,{modelValue:He.value.task_name,"onUpdate:modelValue":l[7]||(l[7]=e=>He.value.task_name=e),"show-word-limit":"",maxlength:"50"},null,8,["modelValue"])])),_:1}),l[27]||(l[27]=n("span",{class:"form-span"},"任务类型",-1)),l[28]||(l[28]=n("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*推广平台和体裁",-1)),l[29]||(l[29]=n("div",{class:"flex task-label"},[n("div",null,[n("p",{class:"task-label-p"},"抖音"),n("p",{style:{color:"#999","font-size":"12px"}},"王牌渠道锁定新生代消费主力")])],-1)),l[30]||(l[30]=n("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*任务类型",-1)),l[31]||(l[31]=n("div",{class:"flex mb-5 task-label"},[n("div",null,[n("p",{class:"task-label-p"},"指派"),n("p",{style:{color:"#999","font-size":"12px"}},"有明确达人或想指定达人接单")])],-1)),l[32]||(l[32]=n("span",{class:"form-span"},"结算方式",-1)),l[33]||(l[33]=n("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*结算方式",-1)),l[34]||(l[34]=n("div",{class:"flex task-label"},[n("div",null,[n("p",{class:"task-label-p"},"按一口价结算"),n("p",{style:{color:"#999","font-size":"12px"}},"以固定价格与达人合作")])],-1))])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),d(I,{"dialog-visible":Be.value,onCancelAddKol:ol,onConfirmAddKol:el},null,8,["dialog-visible"]),d(L,{"dialog-visible":Ee.value,onCancelAddKol:ol,"kol-select-confirm":We.value},null,8,["dialog-visible","kol-select-confirm"])])}}};export{Ce as default};
