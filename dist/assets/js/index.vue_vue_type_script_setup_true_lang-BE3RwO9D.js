import{s as e,x as a,a as n,c as t,o as r,b as s,y as g}from"./index-BE6Fh1xm.js";const i=e({__name:"index",props:{total:{},currentPage:{},layout:{},pageSizes:{},pageSize:{}},emits:["handleSizeChange","handleCurrentChange"],setup(e,{emit:i}){const o=e,p=i,{total:l,currentPage:u,layout:z,pageSizes:h,pageSize:c}=a(o),C=e=>{p("handleSizeChange",e)},d=e=>{p("handleCurrentChange",e)};return(e,a)=>{const i=n("el-pagination");return r(),t("div",null,[s(i,{background:!0,onSizeChange:C,onCurrentChange:d,"page-sizes":g(h),"page-size":g(c),layout:"total, sizes, prev, pager, jumper",total:g(l),"current-page":g(u)},null,8,["page-sizes","page-size","total","current-page"])])}}});export{i as _};
