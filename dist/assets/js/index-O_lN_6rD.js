import{s,a,Q as e,c as t,o as r,h as o,m as n,g as c,w as p,d,E as l}from"./index-C2bfFjZ1.js";import{_ as u}from"./_plugin-vue_export-helper-BXFjo1rG.js";const i={class:"card content-box"},m=s({name:"longpressDirect"}),_=u(s({...m,setup(s){const u=()=>{l.success("长按事件触发成功 🎉🎉🎉")};return(s,l)=>{const m=a("el-button"),_=e("longpress");return r(),t("div",i,[l[1]||(l[1]=o("span",{class:"text"},"长按指令 🍇🍇🍇🍓🍓🍓",-1)),n((r(),c(m,{type:"primary"},{default:p((()=>l[0]||(l[0]=[d(" 长按2秒触发事件 ")]))),_:1})),[[_,u]])])}}}),[["__scopeId","data-v-0db64c7b"]]);export{_ as default};
