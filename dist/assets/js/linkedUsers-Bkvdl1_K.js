import{i as e,j as a,k as t,l as i,m as l}from"./detail-CsDhBsY3.js";import{u as s,a4 as n,r,f as o,p as d,a as c,c as p,o as v,h as u,z as h,y as m,t as _,b as f,w as b,d as g,C as y,a5 as w,a3 as x}from"./index-BE6Fh1xm.js";import{_ as k}from"./_plugin-vue_export-helper-GSmkUi5K.js";const z={class:"author-page-content"},F={class:"tab-card-group link-card business-card"},L={class:"relative-ratio"},C={class:"ratio-value"},A={class:"link-info"},D={class:"link-label"},S={class:"link-value"},O={class:"relative-ratio"},B={class:"ratio-value"},E={class:"link-info"},I={class:"link-label"},P={class:"link-value"},N={class:"relative-ratio"},W={class:"ratio-value"},j={class:"link-info"},T={class:"link-label"},q={class:"link-value"},$={class:"relative-ratio"},M={class:"ratio-value"},V={class:"link-info"},Z={class:"link-label"},R={class:"link-value"},U={class:"relative-ratio"},Q={class:"ratio-value"},Y={class:"link-info"},G={class:"link-label"},H={class:"link-value"},J={class:"card-panel module-card author-link-struct"},K={class:"card-panel-body"},X={class:"content-container"},ee={class:"right"},ae={class:"dsp-flex"},te={class:"type-title"},ie={style:{position:"relative"}},le={class:"card-panel module-card fans-portrait",style:{}},se={class:"title-wrapper"},ne={class:"operation"},re=k({__name:"linkedUsers",setup(k){const re=s().query.platform_uid,{proxy:oe}=n();var de=["#38a1ff","#fe346e"];let ce=r("link"),pe=["连接用户数","粉丝数"],ve=[],ue=r(1);r(1);let he=r({});var me={};let _e=[],fe={},be=r({});const ge=e=>{if(null==e||isNaN(e))return 0;let a;return a=100*Number(e)%1==0?(100*Number(e)).toFixed(0):(100*Number(e)).toFixed(2),a},ye=(e,a,t)=>{const i=document.getElementById("line-chart"),l=x.init(i);var s;(s={color:de,tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{},grid:{top:70,bottom:50},xAxis:[{type:"category",axisTick:{alignWithLabel:!0},axisLine:{onZero:!1,show:!1,lineStyle:{color:de[0]}},axisPointer:{label:{formatter:function(e){return e.value+(e.seriesData.length?"数量"+e.seriesData[0].data:"")}}},data:e},{type:"category",axisTick:{alignWithLabel:!0,show:!1},axisLine:{show:!1},axisPointer:{label:{formatter:function(e){return"Precipitation  "+e.value+(e.seriesData.length?"name:"+e.seriesData[0].data:"")}}},data:{}}],yAxis:[{type:"value"}],series:[{name:pe[0],type:"line",smooth:!0,emphasis:{focus:"series"},data:a},{name:pe[1],type:"line",smooth:!0,emphasis:{focus:"series"},data:t}]})&&l.setOption(s)},we=(e,a,t)=>{const i=document.getElementById(a),l=x.init(i),s={color:"#38a1ff",xAxis:{type:"category",data:t},yAxis:{type:"value",name:"占比",axisLabel:{formatter:function(e){return 100*e+"%"}}},series:[{data:e,type:"bar"}]};s&&l.setOption(s)},xe=(e,a)=>{ce.value=e;let t=[],i=[];pe=[a],_e.map((l=>{i.push(l.fans_cnt),"link"==e?(t.push(l.link_num),pe=[a,"粉丝数"]):"know"==e?t.push(l.know_num):"interest"==e?t.push(l.interest_num):"like"==e?t.push(l.like_num):"follow"==e&&t.push(l.follow_num)})),ye(ve,t,i)},ke=()=>{e({platform_uid:re,portraits_type:ue.value}).then((e=>{var a,t;if((me=e.data).gender){(e=>{const a=document.getElementById("base-chart1"),t=x.init(a);var i;(i={color:de,tooltip:{trigger:"item"},legend:{bottom:"-0%",left:"center"},series:[{type:"pie",radius:["40%","70%"],label:{show:!1,position:"center"},emphasis:{},labelLine:{show:!1},data:e}]})&&t.setOption(i)})([{value:me.gender.male_rate,name:"男性占比"},{value:me.gender.female_rate,name:"女性占比"}])}if(me.age){let e=["18-23","24-30","31-40","41-50","50+"],{age_threshold_rate:a,age_twenty_rate:t,age_thirty_rate:i,age_forty_rate:l,age_fifty_rate:s}=me.age;we([a,t,i,l,s],"base-chart2",e)}if(me.province){let e=[],i=null==(a=me.province[0])?void 0:a.num,l=null==(t=me.province[0])?void 0:t.num;me.province.map((a=>{a.num>i&&(i=a.num),a.num<l&&(l=a.num),e.push({name:a.province_name,value:a.num})})),((e,a,t)=>{const i=document.getElementById("base-chart3"),l=x.init(i);fetch("/map.json").then((e=>e.json())).then((i=>{x.registerMap("china",i);const s={backgroundColor:"#ffffff",tooltip:{trigger:"item",formatter:function(e){const a=e.value&&!isNaN(e.value)?e.value:0;return`${e.name}<br/>数量：${a.toLocaleString()}`},backgroundColor:"#0C121C",borderColor:"rgba(0, 0, 0, 0.16)",textStyle:{color:"#DADADA",fontSize:"12"}},visualMap:{min:a||0,max:t||100,left:"left",top:"bottom",text:["高","低"],calculable:!0,inRange:{color:["#DCF3FF","#38A1FF"]}},series:[{type:"map",map:"china",zoom:1.2,label:{show:!0,fontSize:10,color:"#333",position:"inside",overflow:"break",width:35,align:"center",padding:[0,0,0,0]},emphasis:{label:{show:!0,fontSize:10,color:"#333"},itemStyle:{areaColor:"#FFA538"}},itemStyle:{normal:{borderColor:"#ffffff",borderWidth:1,areaColor:"#DCF3FF"}},data:e.map((e=>({...e,value:e.value&&!isNaN(e.value)?e.value:0})))}]};l.setOption(s),window.addEventListener("resize",(()=>{l.resize()}))}))})(e,l,i);let s=me.province.sort(((e,a)=>a.city_rate_num-e.city_rate_num)).slice(0,10),n=[],r=[];s.map((e=>{n.push(e.city_rate_num),r.push(e.province_name)})),we(n,"base-chart4",r)}if(me.city_class){let e=["一线","二线","三线","四线","五线"],{urban_one_rate:a,urban_two_rate:t,urban_three_rate:i,urban_four_rate:l,urban_five_rate:s}=me.city_class;we([a,t,i,l,s],"base-chart5",e)}if(me.interest){let e=[],a=[];me.interest.map((t=>{a.push(t.distribution_num_rate),e.push(t.distribution)})),we(a,"base-chart6",e)}if(me.crowd[0]){let e=["Z世代","小镇青年","新锐白领","都市蓝领","小镇中老年","都市银发","资深中产","精致妈妈"],{population_generation_rate:a,population_town_youth_rate:t,population_whitecollar_rate:i,population_bluecollar_rate:l,population_quinquagenarian_rate:s,population_silvercollar_rate:n,population_middle_class_rate:r,population_delicate_mother_rate:o}=me.crowd[0];we([a,t,i,l,s,n,r,o],"base-chart7",e)}if(me.device){let{brand_vivo_rate:e,brand_hongmi_rate:a,brand_honor_rate:t,brand_huawei_rate:i,brand_iPhone_rate:l,brand_xiaomi_rate:s,brand_oppo_rate:n,brand_other_rate:r}=me.device;(e=>{const a=document.getElementById("base-chart8"),t=x.init(a);var i={colorBy:"data",visualMap:{show:!1,calculable:!0,realtime:!1,min:0,max:100,inRange:{colorLightness:[.4,2]},range:[0,100]},axisLabel:{formatter:function(e){return 100*e+"%"}},series:[{name:"Access From",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#999"},labelLine:{lineStyle:{color:"#38A1FF"},smooth:.2,length:10,length2:20},data:e}]};i&&t.setOption(i)})([{name:"vivo",value:e},{name:"oppo",value:n},{name:"其他",value:r},{name:"红米",value:a},{name:"荣耀",value:t},{name:"华为",value:i},{name:"iPhone",value:l},{name:"小米",value:s}])}}))},ze=()=>{i({platform_uid:re}).then((e=>{if(fe=e.data,fe.personage_count){(e=>{const a=document.getElementById("pie-chart"),t=x.init(a);var i;(i={tooltip:{trigger:"item"},legend:{show:!1},series:[{type:"pie",radius:["40%","70%"],color:["#DCF3FF","#38A1FF"],avoidLabelOverlap:!1,innerSize:"80%",encode:{name:"name",value:"value",color:["#DCF3FF","#38A1FF"]},connectLine:{stroke:"#38A1FF"},label:{position:"inside",distance:20,alignTo:"edge",margin:50,offsetY:8,color:"#333333",formatter:e=>e.name+"\n"+e.value/(fe.personage_count+fe.star_count)*100+"%"},labelLine:{show:!1},startAngle:-90,data:e}]})&&t.setOption(i)})([{name:"个人",value:fe.personage_count},{name:"星图",value:fe.star_count}])}let a=[],t=[],i=[],l=[];fe.label.length>0&&(fe.label.map((e=>{a.push(e.content_name),t.push(e.content_num)})),((e,a)=>{const t=document.getElementById("bar-trade1-chat"),i=x.init(t);var l;(l={color:"#38a1ff",tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"value"}],yAxis:[{type:"category",data:e,axisTick:{alignWithLabel:!0}}],dataZoom:[{show:!1,type:"inside",orient:"vertical"}],series:[{name:"Direct",type:"bar",barWidth:"60%",data:a}]})&&i.setOption(l)})(a,t)),fe.label.length>0&&(fe.label[0].children.map((e=>{l.push(e.content_name),i.push(e.content_num)})),((e,a)=>{const t=document.getElementById("bar-trade2-chat"),i=x.init(t);var l;(l={color:"#38a1ff",tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:e,axisTick:{alignWithLabel:!0}}],yAxis:[{type:"value"}],series:[{name:"Direct",type:"bar",barWidth:"60%",data:a}]})&&i.setOption(l)})(l,i))}))},Fe=()=>{l({platform_uid:re}).then((e=>{be.value=e.data,((e,a)=>{const t=document.getElementById("funnel-chart"),i=x.init(t);var l;(l={series:[{type:"funnel",color:"#38a1ff",left:"10%",width:"80%",gap:8,orient:"horizontal",label:{position:"leftBottom",formatter:function(e){return`${e.data.name}:${e.percent}%`},color:"#333",align:"left",fontSize:"12"},labelLine:{show:!0,length:30,lineStyle:{type:"dashed",cap:"square",join:"miter",color:"#999"}},labelLayout:e=>({y:"450"}),itemStyle:{opacity:.7,borderColor:"#ffffff"},data:e,z:99},{name:"",type:"funnel",left:"10%",width:"80%",gap:8,color:"#fe346e",maxSize:"80%",orient:"horizontal",label:{show:!1},itemStyle:{borderColor:"#fe346e",borderWidth:1},data:a,z:100}]})&&i.setOption(l)})([{value:he.value.know?he.value.know:0,name:"了解"},{value:he.value.interest?he.value.interest:0,name:"兴趣"},{value:he.value.like?he.value.like:0,name:"喜欢"},{value:he.value.follow?he.value.follow:0,name:"追随"}],[{value:be.value.know_same_industry_median?be.value.know_same_industry_median:0,name:"了解"},{value:be.value.interest_same_industry_median?be.value.interest_same_industry_median:0,name:"兴趣"},{value:be.value.like_same_industry_median?be.value.like_same_industry_median:0,name:"喜欢"},{value:be.value.follow_same_industry_median?be.value.follow_same_industry_median:0,name:"追随"}])}))};return o((()=>ue.value),((e,a)=>{ke()})),d((()=>{ke(),a({platform_uid:re}).then((e=>{he.value=e.data})),ve=[],t({platform_uid:re}).then((e=>{_e=e.data;let a=[],t=[];_e.map((e=>{itemDate=e.fans_date.split("-")[1]+"-"+e.fans_date.split("-")[2],ve.push(itemDate),a.push(e.link_num),t.push(e.fans_cnt)})),ye(ve,a,t)})),ze(),Fe()})),(e,a)=>{const t=c("QuestionFilled"),i=c("el-icon"),l=c("el-tooltip"),s=c("el-radio-button"),n=c("el-radio-group");return v(),p("div",z,[u("div",null,[a[27]||(a[27]=u("div",{class:"card-head","data-btm":"c24763"},[u("span",{class:"title"},"连接用户分布"),u("div",{class:"el-select base-select","data-btm":"industry-select"},[u("div",{class:"el-select-dropdown el-popper",style:{display:"none","min-width":"160px"}})])],-1)),u("div",F,[u("div",{class:h(["tab-card-item link-card-item","link"==m(ce)?"is-active":""]),onClick:a[0]||(a[0]=e=>xe("link","连接用户数"))},[u("div",L,[a[6]||(a[6]=u("span",{class:"ratio-label"},"30天环比",-1)),u("span",C,_("0"!==ge(m(he).link_relative_ratio)?ge(m(he).link_relative_ratio)+"%":"-"),1)]),u("div",A,[f(l,{class:"item el-tooltip",effect:"dark",content:"近30天有效观看了视频的全部用户数",placement:"top"},{default:b((()=>[u("span",D,[a[7]||(a[7]=g("连接用户数")),f(i,null,{default:b((()=>[f(t)])),_:1})])])),_:1}),u("div",S,_(m(he).link),1)])],2),u("div",{class:h(["tab-card-item link-card-item","know"==m(ce)?"is-active":""]),onClick:a[1]||(a[1]=e=>xe("know","了解数"))},[u("div",O,[a[8]||(a[8]=u("span",{class:"ratio-label"},"30天环比",-1)),u("span",B,_(ge(m(he).know_relative_ratio))+"%",1)]),u("div",E,[f(l,{class:"item el-tooltip",effect:"dark",content:"近30天内仅观看了达人视频1次的用户数",placement:"top"},{default:b((()=>[u("span",I,[a[9]||(a[9]=g("了解")),f(i,null,{default:b((()=>[f(t)])),_:1})])])),_:1}),u("div",P,_(m(he).know),1)])],2),u("div",{class:h(["tab-card-item link-card-item","interest"==m(ce)?"is-active":""]),onClick:a[2]||(a[2]=e=>xe("interest","兴趣数"))},[u("div",N,[a[10]||(a[10]=u("span",{class:"ratio-label"},"30天环比",-1)),u("span",W,_(ge(m(he).interest_relative_ratio))+"%",1)]),u("div",j,[f(l,{class:"item el-tooltip",effect:"dark",content:"近30天内与达人有互动的用户数",placement:"top"},{default:b((()=>[u("span",T,[a[11]||(a[11]=g("兴趣")),f(i,null,{default:b((()=>[f(t)])),_:1})])])),_:1}),u("div",q,_(m(he).interest),1)])],2),u("div",{class:h(["tab-card-item link-card-item","like"==m(ce)?"is-active":""]),onClick:a[3]||(a[3]=e=>xe("like","喜欢数"))},[u("div",$,[a[12]||(a[12]=u("span",{class:"ratio-label"},"30天环比",-1)),u("span",M,_(ge(m(he).like_relative_ratio))+"%",1)]),u("div",V,[f(l,{class:"item el-tooltip",effect:"dark",content:"近30天内对达人有持续关注的用户数。持续关注的具体口径",placement:"top"},{default:b((()=>[u("span",Z,[a[13]||(a[13]=g("喜欢")),f(i,null,{default:b((()=>[f(t)])),_:1})])])),_:1}),u("div",R,_(m(he).like),1)])],2),u("div",{class:h(["tab-card-item link-card-item","follow"==m(ce)?"is-active":""]),onClick:a[4]||(a[4]=e=>xe("follow","追随数"))},[u("div",U,[a[14]||(a[14]=u("span",{class:"ratio-label"},"30天环比",-1)),u("span",Q,_(ge(m(he).follow_relative_ratio))+"%",1)]),u("div",Y,[f(l,{class:"item el-tooltip",effect:"dark",content:"近30天内对达人有追随行为的用户数。追随行为的具体口径",placement:"top"},{default:b((()=>[u("span",G,[a[15]||(a[15]=g("追随")),f(i,null,{default:b((()=>[f(t)])),_:1})])])),_:1}),u("div",H,_(m(he).follow),1)])],2)]),a[28]||(a[28]=u("div",{class:"card-panel module-card"},[u("div",{class:"title-wrapper"},[u("div",{class:"title"},"连接用户趋势"),u("span",{class:"desc"}),u("div",{class:"operation"})]),u("div",{class:"sub-title"},[u("div",{class:"divider el-divider el-divider--horizontal"})]),u("div",{class:"card-panel-body"},[u("div",{class:"base-chart",id:"line-chart","size-sensor-id":"3",style:{width:"816px",height:"300px"}})])],-1)),u("div",J,[a[20]||(a[20]=y('<div class="title-wrapper" data-v-8775578f><div class="title" data-v-8775578f>连接用户</div><span class="desc" data-v-8775578f></span><div class="operation" data-v-8775578f></div></div><div class="sub-title" data-v-8775578f><div class="divider el-divider el-divider--horizontal" data-v-8775578f></div></div>',2)),u("div",K,[u("div",X,[a[19]||(a[19]=u("div",{class:"left"},[u("div",{class:"horizontal-funnel"},[u("div",{class:"base-chart","size-sensor-id":"2",style:{position:"relative"}},[u("div",null,[u("div",{class:"describe"},[u("div",null,[u("span",{class:"dot full"}),u("span",null,"行业均值")]),u("div",{class:"median ml-2"},[u("span",{class:"dot compare"}),u("span",null,"达人结构")])])]),u("div",{class:"lightcharts-container",id:"funnel-chart",style:{position:"relative",width:"auto",height:"300px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])])],-1)),u("div",ee,[u("div",ae,[u("div",te,_(m(be).author_link_type)+"型达人",1)]),u("section",null,[a[18]||(a[18]=u("div",{class:"section-title"},"总结",-1)),u("p",null,[a[16]||(a[16]=g(" 月度连接总用户数 ")),u("strong",null,_((m(be).summarize/1e5).toFixed(2))+" w，环比 "+_(ge(m(be).summarize_rank_percent))+"% ",1)]),u("p",null,[a[17]||(a[17]=g(" 位于行业内 ")),u("strong",null,"TOP "+_(ge(m(be).summarize_relative_ratio))+"%",1)])])])])])]),a[29]||(a[29]=u("div",{style:{position:"relative"}},[u("div",null,[u("div",{class:"card-panel module-card","data-btm":"c74869",style:{}},[u("div",{class:"title-wrapper"},[u("div",{class:"title"},"连接用户来源"),u("span",{class:"desc"}),u("div",{class:"operation"})]),u("div",{class:"card-panel-body"},[u("div",{class:"users-source"},[u("div",{class:"char-card triangle"},[u("span",{class:"title"},"流量来源"),u("div",{class:"base-chart","size-sensor-id":"4",style:{width:"222px",height:"280px"},id:"pie-chart"})]),u("div",{class:"char-card triangle"},[u("div",{class:"char-header"},[u("span",{class:"title"},"内容一级标签"),u("span",{class:"selected-name"},"已选个人")]),u("div",{class:"base-chart age-chart","size-sensor-id":"5",id:"bar-trade1-chat",style:{width:"222px",height:"280px"}})]),u("div",{class:"char-card"},[u("div",{class:"char-header"},[u("span",{class:"title"},"内容二级标签"),u("span",{class:"selected-name"},"已选name:时尚")]),u("div",{class:"base-chart age-chart",id:"bar-trade2-chat",style:{width:"222px",height:"280px"},"size-sensor-id":"6"})])])])])])],-1)),u("div",ie,[u("div",null,[u("div",le,[u("div",se,[a[24]||(a[24]=u("div",{class:"title"},"连接用户画像",-1)),a[25]||(a[25]=u("span",{class:"desc"},null,-1)),u("div",ne,[f(n,{modelValue:m(ue),"onUpdate:modelValue":a[5]||(a[5]=e=>w(ue)?ue.value=e:ue=e)},{default:b((()=>[f(s,{label:1},{default:b((()=>a[21]||(a[21]=[g("观众画像")]))),_:1}),f(s,{label:2},{default:b((()=>a[22]||(a[22]=[g("粉丝画像")]))),_:1}),f(s,{label:3},{default:b((()=>a[23]||(a[23]=[g("铁粉画像")]))),_:1})])),_:1},8,["modelValue"])])]),a[26]||(a[26]=u("div",{class:"card-panel-body"},[u("div",{class:"charts"},[u("div",{class:"chart-container gender-container"},[u("p",{class:"title"},"性别分布"),u("div",{class:"base-chart gender-chart","size-sensor-id":"19",style:{position:"relative"}},[u("div",{class:"lightcharts-container",style:{position:"relative",width:"auto",height:"235px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px",cursor:"auto"}},[u("canvas",{id:"base-chart1",width:"457",height:"293",style:{width:"366px",height:"235px"}})])])]),u("div",{class:"chart-container"},[u("p",{class:"title"},"年龄分布"),u("div",{class:"base-chart age-chart","size-sensor-id":"20",style:{position:"relative"}},[u("div",{class:"lightcharts-container",style:{position:"relative",width:"auto",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px",cursor:"auto"}},[u("canvas",{id:"base-chart2",width:"457",height:"342",style:{width:"366px",height:"274px"}})])])]),u("div",{class:"chart-container"},[u("p",{class:"title"},"全国省份分布"),u("div",{class:"base-chart province-chart","size-sensor-id":"21",style:{position:"relative"}},[u("div",{class:"lightcharts-container",id:"base-chart3",style:{position:"relative",width:"366px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])]),u("div",{class:"chart-container"},[u("p",{class:"title"}," 地域占比 TOP10 "),u("div",{class:"base-chart region-chart","size-sensor-id":"22",style:{position:"relative"}},[u("div",{class:"lightcharts-container",style:{position:"relative",width:"auto",height:"263px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}},[u("canvas",{id:"base-chart4",width:"457",height:"328",style:{width:"366px",height:"263px"}})])])]),u("div",{class:"chart-container"},[u("p",{class:"title"},"城市等级分布"),u("div",{class:"base-chart city-level-chart","size-sensor-id":"23",style:{position:"relative"}},[u("div",{class:"lightcharts-container",id:"base-chart5",style:{position:"relative",width:"366px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])]),u("div",{class:"chart-container"},[u("p",{class:"title"},"兴趣分布"),u("div",{class:"base-chart interest-chart","size-sensor-id":"12",style:{position:"relative"}},[u("div",{class:"lightcharts-container",id:"base-chart6",style:{position:"relative",width:"366px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])]),u("div",{class:"chart-container full-width"},[u("p",{class:"title"},"八大人群占比"),u("div",{class:"base-chart eight-crowds-chart","size-sensor-id":"24",style:{position:"relative"}},[u("div",{class:"lightcharts-container",id:"base-chart7",style:{position:"relative",width:"782px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])]),u("div",{class:"chart-container device-container"},[u("p",{class:"title"},"设备分布"),u("div",{class:"base-chart","size-sensor-id":"25"},[u("div",{class:"lightcharts-container"},[u("canvas",{id:"base-chart8",width:"366",height:"274",style:{width:"366px",height:"274px"}})])])])])],-1))])])])])])}}},[["__scopeId","data-v-8775578f"]]);export{re as default};
