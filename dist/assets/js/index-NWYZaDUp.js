import{k as e,r as a,e as l,f as t,p as s,R as u,c as n,n as r,h as o,b as i,w as v,a as c,t as d,y as g,_ as m,F as p,i as w,d as f,m as h,z as y,$ as _,v as b,g as k,aP as T,E as I,aL as q,o as $,Z as x,K as S}from"./index-BE6Fh1xm.js";import{u as M}from"./index-BCAgZHFR.js";import D from"./HistoryList-CvfmKkLT.js";import{_ as E}from"./_plugin-vue_export-helper-GSmkUi5K.js";import"./DetailDrawer-Dr6kpDhL.js";const j={class:"video-review-container"},O={class:"global-tabs"},R={class:"main-content"},z={class:"form-item"},A={class:"form-item"},C={class:"word-count"},L={class:"form-item"},N={class:"word-count"},V={class:"form-item"},W={class:"upload-area"},H={class:"upload-trigger"},F={key:0,class:"upload-image-preview"},B={class:"actions"},Y={class:"result-card"},J={class:"header-right"},U={class:"time-cost"},P={class:"card-content"},K={class:"review-items"},Z={class:"review-item"},G={class:"review-item"},Q={class:"review-item"},X={class:"review-item"},ee={class:"value"},ae={class:"review-item"},le={class:"value"},te={key:0,class:"task-progress-list"},se={class:"task-progress-title"},ue={class:"task-progress-cards"},ne={class:"task-header"},re={class:"task-info"},oe={class:"task-id"},ie={class:"task-time"},ve={class:"task-actions"},ce={class:"task-progress"},de={class:"task-progress-info"},ge={key:1,class:"upload-progress"},me={class:"progress-info"},pe={class:"manuscript-container"},we={class:"main-content"},fe={class:"form-section",style:{"padding-right":"20px"}},he={class:"manuscript-header"},ye={class:"header-right"},_e=["innerHTML"],be={key:0,class:"review-image-container"},ke={class:"review-image-preview"},Te={class:"result-section",style:{"padding-left":"20px"}},Ie={class:"review-section"},qe={class:"review-header"},$e={key:0,class:"task-id-selector"},xe={class:"task-id-tags"},Se={key:0,class:"selected-task-info"},Me={class:"task-id"},De={key:0,class:"task-counts"},Ee={class:"review-table"},je={key:0,class:"no-data-tip"},Oe={class:"review-table"},Re={key:0,class:"no-data-tip"},ze=1e4,Ae=E({__name:"index",setup(E){const Ae=a(null),Ce=a(!1),Le=a([]),Ne=a({}),Ve=a({}),We=a("current"),He=a("小红书"),Fe=a(""),Be=a(""),Ye=a([]),Je=a(0),Ue=a(!1),Pe=a(!0);a(!0),a(1),a([]);const Ke=a(""),Ze=a(""),Ge=a([]),Qe=a(null),Xe=a(""),ea=a(null),aa=a(null),la=a(0),ta=a(null),sa=a(null),ua=a(0),na=a({passed:!0,time:"0s",requestTime:"---",violationsNum:0,reviewNum:0});a({status:"success",time:"3s",optimization:"已完成",format:"符合"});const ra=l((()=>Math.floor(Je.value))),oa=l((()=>`${Math.floor(la.value/60)}分${la.value%60}秒`)),ia=l((()=>Ue.value?Je.value<10?`正在准备数据...（${oa.value}）`:Je.value<30?`数据处理中...（${oa.value}）`:Je.value<60?`审核分析中...（${oa.value}）`:Je.value<90?`即将完成...（${oa.value}）`:`处理完成！（${oa.value}）`:"正在提交审核，请稍候...")),va=a([]),ca=a([]),da=a({}),ga=a(""),ma=l((()=>{if(!Fe.value)return"";let e=Fe.value;e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;");const a=new Array(e.length).fill(0),l=ga.value&&da.value[ga.value]?da.value[ga.value].violations:va.value,t=ga.value&&da.value[ga.value]?da.value[ga.value].contentreview:ca.value;if(l&&l.length>0)for(const r of l){const l=r.problemText||r.original||"";if(l){let t=0;for(;-1!==(t=e.indexOf(l,t));){for(let e=0;e<l.length;e++)t+e<a.length&&(a[t+e]=1);t+=l.length}}}if(t&&t.length>0)for(const r of t){const l=r.problemText||r.sentence||"";if(l){let t=0;for(;-1!==(t=e.indexOf(l,t));){for(let e=0;e<l.length;e++)t+e<a.length&&0===a[t+e]&&(a[t+e]=2);t+=l.length}}}let s="",u=0,n="";for(let r=0;r<e.length;r++)a[r]!==u&&(n&&(s+=1===u?`<span class="highlight-red">${n}</span>`:2===u?`<span class="highlight-yellow">${n}</span>`:n,n=""),u=a[r]),n+=e.charAt(r);return n&&(s+=1===u?`<span class="highlight-red">${n}</span>`:2===u?`<span class="highlight-yellow">${n}</span>`:n),s=s.replace(/\n/g,"<br>"),s}));const pa=a(null),wa=a(null),fa=e=>{},ha=async e=>{const a=e.file,l=/\.(jpg|jpeg|png|gif)$/i.test(a.name),t=a.size/1024/1024<50;if(!l)return I.error("文件类型不支持！"),void e.onError(new Error("文件类型不支持"));if(!t)return I.error("文件大小不能超过 50MB！"),void e.onError(new Error("文件大小不能超过 50MB"));try{const l=new FormData;l.append("file",a),I({type:"info",message:"文件上传中，请稍候...",duration:0});const t=await M(l);if(I.closeAll(),t&&990===t.code&&t.data){const l=t.data;Ge.value.includes(l)?(I.warning("该图片已经上传过了"),e.onError(new Error("重复的图片"))):(Ge.value.push(l),Ye.value.push({uid:Date.now().toString(),name:a.name,url:l}),I.success("文件上传成功"),e.onSuccess(t))}else I.warning("文件上传到服务器失败："+(t.msg||"未知错误")),e.onError(new Error("文件上传到服务器失败："+(t.msg||"未知错误")))}catch(s){I.closeAll(),I.error("文件上传失败："+(s.message||"未知错误")),e.onError(s)}},ya=()=>{const e=document.querySelector(".card-content");e&&(Pe.value?e.classList.add("collapsed"):e.classList.remove("collapsed")),Pe.value=!Pe.value},_a=a(!1),ba=(e,a)=>{var l,t,s;const u=a===Xe.value;if(e&&"object"==typeof e&&e.token&&e.result&&500===e.result.code)if(ja(a,100,"任务执行超时","exception"),Va(a),u)xa(),Je.value=100,_a.value=!1,I.error({message:`任务执行超时：${e.result.msg||"服务器处理超时"}`,duration:5e3}),Ue.value=!1;else{const l=a?a.substring(0,8):"";q({title:"任务执行超时",message:`任务ID: ${l}... ${e.result.msg||"服务器处理超时"}`,type:"error",duration:5e3})}else{if(e&&"abbreviation_review_progress"===e.type){if(e.progress){const l=Math.min(100,Math.max(0,parseInt(e.progress||0)));ja(a,l,`处理中...${l}%`),u&&(Je.value=l),l>=70&&l<95&&Fa(a,l)}}else if(e&&"abbreviation_review_complete"===e.type){if(e.result&&(200===e.result.code||e.result.data&&!e.result.data.violations)){if(e.result){let l=e.result.data||e.result;Ja(a,l)}ja(a,100,"已完成","success"),Va(a),u&&(xa(),Je.value=100,_a.value=!1,Ea(e.result))}else ja(a,100,"已完成但有问题","warning"),Va(a),u&&(xa(),Je.value=100,_a.value=!1,Ea(e.result))}else if(e&&e.result){if(e.result.request_id&&e.result.request_id,500===e.result.code){if(ja(a,100,"任务执行超时","exception"),Va(a),u)xa(),Je.value=100,_a.value=!1,I.error({message:`任务执行超时：${e.result.msg||"服务器处理超时"}`,duration:5e3}),Ue.value=!1;else{const l=a?a.substring(0,8):"";q({title:"任务执行超时",message:`任务ID: ${l}... ${e.result.msg||"服务器处理超时"}`,type:"error",duration:5e3})}return}if(300===e.result.code){const e=(null==(l=Ve.value[a])?void 0:l.progress)||0,t=Math.max(e,50);ja(a,t,`处理中...${t}%`),u&&(Je.value=Math.max(Je.value,t),t>=70&&t<95&&Fa(a,t))}else 200===e.result.code&&e.result.data?(Ja(a,e.result.data),ja(a,100,"已完成","success"),Va(a),u&&(xa(),Je.value=100,_a.value=!1,Ea(e.result.data))):e.result.data&&"success"===e.result.data.result&&(e.result.data&&Ja(a,e.result.data),ja(a,100,"已完成","success"),Va(a),u&&(xa(),Je.value=100,_a.value=!1,Ea(e.result)))}else if(e&&(e.progress||e.request_id)&&(!e.request_id||e.request_id===(u?Xe.value:a))){if(e.progress){const l=Math.min(100,Math.max(0,parseInt(e.progress||0)));ja(a,l,`处理中...${l}%`),u&&(Je.value=l),l>=70&&l<95&&Fa(a,l)}if(e.result){e.result&&(200===e.result.code||"success"===(null==(t=e.result.data)?void 0:t.result)||!(null==(s=e.result.data)?void 0:s.violations))?(e.result.data&&Ja(a,e.result.data),ja(a,100,"已完成","success"),Va(a),u&&(xa(),Je.value=100,_a.value=!1,Ma(e))):(ja(a,100,"已完成但有问题","warning"),Va(a),u&&(xa(),Je.value=100,_a.value=!1))}}u&&Ta()}},ka=e=>{var a;if(!e)return!1;if("string"==typeof e&&e.startsWith("req_"))return!1;if(qa(),Ne.value[e]&&Ne.value[e].connected)return!0;Ne.value[e]&&!Ne.value[e].connected&&(Ne.value[e].disconnect(),delete Ne.value[e]);ua.value++,_a.value=!1;const l=setTimeout((()=>{Ne.value[e]&&!Ne.value[e].connected&&(Ne.value[e].disconnect(),delete Ne.value[e],ua.value<3?ka(e):$a("连接超时"))}),ze);try{const t=T("ws://47.103.150.38:8899",{transports:["websocket"],autoConnect:!1,reconnectionAttempts:3,reconnectionDelay:2e3,timeout:ze,query:{token:window.localStorage.getItem("userId")||"",user_id:window.localStorage.getItem("userId")||"",request_id:e}});return Le.value.indexOf(e)>-1?Ne.value[e]&&Ne.value[e].disconnect():Le.value.push(e),Ne.value[e]=t,e===Xe.value&&(Ae.value=t),t.on("connect",(()=>{var a;e===Xe.value&&(Ce.value=!0),clearTimeout(l),ua.value=0,_a.value=!1,t.emit("get_review_status",{request_id:e}),e===Xe.value&&Ta();((null==(a=Ve.value[e])?void 0:a.progress)||0)<45&&ja(e,45,"连接成功，等待数据...45%","normal")})),t.on("disconnect",(a=>{e===Xe.value&&(Ce.value=!1),_a.value=!1,Ve.value[e]&&Ve.value[e].progress<100&&ja(e,Ve.value[e].progress,`连接断开: ${a}`,"warning"),"io client disconnect"!==a&&Ve.value[e]&&Ve.value[e].progress<100&&setTimeout((()=>{Ve.value[e]&&Ve.value[e].progress<100&&ka(e)}),5e3)})),t.on("connect_error",(a=>{e===Xe.value&&(Ce.value=!1),_a.value=!1,clearTimeout(l),Ve.value[e]&&ja(e,Ve.value[e].progress,`连接错误: ${a.message||"未知错误"}`,"exception"),Ue.value&&e===Xe.value&&(ua.value<3?setTimeout((()=>{Ue.value&&!Ne.value[e]&&ka(e)}),5e3):$a("连接错误: "+(a.message||"未知错误")))})),t.on("receive",(a=>{e===Xe.value&&Ia(),ba(a,e)})),t.connect(),ja(e,Math.max((null==(a=Ve.value[e])?void 0:a.progress)||0,40),"等待连接...","normal"),!0}catch(t){return clearTimeout(l),Ve.value[e]&&ja(e,Ve.value[e].progress,`连接失败: ${t.message||"未知错误"}`,"exception"),e===Xe.value&&$a("创建连接失败: "+(t.message||"未知错误")),!1}},Ta=()=>{Ia(),sa.value=setTimeout((()=>{Ue.value&&Ce.value&&$a("消息响应超时")}),12e4)},Ia=()=>{sa.value&&(clearTimeout(sa.value),sa.value=null)},qa=()=>{ta.value&&(clearTimeout(ta.value),ta.value=null),Ia()},$a=e=>{qa(),Ae.value&&(Ae.value.disconnect(),Ae.value=null),Xe.value&&Ne.value[Xe.value]&&delete Ne.value[Xe.value],_a.value=!1,localStorage.removeItem("reviewRequestId"),q({title:"网络连接问题",message:"与服务器的连接异常，审核结果将在历史记录中查看",type:"warning",duration:5e3}),Sa()},xa=()=>{ea.value&&(clearInterval(ea.value),ea.value=null),aa.value&&(clearInterval(aa.value),aa.value=null),qa()},Sa=()=>{xa(),window.reviewTimeoutId&&(clearTimeout(window.reviewTimeoutId),window.reviewTimeoutId=null),Je.value=0,la.value=0,Ue.value=!1,_a.value=!1,qa(),localStorage.removeItem("reviewRequestId"),Qe.value&&Qe.value.refreshHistory(),We.value="history"},Ma=e=>{try{const a=((new Date-new Date(na.value.requestTime))/1e3).toFixed(2);na.value.time=a+"s";let l=!1;e.request_id&&(e.request_id,Xe.value);const t=[];if(e.result&&e.result.violations&&e.result.violations.length>0){l=!0;const a=e.result.violations;t.push(...a),a.value=a}else va.value=[];const s=[];if(e.result&&e.result.data.contentreview&&e.result.data.contentreview.length>0){l=!0;const a=e.result.data.contentreview;s.push(...a),ca.value=a}else ca.value=[];const u=e.request_id||Xe.value;u&&(da.value[u]||(da.value[u]={violations:[],contentreview:[],content:e.result&&e.result.content?e.result.content:Fe.value}),da.value[u].violations=t,da.value[u].contentreview=s,e.result&&e.result.content?da.value[u].content=e.result.content:da.value[u].content||(da.value[u].content=Fe.value),ga.value=u),Ze.value=Fe.value,na.value={passed:!l,time:a+"s",requestTime:na.value.requestTime,violationsNum:va.value.length,reviewNum:ca.value.length},l?We.value="review":I.success("内容审核通过，无违规内容"),Ue.value=!1,q({title:"审核完成",message:`审核任务已完成，耗时${a}秒`,type:"success",duration:3e3})}catch(a){Ue.value=!1,I.error("处理审核结果失败: "+(a.message||"未知错误"))}};function Da(e){return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`}document.querySelectorAll(".actions .el-button").forEach((e=>{e.addEventListener("click",(e=>{"提交审核"===e.target.textContent.trim()&&(e.preventDefault(),(async()=>{Ze.value=Fe.value,await Wa(),document.querySelectorAll(".result-card").forEach(((e,a)=>{e.style.opacity="0",e.style.transform="translateY(20px)",setTimeout((()=>{e.style.opacity="1",e.style.transform="translateY(0)",e.style.transition="all 0.5s ease"}),200*a)}))})())}))}));t(We,(e=>{const a=document.querySelector(".main-content");a&&(a.style.opacity="0",a.style.transform="translateY(10px)",setTimeout((()=>{a.style.opacity="1",a.style.transform="translateY(0)",a.style.transition="all 0.5s ease";document.querySelectorAll(".form-section, .result-section").forEach((e=>(e=>{if(!e)return;const a=e.scrollTop,l=performance.now(),t=u=>{const n=u-l;n<500?(e.scrollTop=a*(1-s(n/500)),requestAnimationFrame(t)):e.scrollTop=0},s=e=>1-Math.pow(1-e,3);requestAnimationFrame(t)})(e))),"history"===e&&Qe.value&&Qe.value.loadHistoryList()}),100))}));const Ea=e=>{try{const a=la.value;if(na.value.time=a+"s",!e)return;if(300===e.code)return void Ta();localStorage.removeItem("reviewRequestId");const l=(e=>{if(!e)return null;const a=JSON.parse(JSON.stringify(e));return a.violations&&Array.isArray(a.violations)&&(a.violations=a.violations.map((e=>({...e,problemText:e.problemText||e.original||"",type:e.type||e.category||"违禁词",suggestion:e.suggestion||e.suggested||""})))),a.contentreview&&Array.isArray(a.contentreview)&&(a.contentreview=a.contentreview.map((e=>({...e,problemText:e.problemText||e.sentence||"",type:e.type||e.reason||"内容优化",suggestion:e.suggestion||e.modification_suggestion||""})))),a.content||"string"!=typeof Fe.value||(a.content=Fe.value),a})(e);let t=!1;const s=e.request_id||Xe.value;l.violations&&l.violations.length>0?(t=!0,va.value=l.violations):va.value=[],l.contentreview&&l.contentreview.length>0?(t=!0,ca.value=l.contentreview):ca.value=[],s&&(da.value[s]?(da.value[s].violations=va.value,da.value[s].contentreview=ca.value,l.content?da.value[s].content=l.content:da.value[s].content||(da.value[s].content=Fe.value),da.value[s].timestamp=(new Date).getTime()):da.value[s]={violations:va.value,contentreview:ca.value,content:l.content||Fe.value,timestamp:(new Date).getTime()},ga.value=s),Ze.value=Fe.value,na.value={...na.value,passed:!t,violationsNum:va.value.length,reviewNum:ca.value.length},t?I({message:`发现${va.value.length}个违禁词，${ca.value.length}处内容需优化`,type:"warning",duration:5e3}):I.success("内容审核通过，无违规内容"),Ue.value=!1,e.request_id&&Ha[e.request_id]&&(clearInterval(Ha[e.request_id]),delete Ha[e.request_id]),completedCheckAnimation.value=!0,setTimeout((()=>{completedCheckAnimation.value=!1}),3e3)}catch(a){Ue.value=!1,Sa()}};s((()=>{const e=document.querySelector(".main-content");if(e&&pa.value&&wa.value){const a=e.offsetWidth/2;pa.value.style.width=`${a}px`,wa.value.style.width=`${a}px`}const a=localStorage.getItem("reviewRequestId");a&&(a.startsWith("req_")?localStorage.removeItem("reviewRequestId"):(Xe.value=a,ka(Xe.value))),Object.keys(Ve.value).forEach((e=>{"string"==typeof e&&e.startsWith("req_")&&delete Ve.value[e]})),window.addEventListener("beforeunload",(()=>{localStorage.removeItem("reviewRequestId")}))})),u((()=>{document.removeEventListener("mousemove",handleResize),document.removeEventListener("mouseup",stopResize),xa(),qa(),window.reviewTimeoutId&&(clearTimeout(window.reviewTimeoutId),window.reviewTimeoutId=null),localStorage.removeItem("reviewRequestId"),Ae.value&&(Ae.value.disconnect(),Ae.value=null)}));const ja=(e,a,l=null,t=null)=>{e&&("string"==typeof e&&e.startsWith("req_")||(Ve.value[e]||(Ve.value[e]={progress:0,startTime:Da(new Date),statusText:"初始化中...",status:"normal"}),void 0!==a&&(Ve.value[e].progress=a),null!==l&&(Ve.value[e].statusText=l),null!==t&&(Ve.value[e].status=t)))},Oa=e=>{e&&(Ve.value[e]&&delete Ve.value[e],"string"!=typeof e||e.startsWith("req_")||Object.keys(Ve.value).forEach((e=>{"string"==typeof e&&e.startsWith("req_")&&delete Ve.value[e]})))},Ra=()=>{0!==Object.keys(Ve.value).length&&S.confirm("确定要清除所有任务吗？","清除所有任务",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Object.keys(Ne.value).forEach((e=>{Ne.value[e].disconnect(),delete Ne.value[e]})),Object.keys(Ha).forEach((e=>{clearInterval(Ha[e]),delete Ha[e]})),Object.keys(Ve.value).forEach((e=>{delete Ve.value[e]})),Le.value=[],Xe.value="",localStorage.removeItem("reviewRequestId"),Je.value=0,xa(),Sa(),I({type:"success",message:"所有任务已清除"})})).catch((()=>{}))},za=a(null),Aa=a(!1),Ca=a(!1),La=()=>{if(Ca.value)return!0;try{const e=T("ws://47.103.150.38:8899",{transports:["websocket"],autoConnect:!1,reconnectionAttempts:5,reconnectionDelay:2e3,timeout:ze,query:{token:window.localStorage.getItem("userId")||"",user_id:window.localStorage.getItem("userId")||""}});return e.on("connect",(()=>{Aa.value=!0,_a.value=!1,Object.keys(Ve.value).forEach((a=>{if(!a||a.startsWith("req_"))return;const l=Ve.value[a];l&&l.progress<100&&(e.emit("get_review_status",{request_id:a}),l.progress>=70&&l.progress<95&&Fa(a,l.progress))}))})),e.on("disconnect",(e=>{Aa.value=!1,"io client disconnect"!==e&&setTimeout((()=>{Aa.value||za.value.connect()}),5e3)})),e.on("connect_error",(e=>{Aa.value=!1,setTimeout((()=>{Aa.value||za.value.connect()}),5e3)})),e.on("receive",(e=>{var a;let l=null;if(e&&"object"==typeof e&&e.token){if(e.result&&e.result.request_id){if(l=e.result.request_id,500===e.result.code)if(ja(l,100,"任务执行超时","exception"),Va(l),l===Xe.value)xa(),Je.value=100,_a.value=!1,I.error({message:`任务执行超时：${e.result.msg||"服务器处理超时"}`,duration:5e3}),Ue.value=!1;else{const a=l?l.substring(0,8):"";q({title:"任务执行超时",message:`任务ID: ${a}... ${e.result.msg||"服务器处理超时"}`,type:"error",duration:5e3})}else if(300===e.result.code){const e=(null==(a=Ve.value[l])?void 0:a.progress)||0,t=Math.max(e,60);ja(l,t,`处理中...${t}%`,"normal"),l===Xe.value&&(Je.value=Math.max(Je.value,t),Ta());const s=Math.max(e,60);Fa(l,s)}else if(200===e.result.code){if(e.result&&(200===e.result.code||e.result.data&&!e.result.data.violations)){ja(l,100,"已完成","success");const a=l?l.substring(0,8):"";q({title:"审核成功",message:`任务ID: ${a}... 已完成`,type:"success",duration:3e3}),Va(l),da.value[l]=e.result.data,da.value[l].content=e.result.content,l===Xe.value&&(xa(),Je.value=100,_a.value=!1,Ea(e.result.data||e.result))}else ja(l,100,"已完成但有问题","warning"),l===Xe.value&&(xa(),Je.value=100,_a.value=!1,Ea(e.result.data||e.result))}}else if(e.token&&e.progress){l=e.request_id||Xe.value;const a=Math.min(100,Math.max(0,parseInt(e.progress||0)));ja(l,a,`处理中...${a}%`,"normal"),l===Xe.value&&(Je.value=a),a>=70&&a<95&&Fa(l,a)}}else if(l=Xe.value,e&&void 0!==e.progress){const a=Math.min(100,Math.max(0,parseInt(e.progress||0)));ja(l,a,`处理中...${a}%`,"normal"),Je.value=a,a>=70&&a<95&&Fa(l,a)}l||ba(e,Xe.value)})),za.value=e,Ca.value=!0,e.connect(),!0}catch(e){return Ca.value=!1,Aa.value=!1,!1}},Na=new Set,Va=e=>{var a,l;if(e&&!Na.has(e)){if(Na.add(e),ja(e,100,"已完成","success"),e===Xe.value&&localStorage.removeItem("reviewRequestId"),da.value[e]){ga.value=e,We.value="review";const t=e.substring(0,8);q({title:"审核完成",message:`任务ID: ${t}... 已完成，正在显示结果`,type:"success",duration:3e3}),va.value=(null==(a=da.value[e])?void 0:a.violations)||[],ca.value=(null==(l=da.value[e])?void 0:l.contentreview)||[]}setTimeout((()=>{Oa(e);const a=Le.value.indexOf(e);a>-1&&Le.value.splice(a,1),Ne.value[e]&&(Ne.value[e].disconnect(),delete Ne.value[e])}),5e3)}},Wa=async()=>{try{if(!He.value)return void I.warning("请选择平台");if(!Fe.value)return void I.warning("请输入原文内容");if(!Be.value)return void I.warning("请输入内容简述");Ue.value=!0,_a.value=!1,q({title:"提交审核",message:"正在提交审核任务，请稍候...",type:"info",duration:3e3}),(()=>{xa(),Je.value=5,na.value.time="0秒";let e=5,a=0,l=0;ea.value=setInterval((()=>{Math.floor(Je.value)===Math.floor(e)?(a++,a>16&&(Je.value+=2,a=0)):(a=0,e=Je.value);let t=0;t=Je.value<20?.4185:Je.value<40?.27899999999999997:Je.value<60?.23249999999999998:Je.value<80?.186:.11624999999999999,Je.value+=t,Je.value>98&&(Je.value=98),l++,l%10==0&&(l=0),la.value>=120&&Je.value<98&&(Je.value=98)}),300)})();const l=new Date;na.value.requestTime=Da(l),(e=>{if(aa.value&&clearInterval(aa.value),la.value=0,e){const a=new Date-e;la.value=Math.floor(a/1e3)}aa.value=setInterval((()=>{if(la.value++,la.value<60)na.value.time=`${la.value}秒`;else{const e=Math.floor(la.value/60),a=la.value%60;na.value.time=`${e}分${a}秒`}}),1e3)})(l),Ca.value?!Aa.value&&za.value&&za.value.connect():La();const t=localStorage.getItem("userId")||"",s=`req_${Date.now()}_${Math.floor(1e3*Math.random())}`,u={keyword:"inly",timestamp:(new Date).getTime(),user_id:t,text:Fe.value,brief:Be.value,type:2,img:Ge.value,platform:He.value,request_id:s};Xe.value;Xe.value=u.request_id,ja(Xe.value,5,"初始化中...","normal"),Je.value<15&&(Je.value=15,ja(Xe.value,15,"请求中...15%","normal")),await new Promise((e=>setTimeout(e,800))),Je.value<20&&(Je.value=20,ja(Xe.value,20,"准备发送请求...20%","normal")),Je.value<25&&(Je.value=25,ja(Xe.value,25,"发送请求...25%","normal"));const n=await(a=u,e.post("/abbreviationReview",a));Je.value<35&&(Je.value=35,ja(Xe.value,35,"处理中...35%","normal"));const r=n.data;if(r){if(r.request_id){const e=r.request_id;Xe.value=e,localStorage.setItem("reviewRequestId",Xe.value),Je.value<40&&(Je.value=40,Ve.value[s]?(ja(e,Math.max(Ve.value[s].progress,40),"等待处理...40%","normal"),Oa(s)):ja(e,40,"等待处理...40%","normal")),Aa.value&&za.value&&za.value.emit("get_review_status",{request_id:e})}const e=setTimeout((()=>{Ue.value&&Sa()}),18e4);window.reviewTimeoutId=e,Ue.value=!1}else xa(),Ue.value=!1,I.error(r.msg||"提交审核失败，请稍后重试")}catch(l){xa(),Ue.value=!1,Je.value=0,la.value=0,I.error({message:"提交审核失败: "+(l.message||"未知错误"),duration:5e3})}var a};s((()=>{const e=document.querySelector(".main-content");if(e&&pa.value&&wa.value){const a=e.offsetWidth/2;pa.value.style.width=`${a}px`,wa.value.style.width=`${a}px`}La(),Object.keys(Ve.value).forEach((e=>{"string"==typeof e&&e.startsWith("req_")&&delete Ve.value[e]}))})),u((()=>{document.removeEventListener("mousemove",handleResize),document.removeEventListener("mouseup",stopResize),xa(),qa(),window.reviewTimeoutId&&(clearTimeout(window.reviewTimeoutId),window.reviewTimeoutId=null),za.value&&(za.value.disconnect(),za.value=null,Ca.value=!1,Aa.value=!1)}));const Ha={},Fa=(e,a)=>{if(Ha[e]&&(clearInterval(Ha[e]),delete Ha[e]),a>=95)return;let l,t;a<80?(l=5e3,t=1):a<90?(l=8e3,t=1):(l=12e3,t=.5),Ha[e]=setInterval((()=>{const a=Ve.value[e];if(!a)return clearInterval(Ha[e]),void delete Ha[e];const l=a.progress;if(l>=95)return clearInterval(Ha[e]),void delete Ha[e];const s=Math.min(95,l+t);ja(e,s,`处理中...${Math.floor(s)}%`,"normal"),e===Xe.value&&(Je.value=s)}),l)};u((()=>{document.removeEventListener("mousemove",handleResize),document.removeEventListener("mouseup",stopResize),xa(),qa(),window.reviewTimeoutId&&(clearTimeout(window.reviewTimeoutId),window.reviewTimeoutId=null),Object.keys(Ha).forEach((e=>{clearInterval(Ha[e])})),za.value&&(za.value.disconnect(),za.value=null,Ca.value=!1,Aa.value=!1)}));const Ba=e=>{e&&(Xe.value=e,localStorage.setItem("reviewRequestId",Xe.value),Aa.value&&za.value&&za.value.emit("get_review_status",{request_id:e}),Ve.value[e]&&(Je.value=Ve.value[e].progress||0),da.value[e]&&(ga.value=e))},Ya=e=>{if(!e||!da.value[e])return void I.warning("找不到该任务的审核结果");ga.value=e,We.value="review";const a=JSON.parse(JSON.stringify(da.value[e]));if(a.content&&(Fe.value=a.content),a.violations&&a.violations.length>0){const e=a.violations.map((e=>({...e,problemText:e.problemText||e.original||"",type:e.type||e.category||"违禁词",suggestion:e.suggestion||e.suggested||""})));va.value=e}else va.value=[];if(a.contentreview&&a.contentreview.length>0){const e=a.contentreview.map((e=>({...e,problemText:e.problemText||e.sentence||"",type:e.type||e.reason||"内容优化",suggestion:e.suggestion||e.modification_suggestion||""})));ca.value=e}else ca.value=[];const l=a.violations&&a.violations.length>0||a.contentreview&&a.contentreview.length>0;na.value={...na.value,passed:!l,violationsNum:va.value.length,reviewNum:ca.value.length};const t=e.substring(0,8);I.success({message:`正在查看任务 ${t}... 的审核结果（违禁词:${va.value.length}, 优化:${ca.value.length}）`,duration:3e3})},Ja=(e,a)=>{if(!e||!a)return;let l=[],t=[];a.violations&&a.violations.length>0&&(l=a.violations.map((e=>({problemText:e.original||"",type:e.category||"",suggestion:e.suggested||"推荐替代词",reason:e.reason||"",source:e.source||"系统检测",key:`type1_${Date.now()}_${Math.random()}`})))),a.contentreview&&a.contentreview.length>0&&(t=a.contentreview.map((e=>({problemText:e.sentence||"",type:e.reason||"内容优化",suggestion:e.modification_suggestion||"",reason:e.source||"系统检测",key:`content_${Date.now()}_${Math.random()}`}))));let s=null;a.content?s=a.content:e===Xe.value&&Fe.value&&(s=Fe.value),da.value[e]?(da.value[e].violations=l,da.value[e].contentreview=t,s&&!da.value[e].content&&(da.value[e].content=s),da.value[e].timestamp=(new Date).getTime()):da.value[e]={violations:l,contentreview:t,content:s,timestamp:(new Date).getTime()}},Ua=e=>{var a,l;if(!da.value[e])return 0;return((null==(a=da.value[e].violations)?void 0:a.length)||0)+((null==(l=da.value[e].contentreview)?void 0:l.length)||0)};return window.handleAuditData=()=>{try{const e=(e=>{const a=[];for(let l=0;l<e;l++){const e=["receive",{token:`135${l}`,result:{code:200,msg:"请求成功",data:{contentreview:[{reason:"内容要求",sentence:"HMOs母乳低聚糖"+(l>0?` 样本${l}`:""),source:"禁止出现'母乳'相关及或者暗示与母乳对比",modification_suggestion:"修改为'特定低聚糖'以避免与母乳的直接对比"},{reason:"内容要求",sentence:"全面对标母乳，理想奶粉",source:"禁止明示或暗示母乳代用品堪比，类似或优于母乳喂养",modification_suggestion:"删除'全面对标母乳，理想奶粉'的描述，避免与母乳的对比"},{reason:"内容要求",sentence:`保护力奶粉${l}`,source:"禁止使用食品品类不应该拥有的功能性",modification_suggestion:"修改为'配方奶粉'以避免功能性宣称"}],violations:[{category:"智能检测",original:`配方之王${l}`,reason:"虚假宣传",source:"《广告法》第4条",suggested:"优选配方"},{category:"智能检测",original:"满分",reason:"绝对化用语",source:"《广告法》第9条",suggested:"高分"},{category:"通用敏感词",original:`全方位${l+1}`,reason:"违反词库：电商违禁词.txt",source:"内部词库",suggested:"多方面"}],is_sensitive:1},request_id:`test-${l}-${Date.now()}`}}];a.push(e)}return a})(3),a=[];return e.forEach(((e,l)=>{var t,s;const u=e[1].result.request_id;Ja(u,e[1].result.data),a.push(u),0===l&&(ga.value=u,va.value=(null==(t=da.value[u])?void 0:t.violations)||[],ca.value=(null==(s=da.value[u])?void 0:s.contentreview)||[])})),We.value="review",I.success({message:`已处理${e.length}条测试数据`,duration:3e3}),a}catch(e){I.error("处理测试数据时发生错误: "+(e.message||"未知错误"))}},(e,a)=>{const l=c("el-icon"),t=c("el-progress"),s=c("el-option"),u=c("el-select"),T=c("el-input"),q=c("el-upload"),M=c("el-image"),E=c("el-button"),ze=c("el-badge"),Ae=c("el-tag"),Ce=c("el-tab-pane"),Ze=c("el-table-column"),ea=c("el-table"),aa=c("el-tabs");return $(),n("div",j,[r("",!0),o("div",O,[i(aa,{modelValue:We.value,"onUpdate:modelValue":a[4]||(a[4]=e=>We.value=e)},{default:v((()=>[i(Ce,{label:"原文",name:"current"},{default:v((()=>[o("div",R,[o("div",{class:"form-section",ref_key:"formSection",ref:pa},[o("div",z,[a[5]||(a[5]=o("div",{class:"label required"},"平台选择",-1)),i(u,{modelValue:He.value,"onUpdate:modelValue":a[0]||(a[0]=e=>He.value=e),class:"platform-select",placeholder:"请选择平台"},{default:v((()=>[i(s,{label:"小红书",value:"小红书"}),i(s,{label:"抖音",value:"抖音"}),i(s,{label:"其它",value:"其它"})])),_:1},8,["modelValue"])]),o("div",A,[a[6]||(a[6]=o("div",{class:"label required"},"原文内容",-1)),i(T,{modelValue:Fe.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Fe.value=e),type:"textarea",rows:8,placeholder:"请输入原文内容"},null,8,["modelValue"]),o("div",C,"全文字数："+d(Fe.value.length),1)]),o("div",L,[a[7]||(a[7]=o("div",{class:"label required"},"brief",-1)),i(T,{modelValue:Be.value,"onUpdate:modelValue":a[2]||(a[2]=e=>Be.value=e),type:"textarea",rows:4,placeholder:"请输入内容简述"},null,8,["modelValue"]),o("div",N,"简述字数："+d(Be.value.length),1)]),o("div",V,[a[10]||(a[10]=o("div",{class:"label"},"图片",-1)),o("div",W,[i(q,{drag:"","file-list":Ye.value,"http-request":ha,"on-change":fa,action:"#","show-file-list":!1,"auto-upload":!0},{default:v((()=>[o("div",H,[i(l,{class:"upload-icon"},{default:v((()=>[i(g(m))])),_:1}),a[8]||(a[8]=o("div",{class:"upload-text"},"点击或拖拽文件到此处上传",-1)),a[9]||(a[9]=o("div",{class:"upload-hint"},"支持扩展名：.png .jpg ...",-1))])])),_:1},8,["file-list"]),Ge.value.length>0?($(),n("div",F,[($(!0),n(p,null,w(Ge.value,((e,a)=>($(),n("div",{class:"image-preview-wrapper",key:a},[i(M,{src:e,fit:"cover",class:"preview-image","preview-src-list":Ge.value},null,8,["src","preview-src-list"]),i(E,{class:"delete-button",type:"danger",size:"small",circle:"",onClick:e=>(e=>{Ge.value.splice(e,1),Ye.value=Ye.value.filter(((a,l)=>l!==e)),0===Ge.value.length&&(Ke.value=""),I.success("图片已删除")})(a)},{default:v((()=>[i(l,null,{default:v((()=>[i(g(x))])),_:1})])),_:2},1032,["onClick"])])))),128))])):r("",!0)])]),o("div",B,[i(E,{type:"primary",onClick:Wa,loading:Ue.value},{default:v((()=>a[11]||(a[11]=[f(" 提交审核 ")]))),_:1},8,["loading"])])],512),o("div",{class:"resizer",onMousedown:a[3]||(a[3]=(...a)=>e.startResize&&e.startResize(...a))},null,32),o("div",{class:"result-section",ref_key:"resultSection",ref:wa},[o("div",Y,[o("div",{class:"card-header",onClick:ya},[a[12]||(a[12]=o("div",{class:"header-left"},[o("span",{class:"title"},"内容审核结果详情")],-1)),o("div",J,[o("span",{class:y(["status",{warning:!na.value.passed}])},d(na.value.passed?"合格检查":"审核不通过"),3),o("span",U,"耗时："+d(na.value.time),1),i(l,{class:y(["toggle-icon",{"is-reverse":!Pe.value}])},{default:v((()=>[i(g(_))])),_:1},8,["class"])])]),h(o("div",P,[o("div",K,[o("div",Z,[a[13]||(a[13]=o("span",{class:"label"},"违规词",-1)),o("span",{class:y(["value",{warning:va.value.length>0}])},d(va.value.length>0?"发现问题":"未发现"),3)]),o("div",G,[a[14]||(a[14]=o("span",{class:"label"},"违规数量",-1)),o("span",{class:y(["value",{warning:va.value.length>0}])},d(va.value.length||0),3)]),o("div",Q,[a[15]||(a[15]=o("span",{class:"label"},"内容优化数量",-1)),o("span",{class:y(["value",{warning:ca.value.length>0}])},d(ca.value.length||0),3)]),o("div",X,[a[16]||(a[16]=o("span",{class:"label"},"请求时间",-1)),o("span",ee,d(na.value.requestTime),1)]),o("div",ae,[a[17]||(a[17]=o("span",{class:"label"},"接口耗时",-1)),o("span",le,d(na.value.time),1)])])],512),[[b,Pe.value]])]),Object.keys(Ve.value).length>0?($(),n("div",te,[o("div",se,[o("span",null,"活跃任务列表 ("+d(Object.keys(Ve.value).length)+")",1),Object.keys(Ve.value).length>0?($(),k(E,{key:0,type:"primary",link:"",size:"small",onClick:Ra},{default:v((()=>a[18]||(a[18]=[f("清空所有")]))),_:1})):r("",!0)]),o("div",ue,[($(!0),n(p,null,w(Ve.value,((e,s)=>($(),n("div",{key:s,class:"task-progress-card"},[o("div",ne,[o("div",re,[o("span",oe,"任务ID: "+d(s.substring(0,8))+"...",1),o("span",ie,d(e.startTime),1),da.value[s]?($(),k(Ae,{key:0,type:"success",size:"small",effect:"plain",style:{"margin-left":"8px"}},{default:v((()=>[a[19]||(a[19]=f(" 有结果 ")),Ua(s)>0?($(),k(ze,{key:0,value:Ua(s),class:"task-badge",type:"warning"},null,8,["value"])):r("",!0)])),_:2},1024)):r("",!0)]),o("div",ve,[da.value[s]?($(),k(E,{key:0,type:"primary",link:"",size:"small",onClick:e=>Ya(s),style:{"margin-right":"8px"}},{default:v((()=>a[20]||(a[20]=[f(" 查看结果 ")]))),_:2},1032,["onClick"])):r("",!0),i(E,{type:"danger",link:"",size:"small",onClick:e=>{var a;(a=s)&&S.confirm(`确定要取消任务 ${a.substring(0,8)}... 吗？`,"取消任务",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Ne.value[a]&&(Ne.value[a].disconnect(),delete Ne.value[a]),Oa(a);const e=Le.value.indexOf(a);e>-1&&Le.value.splice(e,1),Ha[a]&&(clearInterval(Ha[a]),delete Ha[a]),a===Xe.value&&(localStorage.removeItem("reviewRequestId"),Le.value.length>0?Ba(Le.value[Le.value.length-1]):(Xe.value="",Je.value=0,xa(),Sa())),I({type:"success",message:"任务已取消"})})).catch((()=>{}))}},{default:v((()=>[i(l,null,{default:v((()=>[i(g(x))])),_:1})])),_:2},1032,["onClick"])])]),o("div",ce,[i(t,{percentage:e.progress||0,status:e.status},null,8,["percentage","status"]),o("div",de,[o("span",null,d(e.statusText||"处理中..."),1),o("span",null,d(e.progress||0)+"%",1)])])])))),128))])])):r("",!0),Ue.value?($(),n("div",ge,[i(t,{percentage:ra.value},null,8,["percentage"]),o("div",me,[o("span",null,d(ia.value),1),o("span",null,d(ra.value)+"%",1)])])):r("",!0)],512)])])),_:1}),i(Ce,{label:"审核",name:"review"},{default:v((()=>{var e,l,t,s;return[o("div",pe,[o("div",we,[o("div",fe,[o("div",he,[a[21]||(a[21]=o("div",{class:"title-section"},[o("h2",null,"原文标注")],-1)),o("div",ye,[o("span",null,"全文字数："+d(Fe.value.length),1)])]),o("div",{class:"content-text",innerHTML:ma.value},null,8,_e),Ke.value?($(),n("div",be,[a[22]||(a[22]=o("h3",{class:"image-title"},"上传图片",-1)),o("div",ke,[i(M,{src:Ke.value,fit:"cover","preview-src-list":[Ke.value],class:"review-preview-image"},null,8,["src","preview-src-list"])])])):r("",!0),a[23]||(a[23]=o("div",{class:"form-item"},[o("div",{class:"upload-area"})],-1)),a[24]||(a[24]=o("div",{class:"actions"},null,-1))]),a[30]||(a[30]=o("div",{class:"resizer",style:{width:"4px",background:"#e4e7ed"}},null,-1)),o("div",Te,[o("div",Ie,[o("div",qe,[a[27]||(a[27]=o("span",null,"内容审核结果详情：",-1)),Object.keys(da.value).length>0?($(),n("div",$e,[a[26]||(a[26]=o("div",{class:"task-selector-title"},"请选择任务ID：",-1)),o("div",xe,[($(!0),n(p,null,w(Object.keys(da.value).sort(((e,a)=>{const l=da.value[e].timestamp||0;return(da.value[a].timestamp||0)-l})),(e=>($(),k(Ae,{key:e,type:ga.value===e?"primary":"info",size:"small",effect:"light",class:"task-tag",onClick:a=>Ya(e)},{default:v((()=>[o("span",null,"任务 "+d(e.substring(0,8))+"...",1),da.value[e]?($(),k(ze,{key:0,value:Ua(e),class:"task-badge",type:"warning"},null,8,["value"])):r("",!0)])),_:2},1032,["type","onClick"])))),128))]),ga.value?($(),n("div",Se,[a[25]||(a[25]=f(" 当前选中: ")),o("span",Me,"任务 "+d(ga.value.substring(0,8))+"...",1),da.value[ga.value]?($(),n("span",De,[i(Ae,{size:"small",type:"danger",effect:"plain"},{default:v((()=>[f("违禁词: "+d(da.value[ga.value].violations.length||0),1)])),_:1}),i(Ae,{size:"small",type:"warning",effect:"plain"},{default:v((()=>[f("优化建议: "+d(da.value[ga.value].contentreview.length||0),1)])),_:1})])):r("",!0)])):r("",!0)])):r("",!0)]),o("div",Ee,[a[28]||(a[28]=o("h3",{class:"table-title"},"违禁词检测",-1)),i(ea,{data:ga.value&&da.value[ga.value]?da.value[ga.value].violations:va.value,border:""},{default:v((()=>[i(Ze,{label:"有问题文本",align:"center"},{default:v((e=>[f(d(e.row.problemText||e.row.original||""),1)])),_:1}),i(Ze,{label:"类型",align:"center"},{default:v((e=>[f(d(e.row.type||e.row.category||e.row.source||""),1)])),_:1}),i(Ze,{label:"原因",align:"center"},{default:v((e=>[f(d(e.row.reason||e.row.source||""),1)])),_:1}),i(Ze,{label:"建议修改内容",align:"center"},{default:v((e=>[f(d(e.row.suggestion||e.row.suggested||""),1)])),_:1})])),_:1},8,["data"]),ga.value&&0===(null==(l=null==(e=da.value[ga.value])?void 0:e.violations)?void 0:l.length)||!ga.value&&0===va.value.length?($(),n("div",je," 暂无违禁词检测数据 ")):r("",!0)]),o("div",Oe,[a[29]||(a[29]=o("h3",{class:"table-title"},"内容智能优化",-1)),i(ea,{data:ga.value&&da.value[ga.value]?da.value[ga.value].contentreview:ca.value,border:""},{default:v((()=>[i(Ze,{label:"有问题文本",align:"center"},{default:v((e=>[f(d(e.row.problemText||e.row.sentence||""),1)])),_:1}),i(Ze,{label:"类型",align:"center"},{default:v((e=>[f(d(e.row.type||e.row.reason||""),1)])),_:1}),i(Ze,{label:"原因",align:"center"},{default:v((e=>[f(d(e.row.reason||e.row.source||""),1)])),_:1}),i(Ze,{label:"建议修改内容",align:"center"},{default:v((e=>[f(d(e.row.suggestion||e.row.modification_suggestion||""),1)])),_:1})])),_:1},8,["data"]),ga.value&&0===(null==(s=null==(t=da.value[ga.value])?void 0:t.contentreview)?void 0:s.length)||!ga.value&&0===ca.value.length?($(),n("div",Re," 暂无内容优化数据 ")):r("",!0)])])])])])]})),_:1}),i(Ce,{label:"历史记录",name:"history"},{default:v((()=>[i(D,{ref_key:"historyListRef",ref:Qe},null,512)])),_:1})])),_:1},8,["modelValue"])])])}}},[["__scopeId","data-v-5eb19be3"]]);export{Ae as default};
