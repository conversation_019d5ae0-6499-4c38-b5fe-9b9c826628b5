import{p as e,r as t,f as a,E as i,a as _,c as d,o as n,h as l,g,n as o,w as r,d as s,F as p,i as c,b as m,t as u,y,A as b}from"./index-BE6Fh1xm.js";import{_ as f}from"./_plugin-vue_export-helper-GSmkUi5K.js";const h={class:"target-section"},v={class:"section-header"},x={class:"target-cards"},z={class:"card-header"},w={class:"card-actions"},V={class:"form-row"},U={key:3,class:"form-row"},k={key:4,class:"form-row"},E={class:"budget-section"},C={key:1,class:"form-row"},S=f({__name:"target",props:{targetData:Object,biddingStrategies:Array,isEdit:{type:Boolean,default:!1}},emits:["update:bidding-strategies"],setup(f,{expose:S,emit:q}){const $=f,j=q;e((()=>{$.biddingStrategies&&$.biddingStrategies.length>0&&(A.value=$.biddingStrategies.map((e=>({bidding_strategy:e.bidding_strategy||3,limit_day_budget:e.limit_day_budget||0,campaign_day_budget:e.campaign_day_budget||0,time_period_type:e.time_period_type||0,optimize_target:e.optimize_target||0,constraint_type:e.constraint_type??101,smart_switch:e.smart_switch||0,search_flag:e.search_flag||0,target_extension_switch:e.target_extension_switch||0,search_bid_ratio:e.search_bid_ratio||1.2,pacing_mode:e.pacing_mode||2,budget_type:1===e.limit_day_budget?1:0,intelligent_expansion:e.intelligent_expansion||0,conversion_platform:"taolian"}))))})),a((()=>$.biddingStrategies),(e=>{e&&e.length>0&&(A.value=e.map((e=>({bidding_strategy:e.bidding_strategy||3,limit_day_budget:e.limit_day_budget||0,campaign_day_budget:e.campaign_day_budget||0,time_period_type:e.time_period_type||0,optimize_target:e.optimize_target||0,constraint_type:e.constraint_type??101,smart_switch:e.smart_switch||0,search_flag:e.search_flag||0,target_extension_switch:e.target_extension_switch||0,search_bid_ratio:e.search_bid_ratio||1.2,pacing_mode:e.pacing_mode||2,budget_type:1===e.limit_day_budget?1:0,intelligent_expansion:e.intelligent_expansion||0,conversion_platform:"taolian"}))))}),{deep:!0,immediate:!0});const A=t([{bidding_strategy:3,limit_day_budget:1,campaign_day_budget:0,time_period_type:0,optimize_target:0,constraint_type:101,smart_switch:0,search_flag:0,target_extension_switch:0,search_bid_ratio:1.2,pacing_mode:2,budget_type:0,intelligent_expansion:0,conversion_platform:"taolian"}]),B=()=>{A.value.push({bidding_strategy:3,limit_day_budget:1,campaign_day_budget:0,time_period_type:0,optimize_target:0,constraint_type:101,smart_switch:0,pacing_mode:2,search_flag:0,target_extension_switch:0,search_bid_ratio:1.2,budget_type:0,intelligent_expansion:0,conversion_platform:"taolian"})},D=()=>{const e=[];for(let t=0;t<A.value.length;t++)for(let a=t+1;a<A.value.length;a++)F(A.value[t],A.value[a])&&e.push({index1:t+1,index2:a+1});if(e.length>0){let t="发现重复的目标和出价方式：";return e.forEach((e=>{t+=`\n第${e.index1}条和第${e.index2}条数据重复`})),i.error(t),!1}return!0},F=(e,t)=>e.optimize_target===t.optimize_target&&e.budget_type===t.budget_type&&e.time_period_type===t.time_period_type&&e.smart_switch===t.smart_switch&&e.pacing_mode===t.pacing_mode&&e.intelligent_expansion===t.intelligent_expansion&&e.limit_day_budget===t.limit_day_budget&&e.bidding_strategy===t.bidding_strategy&&e.constraint_type===t.constraint_type;a(A,(e=>{e.forEach((e=>{0===e.budget_type?(e.campaign_day_budget=0,e.limit_day_budget=0,e.pacing_mode=2):0===e.campaign_day_budget&&(e.campaign_day_budget=100,e.limit_day_budget=1)}))}),{deep:!0}),a(A,(e=>{const t=e.map((e=>{const t={bidding_strategy:e.bidding_strategy,limit_day_budget:1===e.budget_type?1:0,smart_switch:e.smart_switch,time_period_type:e.time_period_type,optimize_target:e.optimize_target,intelligent_expansion:e.intelligent_expansion};return 1===e.budget_type&&(t.pacing_mode=e.pacing_mode),7===e.bidding_strategy&&(t.constraint_type=e.constraint_type),1===e.budget_type?{...t,campaign_day_budget:e.campaign_day_budget}:(2!==e.bidding_strategy&&4!==e.bidding_strategy&&(7!==e.bidding_strategy||0!==e.constraint_type&&1!==e.constraint_type)||(t.search_flag=e.search_flag,1===e.search_flag&&(t.search_bid_ratio=e.search_bid_ratio,t.target_extension_switch=e.target_extension_switch)),18===e.optimize_target&&(t.conversion_platform=e.conversion_platform),t)}));j("update:bidding-strategies",t)}),{deep:!0});const I=e=>0!==e.optimize_target,M=e=>101===e.constraint_type?3:7,O=e=>{1===e.constraint_type||0===e.constraint_type?e.bidding_strategy=7:101===e.constraint_type&&(e.bidding_strategy=3)};a(A,(e=>{e.forEach((e=>{2!==e.bidding_strategy&&O(e)}))}),{deep:!0,immediate:!0});a(A,((e,t)=>{t&&e.forEach(((e,a)=>{const i=t[a];i&&e.optimize_target!==i.optimize_target&&(e=>{const t=e.optimize_target;e.search_flag=0,e.search_bid_ratio=1.2,e.target_extension_switch=0,e.intelligent_expansion=0,e.time_period_type=0,e.smart_switch=0,0===t||1===t?(e.bidding_strategy=3,e.constraint_type=101,e.budget_type=1,e.campaign_day_budget=100,e.limit_day_budget=1,e.pacing_mode=1):18===t&&(e.bidding_strategy=3,e.constraint_type=101,e.budget_type=1,e.campaign_day_budget=100,e.limit_day_budget=1,e.pacing_mode=1,e.conversion_platform="taolian")})(e)}))}),{deep:!0}),a(A,(e=>{e.forEach((e=>{0!==e.optimize_target&&2===e.bidding_strategy&&(e.bidding_strategy=3),7===e.bidding_strategy&&0!==e.optimize_target&&1!==e.optimize_target&&18!==e.optimize_target&&(e.bidding_strategy=3),7===e.bidding_strategy&&0!==e.optimize_target&&1!==e.optimize_target&&18!==e.optimize_target&&(e.constraint_type=0),T(e)&&0===e.budget_type&&(e.budget_type=1,e.campaign_day_budget=e.campaign_day_budget||100,e.limit_day_budget=1),G(e)&&2===e.pacing_mode&&(e.pacing_mode=1)}))}),{deep:!0}),a(A,(e=>{e.forEach((e=>{3===e.bidding_strategy&&101===e.constraint_type&&(e.pacing_mode=1)}))}),{deep:!0}),a(A,(e=>{e.forEach((e=>{T(e)&&0===e.budget_type&&(e.budget_type=1,0===e.campaign_day_budget&&(e.campaign_day_budget=100),e.limit_day_budget=1)}))}),{deep:!0}),a(A,(e=>{e.forEach((e=>{T(e)&&0===e.budget_type&&(e.budget_type=1,0===e.campaign_day_budget&&(e.campaign_day_budget=100),e.limit_day_budget=1)}))}),{deep:!0,immediate:!0}),a(A,(e=>{e.forEach((e=>{G(e)&&2===e.pacing_mode&&(e.pacing_mode=1)}))}),{deep:!0,immediate:!0});const T=e=>0===e.optimize_target&&3===e.bidding_strategy&&101===e.constraint_type||1===e.optimize_target&&3===e.bidding_strategy&&101===e.constraint_type||18===e.optimize_target&&3===e.bidding_strategy&&101===e.constraint_type,G=e=>3===e.bidding_strategy&&101===e.constraint_type||1===e.optimize_target&&3===e.bidding_strategy||18===e.optimize_target&&3===e.bidding_strategy;return S({targetCards:A,checkDuplicateTargets:D,validate:()=>{for(let e=0;e<A.value.length;e++){const t=A.value[e];if(null===t.optimize_target||void 0===t.optimize_target)return i.error(`第${e+1}个目标和出价方式的推广目标不能为空`),!1;if(1===t.budget_type&&(!t.campaign_day_budget||t.campaign_day_budget<100||t.campaign_day_budget>999999))return i.error(`第${e+1}个目标和出价方式的预算金额必须在100-999999元之间`),!1}return D()},getBiddingStrategies:()=>A.value.map((e=>{const t={bidding_strategy:e.bidding_strategy,limit_day_budget:1===e.budget_type?1:0,smart_switch:e.smart_switch||0,time_period_type:e.time_period_type||0,optimize_target:e.optimize_target,intelligent_expansion:e.intelligent_expansion||0};return 1===e.budget_type&&(t.pacing_mode=e.pacing_mode||1),7===e.bidding_strategy&&(t.constraint_type=e.constraint_type),1===e.budget_type&&(t.campaign_day_budget=e.campaign_day_budget),2!==e.bidding_strategy&&4!==e.bidding_strategy&&(7!==e.bidding_strategy||0!==e.constraint_type&&1!==e.constraint_type)||(t.search_flag=e.search_flag,1===e.search_flag&&(t.search_bid_ratio=e.search_bid_ratio,t.target_extension_switch=e.target_extension_switch)),18===e.optimize_target&&(t.conversion_platform=e.conversion_platform),t}))}),(e,t)=>{const a=_("el-button"),S=_("el-icon"),q=_("el-tooltip"),$=_("el-option"),j=_("el-select"),D=_("el-form-item"),F=_("el-radio"),H=_("el-radio-group"),J=_("el-switch"),K=_("el-input-number"),L=_("el-input"),N=_("el-form");return n(),d("div",h,[l("div",v,[t[1]||(t[1]=l("h2",null,"目标和出价方式及预算组合",-1)),f.isEdit?o("",!0):(n(),g(a,{key:0,type:"primary",onClick:B},{default:r((()=>t[0]||(t[0]=[s("添加")]))),_:1}))]),l("div",x,[(n(!0),d(p,null,c(A.value,((e,_)=>(n(),d("div",{key:_,class:"target-card"},[l("div",z,[l("span",null,"目标和出价方式及预算"+u(_+1),1),l("div",w,[!f.isEdit&&A.value.length>1?(n(),g(a,{key:0,type:"danger",link:"",onClick:e=>(e=>{A.value.length<=1?i.warning("至少需要保留一个目标和出价方式"):A.value.splice(e,1)})(_)},{default:r((()=>t[2]||(t[2]=[s("删除")]))),_:2},1032,["onClick"])):o("",!0)])]),m(N,{model:e,"label-width":"140px",class:"target-form"},{default:r((()=>[l("div",V,[m(D,{label:"推广目标",required:""},{label:r((()=>[t[3]||(t[3]=s(" 推广目标 ")),m(q,{content:"推广目标是指将广告投放给更可能获得目标成效的用户。例如：推广目标为互动量，将展示给最可能与广告产生互动行为的用户",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(j,{modelValue:e.optimize_target,"onUpdate:modelValue":t=>e.optimize_target=t,placeholder:"选择推广目标",onChange:t=>(e=>{0===e.optimize_target?(e.bidding_strategy=3,e.limit_day_budget=1,e.campaign_day_budget=100,e.time_period_type=0,e.optimize_target=0,e.constraint_type=101,e.smart_switch=0,e.search_flag=0,e.target_extension_switch=0,e.search_bid_ratio=1.2,e.pacing_mode=1,e.budget_type=1,e.intelligent_expansion=0):1===e.optimize_target?(e.bidding_strategy=3,e.limit_day_budget=1,e.campaign_day_budget=100,e.time_period_type=0,e.optimize_target=1,e.constraint_type=101,e.smart_switch=0,e.search_flag=0,e.target_extension_switch=0,e.search_bid_ratio=1.2,e.pacing_mode=1,e.budget_type=1,e.intelligent_expansion=0):18===e.optimize_target&&(e.bidding_strategy=7,e.limit_day_budget=1,e.campaign_day_budget=100,e.time_period_type=0,e.optimize_target=18,e.constraint_type=101,e.smart_switch=0,e.search_flag=0,e.target_extension_switch=0,e.search_bid_ratio=1.2,e.pacing_mode=1,e.budget_type=1,e.intelligent_expansion=0,e.conversion_platform="taolian")})(e)},{default:r((()=>[m($,{label:"点击量",value:0}),m($,{label:"互动量",value:1})])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1024)]),18===e.optimize_target?(n(),g(D,{key:0,label:"转化平台"},{label:r((()=>[t[4]||(t[4]=s(" 转化平台 ")),m(q,{content:"转化标的用于追踪广告投放效果，当用户完成您设定的转化标的时会记录一次转化，您可以在报表中查看转化相关数据。",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(j,{modelValue:e.conversion_platform,"onUpdate:modelValue":t=>e.conversion_platform=t,disabled:""},{default:r((()=>[m($,{label:"淘联-小红星",value:"taolian"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1024)):o("",!0),m(D,{label:"出价方式",required:""},{label:r((()=>[t[5]||(t[5]=s(" 出价方式 ")),m(q,{content:"出价方式会影响您的投放成本和推广目标量级。请您根据投放诉求，选择合适的出价方式。",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(H,{modelValue:e.bidding_strategy,"onUpdate:modelValue":t=>e.bidding_strategy=t},{default:r((()=>[m(F,{label:M(e)},{default:r((()=>t[6]||(t[6]=[s("自动出价")]))),_:2},1032,["label"]),m(F,{label:2,disabled:I(e)},{default:r((()=>t[7]||(t[7]=[s("手动出价")]))),_:2},1032,["disabled"])])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1024),2!==e.bidding_strategy?(n(),g(D,{key:1,label:"成本控制方式",required:""},{label:r((()=>[t[8]||(t[8]=s(" 成本控制方式 ")),m(q,{content:"成本控制方式会影响您单元层级的出价单位，并按照计划天级别帮助达成目标最大化。例如：选择点击成本控制，您将在单元层级设置您点击成本，同时系统将尽力控制点击成本达成。",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(H,{modelValue:e.constraint_type,"onUpdate:modelValue":t=>e.constraint_type=t,onChange:t=>O(e)},{default:r((()=>[m(F,{label:101},{default:r((()=>t[9]||(t[9]=[s("自动控制")]))),_:1}),m(F,{label:0},{default:r((()=>t[10]||(t[10]=[s("点击成本控制")]))),_:1}),1===e.optimize_target?(n(),g(F,{key:0,label:1},{default:r((()=>t[11]||(t[11]=[s("互动成本控制")]))),_:1})):o("",!0)])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:2},1024)):o("",!0),2===e.bidding_strategy||4===e.bidding_strategy||7===e.bidding_strategy&&(0===e.constraint_type||1===e.constraint_type)?(n(),g(D,{key:2,label:"搜索快投"},{label:r((()=>[t[12]||(t[12]=s(" 搜索快投 ")),m(q,{content:"出价系数只针对搜索场域提高出价，如计划上每个出价为1元，出价系数为1.2，则在搜索场域下每个出价为1.2元",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(J,{modelValue:e.search_flag,"onUpdate:modelValue":t=>e.search_flag=t,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)):o("",!0),2!==e.bidding_strategy&&4!==e.bidding_strategy&&(7!==e.bidding_strategy||0!==e.constraint_type&&1!==e.constraint_type)||1!==e.search_flag?o("",!0):(n(),d("div",U,[m(D,{label:"出价系数"},{label:r((()=>[t[13]||(t[13]=s(" 出价系数 ")),m(q,{content:"出价系数只针对搜索场域提高出价，如计划上每个出价为1元，出价系数为1.2，则在搜索场域下每个出价为1.2元",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(K,{modelValue:e.search_bid_ratio,"onUpdate:modelValue":t=>e.search_bid_ratio=t,min:1,max:10,precision:1,step:.1,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),2!==e.bidding_strategy&&4!==e.bidding_strategy&&(3!==e.bidding_strategy&&7!==e.bidding_strategy||0!==e.constraint_type&&1!==e.constraint_type)||1!==e.search_flag?o("",!0):(n(),d("div",k,[m(D,{label:"定向拓展"},{label:r((()=>[t[14]||(t[14]=s(" 定向拓展 ")),m(q,{content:"开启定向拓展后搜索场景下性别和年龄定向设置为不限，拿量能力强",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(J,{modelValue:e.target_extension_switch,"onUpdate:modelValue":t=>e.target_extension_switch=t,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),l("div",E,[t[24]||(t[24]=l("div",{class:"section-title"},"计划预算",-1)),m(D,{label:"预算类型",required:""},{label:r((()=>t[15]||(t[15]=[s(" 预算类型 ")]))),default:r((()=>[m(H,{modelValue:e.budget_type,"onUpdate:modelValue":t=>e.budget_type=t},{default:r((()=>[m(F,{label:0,disabled:T(e)},{default:r((()=>t[16]||(t[16]=[s("不限预算")]))),_:2},1032,["disabled"]),m(F,{label:1},{default:r((()=>t[17]||(t[17]=[s("指定预算")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1024),1===e.budget_type?(n(),g(D,{key:0,label:"预算金额",required:""},{label:r((()=>[t[18]||(t[18]=s(" 预算金额 ")),m(q,{content:"设置当前计划的每天消耗上限。自动出价模式下，系统将在您设置的预算下进行最大化推广目标达成",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(L,{modelValue:e.campaign_day_budget,"onUpdate:modelValue":t=>e.campaign_day_budget=t,modelModifiers:{number:!0},type:"number",placeholder:"请输入,默认为最低预算"},{append:r((()=>t[19]||(t[19]=[s("元")]))),_:2},1032,["modelValue","onUpdate:modelValue"]),t[20]||(t[20]=l("div",{class:"budget-tip"},"预算不少于100元，不超过999999元",-1))])),_:2},1024)):o("",!0),1===e.budget_type?(n(),d("div",C,[m(D,{label:"消耗速率"},{label:r((()=>[t[21]||(t[21]=s(" 消耗速率 ")),m(q,{content:"加速消耗：设置当前计划的每天消耗上限。自动出价模式下，系统将在您设置的预算下进行最大化推广目标达成。均匀消耗：设置当前计划的每天消耗上限。自动出价模式下，系统将在您设置的预算下进行最大化推广目标达成",placement:"top"},{default:r((()=>[m(S,null,{default:r((()=>[m(y(b))])),_:1})])),_:1})])),default:r((()=>[m(H,{modelValue:e.pacing_mode,"onUpdate:modelValue":t=>e.pacing_mode=t},{default:r((()=>[m(F,{label:2,disabled:G(e)},{default:r((()=>t[22]||(t[22]=[s("加速消耗")]))),_:2},1032,["disabled"]),m(F,{label:1},{default:r((()=>t[23]||(t[23]=[s("均匀消耗")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:2},1024)])):o("",!0)])])),_:2},1032,["model"])])))),128))])])}}},[["__scopeId","data-v-1874a939"]]);export{S as default};
