import{aB as e,s as l,r as a,a as t,g as i,o as s,w as o,b as u,d as n,ar as p,h as d,t as r}from"./index-C2bfFjZ1.js";import{_ as c}from"./_plugin-vue_export-helper-BXFjo1rG.js";const m=async(l,a,t={},i=!0,s=".xlsx")=>{i&&e({title:"温馨提示",message:"如果数据庞大会导致下载缓慢哦，请您耐心等待！",type:"info",duration:3e3});try{const e=await l(t),i=new Blob([e]);if("msSaveOrOpenBlob"in navigator)return window.navigator.msSaveOrOpenBlob(i,a+s);const o=window.URL.createObjectURL(i),u=document.createElement("a");u.style.display="none",u.download=`${a}${s}`,u.href=o,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(o)}catch(o){}},v={class:"el-upload__tip"},f=l({name:"ImportExcel"}),w=c(l({...f,setup(l,{expose:c}){const f=a(!1),w=a(1),y=a(!1),g=a({title:"",fileSize:5,fileType:["application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]}),x=()=>{g.value.tempApi&&m(g.value.tempApi,`${g.value.title}模板`)},b=async e=>{let l=new FormData;l.append("file",e.file),l.append("isCover",f.value),await g.value.importApi(l),g.value.getTableList&&g.value.getTableList(),y.value=!1},_=l=>{const a=g.value.fileType.includes(l.type),t=l.size/1024/1024<g.value.fileSize;return a||e({title:"温馨提示",message:"上传文件只能是 xls / xlsx 格式！",type:"warning"}),t||setTimeout((()=>{e({title:"温馨提示",message:`上传文件大小不能超过 ${g.value.fileSize}MB！`,type:"warning"})}),0),a&&t},h=()=>{e({title:"温馨提示",message:"最多只能上传一个文件！",type:"warning"})},$=()=>{e({title:"温馨提示",message:`批量添加${g.value.title}失败，请您重新上传！`,type:"error"})},L=()=>{e({title:"温馨提示",message:`批量添加${g.value.title}成功！`,type:"success"})};return c({acceptParams:e=>{g.value={...g.value,...e},y.value=!0}}),(e,l)=>{const a=t("el-button"),c=t("el-form-item"),m=t("upload-filled"),O=t("el-icon"),S=t("el-upload"),T=t("el-switch"),U=t("el-form"),V=t("el-dialog");return s(),i(V,{modelValue:y.value,"onUpdate:modelValue":l[1]||(l[1]=e=>y.value=e),title:`批量添加${g.value.title}`,"destroy-on-close":!0,width:"580px",draggable:""},{default:o((()=>[u(U,{class:"drawer-multiColumn-form","label-width":"100px"},{default:o((()=>[u(c,{label:"模板下载 :"},{default:o((()=>[u(a,{type:"primary",icon:"Download",onClick:x},{default:o((()=>l[2]||(l[2]=[n(" 点击下载 ")]))),_:1})])),_:1}),u(c,{label:"文件上传 :"},{default:o((()=>[u(S,{action:"#",class:"upload",drag:!0,limit:w.value,multiple:!0,"show-file-list":!0,"http-request":b,"before-upload":_,"on-exceed":h,"on-success":L,"on-error":$,accept:g.value.fileType.join(",")},{tip:o((()=>[p(e.$slots,"tip",{},(()=>[d("div",v,"请上传 .xls , .xlsx 标准格式文件，文件最大为 "+r(g.value.fileSize)+"M",1)]),!0)])),default:o((()=>[p(e.$slots,"empty",{},(()=>[u(O,{class:"el-icon--upload"},{default:o((()=>[u(m)])),_:1}),l[3]||(l[3]=d("div",{class:"el-upload__text"},[n("将文件拖到此处，或"),d("em",null,"点击上传")],-1))]),!0)])),_:3},8,["limit","accept"])])),_:3}),u(c,{label:"数据覆盖 :"},{default:o((()=>[u(T,{modelValue:f.value,"onUpdate:modelValue":l[0]||(l[0]=e=>f.value=e)},null,8,["modelValue"])])),_:1})])),_:3})])),_:3},8,["modelValue","title"])}}}),[["__scopeId","data-v-02e2b6f1"]]);export{w as I,m as u};
