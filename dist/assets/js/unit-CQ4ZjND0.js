import{a as e,o as t,b as a}from"./temp-api-B36KNeYM.js";import{l}from"./lodash-BOiQUzk8.js";import o from"./AnchorNavigation-DOB1pEjt.js";import{s as r,r as n,a6 as d,f as i,p as s,E as c,q as u,e as g,a as v,Q as p,c as _,o as m,b as w,h as f,w as y,t as h,F as k,i as D,n as C,d as b,g as A,y as I,m as V,z as T}from"./index-BE6Fh1xm.js";import{_ as N}from"./_plugin-vue_export-helper-GSmkUi5K.js";import"./setupRequest-C4Opp8Oc.js";const x={class:"unit-section"},R={id:"notes",class:"content-section",ref:"notesRef"},S={class:"section-content"},U={class:"note-table"},E={class:"note-description"},K={class:"tag-spu"},z={id:"placement",class:"content-section",ref:"targetingRef"},L={class:"section-content"},P={class:"target-cards"},j={class:"card-content"},F={class:"target-section"},$={class:"section-right"},M={class:"target-section"},B={class:"section-right"},O={class:"type-desc"},J={key:0,class:"target-section",style:{"flex-direction":"column"}},q={class:"section-right target-details"},Q={style:{"margin-bottom":"10px",display:"flex","align-items":"center","justify-content":"space-between"}},G={style:{"margin-bottom":"10px",display:"flex","align-items":"center","justify-content":"space-between"}},H={class:"keyword-section"},W={class:"keyword-search"},X={class:"keyword-content"},Y={class:"recommend-keywords"},Z={class:"section-header"},ee={class:"keyword-table"},te={class:"selected-keywords"},ae={class:"section-header"},le={class:"keyword-list"},oe={class:"keyword-info"},re={class:"keyword"},ne={key:1,class:"empty-text"},de={class:"crowd-section"},ie={class:"crowd-search"},se={class:"crowd-content"},ce={class:"recommend-crowds"},ue={class:"section-header"},ge={class:"crowd-table"},ve={class:"selected-crowds"},pe={class:"section-header"},_e={class:"crowd-list"},me={class:"crowd-info"},we={class:"crowd-name"},fe={class:"crowd-id"},ye={key:1,class:"empty-text"},he={class:"unit-section-header"},ke={class:"target-section"},De={class:"section-right target-details"},Ce={class:"detail-item"},be={class:"detail-item",style:{display:"flex","align-items":"self-start"}},Ae={class:"location-section"},Ie={key:0},Ve={class:"location-mode"},Te={class:"location-search"},Ne={class:"location-selector"},xe={class:"cascader-wrapper"},Re={key:0},Se={key:1,class:"empty-data"},Ue={class:"selected-locations"},Ee={class:"selected-header"},Ke={class:"selected-content"},ze={class:"location-row"},Le={key:1,class:"empty-text"},Pe={class:"detail-item"},je={class:"detail-item"},Fe={id:"targeting",class:"content-section",ref:"biddingRef"},$e={class:"section-content"},Me={class:"bid-range"},Be={class:"drawer-header"},Oe={class:"drawer-tabs"},Je=["onClick"],qe={class:"search-bar"},Qe={class:"search-input"},Ge={class:"search-actions"},He={class:"tab-content"},We={class:"search-results"},Xe={class:"note-description"},Ye={class:"tag-spu"},Ze={class:"pagination-container"},et={class:"dialog-content"},tt={class:"flex items-center"},at={style:{color:"#999","font-size":"13px","margin-left":"10px"}},lt={class:"dialog-footer"},ot=N(r({__name:"unit",props:{unitData:{},savedState:{},campaignData:{},rowData:{},onSaveData:{type:Function}},emits:["update:unit-data","update:rowData","saveComplete"],setup(r,{expose:N,emit:ot}){var rt,nt,dt,it,st,ct,ut,gt,vt,pt,_t,mt,wt,ft,yt,ht,kt,Dt,Ct,bt,At,It,Vt,Tt,Nt,xt,Rt,St,Ut,Et,Kt,zt,Lt,Pt,jt,Ft,$t,Mt,Bt,Ot,Jt,qt,Qt,Gt,Ht,Wt,Xt,Yt,Zt,ea,ta,aa,la,oa,ra,na,da,ia,sa,ca,ua,ga,va,pa,_a,ma,wa,fa,ya,ha,ka,Da,Ca,ba,Aa,Ia,Va,Ta,Na,xa,Ra,Sa,Ua,Ea,Ka;const za=e=>{switch(Number(e)||1){case 1:default:return"不限人群，向小红书域内全部人群进行投放";case 2:return"系统智能寻找相似人群，精准触达广告主目标人群";case 3:return"通过关键词定向触达潜在用户"}},La=r,Pa=ot,ja=n((null==(nt=null==(rt=La.rowData)?void 0:rt.creativity)?void 0:nt.note)?[{noteId:La.rowData.creativity.note.noteId,image:La.rowData.creativity.note.image,desc:La.rowData.creativity.note.desc,author:La.rowData.creativity.note.author,noteType:La.rowData.creativity.note.noteType,createTime:La.rowData.creativity.note.createTime}]:(null==(it=null==(dt=La.campaignData)?void 0:dt.creativity)?void 0:it.note)?[{noteId:La.campaignData.creativity.note.noteId,image:La.campaignData.creativity.note.image,desc:La.campaignData.creativity.note.desc,author:La.campaignData.creativity.note.author,noteType:La.campaignData.creativity.note.noteType,createTime:La.campaignData.creativity.note.createTime}]:(null==(st=La.savedState)?void 0:st.noteList)||[]),Fa=d({note_ids:(null==(ut=null==(ct=La.rowData)?void 0:ct.unit)?void 0:ut.note_ids)||(null==(vt=null==(gt=La.campaignData)?void 0:gt.unit)?void 0:vt.note_ids)||(null==(pt=La.unitData)?void 0:pt.note_ids)||[],notes:(null==(_t=La.savedState)?void 0:_t.noteList)||[],target_cards:(null==(mt=La.savedState)?void 0:mt.targetCards)||[],bid_form:(null==(wt=La.savedState)?void 0:wt.bidForm)||{},event_bid:(null==(yt=null==(ft=La.rowData)?void 0:ft.unit)?void 0:yt.event_bid)||(null==(kt=null==(ht=La.campaignData)?void 0:ht.unit)?void 0:kt.event_bid)||(null==(Dt=La.unitData)?void 0:Dt.event_bid)||0,target_config:(null==(bt=null==(Ct=La.rowData)?void 0:Ct.unit)?void 0:bt.target_config)||(null==(It=null==(At=La.campaignData)?void 0:At.unit)?void 0:It.target_config)||(null==(Vt=La.unitData)?void 0:Vt.target_config)||{},noteList:(null==(Tt=La.savedState)?void 0:Tt.noteList)||[],targetCards:(null==(Nt=La.savedState)?void 0:Nt.targetCards)||[],unit_name:(null==(Rt=null==(xt=La.rowData)?void 0:xt.unit)?void 0:Rt.unit_name)||(null==(Ut=null==(St=La.campaignData)?void 0:St.unit)?void 0:Ut.unit_name)||(null==(Et=La.unitData)?void 0:Et.unit_name)||"",advertiser_id:(null==(zt=null==(Kt=La.rowData)?void 0:Kt.campaign)?void 0:zt.advertiser_id)||(null==(Pt=null==(Lt=La.campaignData)?void 0:Lt.campaign)?void 0:Pt.advertiser_id)||(null==(jt=La.unitData)?void 0:jt.advertiser_id)||""}),$a=n(!1),Ma=n(""),Ba=n([]),Oa=n([]),Ja=n([]),qa=n(null),Qa=d({gender:(null==(Mt=null==($t=null==(Ft=La.rowData)?void 0:Ft.unit)?void 0:$t.target_config)?void 0:Mt.target_gender)||(null==(Jt=null==(Ot=null==(Bt=La.campaignData)?void 0:Bt.unit)?void 0:Ot.target_config)?void 0:Jt.target_gender)||(null==(Qt=null==(qt=La.unitData)?void 0:qt.target_config)?void 0:Qt.target_gender)||"all",age:(null==(Wt=null==(Ht=null==(Gt=La.rowData)?void 0:Gt.unit)?void 0:Ht.target_config)?void 0:Wt.target_age)||(null==(Zt=null==(Yt=null==(Xt=La.campaignData)?void 0:Xt.unit)?void 0:Yt.target_config)?void 0:Zt.target_age)||(null==(ta=null==(ea=La.unitData)?void 0:ea.target_config)?void 0:ta.target_age)||"all",location:"all"===(null==(oa=null==(la=null==(aa=La.rowData)?void 0:aa.unit)?void 0:la.target_config)?void 0:oa.target_city)||"all"===(null==(da=null==(na=null==(ra=La.campaignData)?void 0:ra.unit)?void 0:na.target_config)?void 0:da.target_city)||"all"===(null==(sa=null==(ia=La.unitData)?void 0:ia.target_config)?void 0:sa.target_city)?"all":"custom",interests:[],crowds:(null==(pa=null==(va=null==(ga=null==(ua=null==(ca=La.rowData)?void 0:ca.unit)?void 0:ua.target_config)?void 0:ga.crowd_target)?void 0:va.crowd_pkg)?void 0:pa.map((e=>e.value)))||(null==(ya=null==(fa=null==(wa=null==(ma=null==(_a=La.campaignData)?void 0:_a.unit)?void 0:ma.target_config)?void 0:wa.crowd_target)?void 0:fa.crowd_pkg)?void 0:ya.map((e=>e.value)))||(null==(Ca=null==(Da=null==(ka=null==(ha=La.unitData)?void 0:ha.target_config)?void 0:ka.crowd_target)?void 0:Da.crowd_pkg)?void 0:Ca.map((e=>e.value)))||[]}),Ga=[{id:"notes",title:"推广笔记"},{id:"placement",title:"定向组合"},{id:"targeting",title:"出价"}],Ha=d(La.savedState?{...La.savedState.bidForm}:{bid:((null==(Aa=null==(ba=La.rowData)?void 0:ba.unit)?void 0:Aa.event_bid)||(null==(Va=null==(Ia=La.campaignData)?void 0:Ia.unit)?void 0:Va.event_bid)||(null==(Ta=La.unitData)?void 0:Ta.event_bid)||0)/100,smartBid:!1,minBid:.1,maxBid:100}),Wa=d({currentPage:1,pageSize:10,total:0}),Xa=n(!1),Ya=async()=>{var t,a,l,o,r,n,d;Xa.value=!0;try{let i=Ma.value;Ma.value&&Ma.value.match(/[,\s\n]+/)&&(i=Ma.value.split(/[,\s\n]+/).map((e=>e.trim())).filter(Boolean));const s=await e({keyword:i,advertiser_id:(null==(a=null==(t=La.rowData)?void 0:t.campaign)?void 0:a.advertiser_id)||(null==(o=null==(l=La.campaignData)?void 0:l.campaign)?void 0:o.advertiser_id)||(null==(n=null==(r=La.campaignData)?void 0:r.plan)?void 0:n.advertiser_id),page:Wa.currentPage,page_size:Wa.pageSize,order_field:"",order_type:"desc",note_content_type:0,placement_type:0,spu_id:"",filter_taobao:0,market_target:0,spu_type:0,note_type:nl[sl.value],base_only:!1});if(990===s.code&&(null==(d=s.data)?void 0:d.data)){const e=(s.data.data.notes||[]).map((e=>{var t;return{noteId:e.note_id,image:e.image,spu:(null==(t=e.note_multi_spu_info)?void 0:t.length)||0,desc:e.desc,author:e.author,noteType:1===e.note_type?"图文笔记":"视频笔记",createTime:new Date(e.create_time).toLocaleString()}}));Ba.value=e;const t=s.data.data.total;return Wa.total=t||0,{data:e}}return{data:[]}}catch(i){return c.error("搜索笔记失败"),{data:[]}}finally{Xa.value=!1}},Za=e=>{Wa.currentPage=e,Ya()},el=e=>{Wa.pageSize=e,Wa.currentPage=1,Ya()},tl=async()=>{Wa.currentPage=1;const e=Ma.value.split(/[,\s\n]+/).map((e=>e.trim())).filter(Boolean);if(!e.length)return void c.warning("请输入有效的笔记ID");await Ya();const t=Ba.value.filter((t=>e.includes(t.noteId)));Oa.value=t};i([Qa,Ha],(()=>{Fa.event_bid=100*Ha.bid,Fa.target_config={...La.unitData.target_config,target_gender:Qa.gender,target_age:Qa.age,target_city:"all"===Qa.location?"all":Qa.location,crowd_target:{...La.unitData.target_config.crowd_target,crowd_pkg:Qa.crowds.map((e=>{var t;return{value:e,name:(null==(t=La.unitData.target_config.crowd_target.crowd_pkg.find((t=>t.value===e)))?void 0:t.name)||""}}))}}}),{deep:!0});const al=e=>ja.value.some((t=>t.noteId===e.noteId))||Ja.value.some((t=>t.noteId===e.noteId)),ll=e=>{Ja.value=e};s((async()=>{var e,t,a,l,o,r,n,d,i,g,v,p,_,m,w,f,y;if(ql()){(null==(a=null==(t=null==(e=La.rowData)?void 0:e.unit)?void 0:t.note_ids)?void 0:a.length)?await dl(La.rowData.unit.note_ids):(null==(r=null==(o=null==(l=La.campaignData)?void 0:l.unit)?void 0:o.note_ids)?void 0:r.length)?await dl(La.campaignData.unit.note_ids):(null==(d=null==(n=La.unitData)?void 0:n.note_ids)?void 0:d.length)&&await dl(La.unitData.note_ids);try{if(await Cl(),bl.value.forEach((e=>{var t,a,l,o,r,n,d,i,s,c,u,g,v,p,_,m,w,f;e.targetType=Number(e.targetType)||(null==(a=null==(t=La.rowData)?void 0:t.unit)?void 0:a.target_type)||(null==(o=null==(l=La.campaignData)?void 0:l.unit)?void 0:o.target_type)||1,e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.selectedCrowds||(e.selectedCrowds=[]),e.crowds||(e.crowds=[]),(null==(s=null==(i=null==(d=null==(n=null==(r=La.rowData)?void 0:r.unit)?void 0:n.target_config)?void 0:d.crowd_target)?void 0:i.crowd_pkg)?void 0:s.length)&&La.rowData.unit.target_config.crowd_target.crowd_pkg.forEach((t=>{e.crowd_target.crowd_pkg.some((e=>e.value===t.value))||(e.crowd_target.crowd_pkg.push({...t}),e.selectedCrowds.some((e=>e.value===t.value))||e.selectedCrowds.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))})),(null==(v=null==(g=null==(u=null==(c=La.unitData)?void 0:c.target_config)?void 0:u.crowd_target)?void 0:g.crowd_pkg)?void 0:v.length)&&La.unitData.target_config.crowd_target.crowd_pkg.forEach((t=>{e.crowd_target.crowd_pkg.some((e=>e.value===t.value))||(e.crowd_target.crowd_pkg.push({...t}),e.selectedCrowds.some((e=>e.value===t.value))||e.selectedCrowds.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))})),(null==(f=null==(w=null==(m=null==(_=null==(p=La.campaignData)?void 0:p.unit)?void 0:_.target_config)?void 0:m.crowd_target)?void 0:w.crowd_pkg)?void 0:f.length)&&La.campaignData.unit.target_config.crowd_target.crowd_pkg.forEach((t=>{e.crowd_target.crowd_pkg.some((e=>e.value===t.value))||(e.crowd_target.crowd_pkg.push({...t}),e.selectedCrowds.some((e=>e.value===t.value))||e.selectedCrowds.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))})),3===e.targetType&&ol(e),e.location.includes("custom")&&Zl(e)})),0===bl.value.length){const e=(null==(g=null==(i=La.rowData)?void 0:i.unit)?void 0:g.target_type)||(null==(p=null==(v=La.campaignData)?void 0:v.unit)?void 0:p.target_type)||1,t=(null==(m=null==(_=La.rowData)?void 0:_.unit)?void 0:m.target_config)||(null==(f=null==(w=La.campaignData)?void 0:w.unit)?void 0:f.target_config)||{},a={targetPackage:"",targetType:e,gender:[t.target_gender||"all"],location:[t.target_city||"all"],ageRanges:Hl(t.target_age||"all"),platform:[t.target_device||"all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:(null==(y=t.interest_keywords)?void 0:y.map((e=>({id:e,keyword:e}))))||[],selectedContentInterests:[],selectedShoppingInterests:[],intelligent_expansion:t.intelligent_expansion||0};3===e&&ol(a),bl.value.push(a)}}catch(h){c.error("获取定向数据失败")}await Ya(),u((()=>{bl.value.forEach((e=>{e.location.includes("custom")&&Zl(e)}))})),s((()=>{var e,t,a,l,o,r,n,d,i;null==(l=null==(a=null==(t=null==(e=La.unitData)?void 0:e.target_config)?void 0:t.crowd_target)?void 0:a.crowd_pkg)||l.length,null==(d=null==(n=null==(r=null==(o=La.savedState)?void 0:o.targetCards)?void 0:r[0])?void 0:n.selectedCrowds)||d.length,(null==(i=bl.value)?void 0:i.length)&&bl.value.forEach((e=>{var t,a;e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),(null==(t=e.selectedCrowds)?void 0:t.length)&&!e.crowd_target.crowd_pkg.length?e.crowd_target.crowd_pkg=e.selectedCrowds.map((e=>({name:e.name,value:e.value}))):!(null==(a=e.crowd_target.crowd_pkg)?void 0:a.length)||e.selectedCrowds&&e.selectedCrowds.length||(e.selectedCrowds||(e.selectedCrowds=[]),e.selectedCrowds=e.crowd_target.crowd_pkg.map((e=>({name:e.name,value:e.value}))))}))}))}else c.error("数据不完整，请检查")}));const ol=e=>{var t,a,l,o,r,n,d,i,s,c,u,g;const v=(null==(a=null==(t=La.rowData)?void 0:t.unit)?void 0:a.target_config)||(null==(o=null==(l=La.campaignData)?void 0:l.unit)?void 0:o.target_config)||(null==(r=La.unitData)?void 0:r.target_config);if(!v)return;const p=e=>{let t=[];const a=e=>{t.push(e.code),e.children&&Array.isArray(e.children)&&e.children.length>0&&e.children.forEach(a)};return e.forEach(a),t};(null==(d=null==(n=v.industry_interest_target)?void 0:n.content_interests)?void 0:d.length)>0&&(e.selectedContentInterests=p(v.industry_interest_target.content_interests)),(null==(s=null==(i=v.industry_interest_target)?void 0:i.shopping_interests)?void 0:s.length)>0&&(e.selectedShoppingInterests=p(v.industry_interest_target.shopping_interests)),(null==(c=v.interest_keywords)?void 0:c.length)>0&&(e.selectedKeywords=v.interest_keywords.map((e=>({id:e,keyword:e})))),(null==(g=null==(u=v.crowd_target)?void 0:u.crowd_pkg)?void 0:g.length)>0&&(e.selectedCrowds||(e.selectedCrowds=[]),e.crowds||(e.crowds=[]),e.selectedCrowds=[],e.crowds=[],v.crowd_target.crowd_pkg.forEach((t=>{e.selectedCrowds.push({name:t.name||"",value:t.value||""}),e.crowds.includes(t.value)||e.crowds.push(t.value)}))),e.intelligent_expansion=v.intelligent_expansion||0},rl=()=>{if(!Ja.value.length)return void c.warning("请选择要添加的笔记");const e=Ja.value.map((e=>({noteId:e.noteId,image:e.image,desc:e.desc,author:e.author,noteType:e.noteType,spu:e.spu,createTime:e.createTime}))),t=new Set(ja.value.map((e=>e.noteId))),a=e.filter((e=>!t.has(e.noteId)));0!==a.length?(ja.value=[...ja.value,...a],Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.notes=[...ja.value],c.success(`成功添加 ${a.length} 条笔记`),$a.value=!1,Ma.value="",Ba.value=[],Ja.value=[]):c.warning("所选笔记已全部添加")},nl={my:1,cooperation:2,protagonist:4,employee:6,authorized:11};async function dl(t){var a,l,o,r,n,d,i,s,u,g,v,p,_,m,w,f,y,h,k,D,C,b,A,I;try{if(null==(l=null==(a=La.rowData)?void 0:a.creativity)?void 0:l.note){const e=La.rowData.creativity.note,t={noteId:e.noteId,image:e.image,desc:e.desc,author:e.author,noteType:e.noteType,createTime:e.createTime};ja.value.some((e=>e.noteId===t.noteId))||ja.value.push(t)}else if(null==(r=null==(o=La.campaignData)?void 0:o.creativity)?void 0:r.note){const e=La.campaignData.creativity.note,t={noteId:e.noteId,image:e.image,desc:e.desc,author:e.author,noteType:e.noteType,createTime:e.createTime};ja.value.some((e=>e.noteId===t.noteId))||ja.value.push(t)}else{const a=await e({note_ids:t,advertiser_id:(null==(d=null==(n=La.rowData)?void 0:n.campaign)?void 0:d.advertiser_id)||(null==(s=null==(i=La.campaignData)?void 0:i.campaign)?void 0:s.advertiser_id)||(null==(g=null==(u=La.campaignData)?void 0:u.plan)?void 0:g.advertiser_id),order_field:"",order_type:"desc",note_content_type:0,placement_type:0,spu_id:"",filter_taobao:0,market_target:0,spu_type:0,page:1,page_size:t.length,note_type:nl[sl.value]});if(990===a.code&&(null==(v=a.data)?void 0:v.data)){const e=(a.data.data.notes||[]).map((e=>({noteId:e.note_id,image:e.cover_url,desc:e.content,author:e.author_name,noteType:nl[e.note_type],spu:e.spu_id,createTime:e.create_time}))),t=new Set(ja.value.map((e=>e.noteId))),l=e.filter((e=>!t.has(e.noteId)));ja.value=[...ja.value,...l]}}}catch(N){c.error("获取笔记详情失败")}Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.notes=[...ja.value],0===bl.value.length&&((null==(_=null==(p=La.unitData)?void 0:p.targetCards)?void 0:_.length)>0?bl.value=JSON.parse(JSON.stringify(La.unitData.targetCards)).map((e=>{var t;return{...e,targetType:Number(e.targetType)||1,ageRanges:Hl(e.target_age||(null==(t=e.target_config)?void 0:t.target_age)||"all")}})):bl.value=[Ol()]);const V=JSON.parse(JSON.stringify(bl.value)).map((e=>({...e,targetType:Number(e.targetType)||1}))),T=Fa.unit_name;Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.event_bid=100*Ha.bid,Fa.noteList=ja.value,Fa.target_cards=V,Fa.targetCards=V,Fa.bid_form={...Ha},Fa.unit_name=T,bl.value.length>0&&(Fa.target_config={...La.unitData.target_config,target_gender:(null==(m=bl.value[0])?void 0:m.gender[0])||"all",target_age:(null==(w=bl.value[0])?void 0:w.ageRanges[0])||"all",target_city:"all"===(null==(f=bl.value[0])?void 0:f.location[0])?"all":{mode:null==(y=bl.value[0])?void 0:y.locationMode,countries:null==(h=bl.value[0])?void 0:h.selectedLocations,cities:null==(k=bl.value[0])?void 0:k.selectedCities,filter:null==(D=bl.value[0])?void 0:D.locationFilter},target_device:(null==(C=bl.value[0])?void 0:C.platform[0])||"all",crowd_target:{...(null==(b=La.unitData.target_config)?void 0:b.crowd_target)||{},crowd_pkg:(null==(I=null==(A=bl.value[0])?void 0:A.crowds)?void 0:I.map((e=>{var t;return{value:e,name:(null==(t=wl.value.find((t=>t.value===e)))?void 0:t.name)||""}})))||[]}})}const il=e=>{const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})},sl=n("cooperation"),cl=[{key:"cooperation",name:"合作笔记"},{key:"my",name:"我的笔记"},{key:"authorized",name:"授权笔记"},{key:"employee",name:"员工笔记"},{key:"protagonist",name:"主理人笔记"}],ul=n("content_interests"),gl=n(!1),vl=n([]),pl=n([]),_l=n([]),ml=n([]),wl=n([]),fl=n(""),yl=n([]),hl=n(!1),kl=n({content_interests:[],shopping_interests:[]}),Dl=g((()=>{if(!kl.value||!kl.value[ul.value])return[];const e=t=>t&&Array.isArray(t)?t.map((t=>{!t.code||t.name;const a={code:t.code,name:t.name,value:t.code,label:t.name};return t.children&&Array.isArray(t.children)&&t.children.length>0&&(a.children=e(t.children)),a})):[];return e(kl.value[ul.value])}));n([]);const Cl=async()=>{var e,a,l,o,r,n,d,i,s,g,v,p,_,m,w,f,y,h,k,D,C,b,A,I,V,T,N,x;gl.value=!0;try{const c=(null==(a=null==(e=La.rowData)?void 0:e.campaign)?void 0:a.advertiser_id)||(null==(o=null==(l=La.campaignData)?void 0:l.campaign)?void 0:o.advertiser_id)||(null==(n=null==(r=La.campaignData)?void 0:r.plan)?void 0:n.advertiser_id)||(null==(d=La.unitData)?void 0:d.advertiser_id);if(!c)throw new Error("广告主ID不能为空");(()=>{var e,t,a,l,o,r,n;const d=(null==(t=null==(e=La.rowData)?void 0:e.unit)?void 0:t.target_config)||(null==(l=null==(a=La.campaignData)?void 0:a.unit)?void 0:l.target_config)||(null==(o=La.unitData)?void 0:o.target_config);d&&d.industry_interest_target&&((null==(r=d.industry_interest_target.content_interests)?void 0:r.length)>0&&(kl.value.content_interests=[...d.industry_interest_target.content_interests]),(null==(n=d.industry_interest_target.shopping_interests)?void 0:n.length)>0&&(kl.value.shopping_interests=d.industry_interest_target.shopping_interests))})();const R=await t({advertiser_id:c});if(!R)throw new Error("获取定向数据接口返回为空");if(990!==R.code)throw new Error(R.message||"获取定向数据接口返回错误");if(!(null==(i=R.data)?void 0:i.data))throw new Error("获取定向数据接口返回数据格式错误");const S=R.data.data;vl.value=S.gender_targets||[],pl.value=S.age_targets||[],_l.value=S.device_targets||[],ml.value=(e=>e.filter((e=>"-1"!==e.code)).map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.filter((e=>"-1"!==e.code)).map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.filter((e=>"-1"!==e.code)).map((e=>({code:e.code,name:e.name})))}}))}})))(S.area_targets||[]),wl.value=(null==(s=S.crowd_target)?void 0:s.crowd_pkg)||[],yl.value=[...wl.value],S.industry_interest_target?kl.value={content_interests:S.industry_interest_target.content_interests||[],shopping_interests:S.industry_interest_target.shopping_interests||[]}:kl.value={content_interests:[],shopping_interests:[]},vl.value.length>0&&!vl.value.some((e=>"all"===e.code))&&vl.value.unshift({code:"all",name:"全部"}),pl.value.length>0&&!pl.value.some((e=>"all"===e.code))&&pl.value.unshift({code:"all",name:"全部"}),_l.value.length>0&&!_l.value.some((e=>"all"===e.code))&&_l.value.unshift({code:"all",name:"全部"});const U=e=>e&&"all"!==e?"string"==typeof e&&e.includes("#")?["custom"]:"object"!=typeof e||null===e||Array.isArray(e)?Array.isArray(e)?e.length>1||1===e.length&&"all"!==e[0]?["custom"]:e:[e||"all"]:["custom"]:["all"],E=e=>e&&"all"!==e&&"string"==typeof e&&e.includes("#")?e.split("#"):[];if((null==(v=null==(g=La.savedState)?void 0:g.targetCards)?void 0:v.length)>0)bl.value=La.savedState.targetCards.map((e=>{var t,a,l,o,r,n,d,i,s,c,u,g,v,p;!e.crowd_target&&(null==(t=e.target_config)?void 0:t.crowd_target)&&(e.crowd_target=e.target_config.crowd_target);const _=e.target_city||(null==(a=e.target_config)?void 0:a.target_city)||"all",m=U(_),w=E(_);return{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(l=e.target_config)?void 0:l.target_gender)||"all"],location:m,ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:(e.target_age||(null==(o=e.target_config)?void 0:o.target_age)||"").includes("#")?(e.target_age||(null==(r=e.target_config)?void 0:r.target_age)).split("#"):[e.target_age||(null==(n=e.target_config)?void 0:n.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(d=e.target_config)?void 0:d.target_device)||"all"],locationMode:e.locationMode||"country",selectedLocations:e.selectedLocations||[],selectedCities:e.selectedCities||{},locationFilter:e.locationFilter||"",crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(i=e.crowd_target)?void 0:i.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(c=null==(s=e.target_config)?void 0:s.crowd_target)?void 0:c.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:(null==(u=e.selectedNodes)?void 0:u.length)?e.selectedNodes:w,selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(g=e.crowd_target)?void 0:g.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(p=null==(v=e.target_config)?void 0:v.crowd_target)?void 0:p.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}}));else if((null==(p=La.rowData)?void 0:p.unit)||(null==(_=La.campaignData)?void 0:_.unit)){const e=(null==(m=La.rowData)?void 0:m.unit)||(null==(w=La.campaignData)?void 0:w.unit);if(e){const t=(null==(f=e.target_config)?void 0:f.target_city)||"all",a=U(t),l=E(t),o={targetType:e.target_type||1,gender:[((null==(y=e.target_config)?void 0:y.target_gender)||"").includes("#")?e.target_config.target_gender.split("#"):(null==(h=e.target_config)?void 0:h.target_gender)||"all"],location:a,ageRanges:((null==(k=e.target_config)?void 0:k.target_age)||"").includes("#")?e.target_config.target_age.split("#"):[(null==(D=e.target_config)?void 0:D.target_age)||"all"],platform:[((null==(C=e.target_config)?void 0:C.target_device)||"").includes("#")?e.target_config.target_device.split("#"):(null==(b=e.target_config)?void 0:b.target_device)||"all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:l,selectedCrowds:[],selectedKeywords:(null==(I=null==(A=e.target_config)?void 0:A.interest_keywords)?void 0:I.map((e=>({id:e,keyword:e}))))||[],selectedContentInterests:[],selectedShoppingInterests:[],intelligent_expansion:(null==(V=e.target_config)?void 0:V.intelligent_expansion)||0};bl.value=[o],3===e.target_type&&ol(bl.value[0])}}else(null==(x=null==(N=null==(T=La.campaignData)?void 0:T.unit)?void 0:N.targetCards)?void 0:x.length)>0?bl.value=La.campaignData.unit.targetCards.map((e=>{var t,a,l,o,r,n,d,i,s,c,u,g,v;!e.crowd_target&&(null==(t=e.target_config)?void 0:t.crowd_target)&&(e.crowd_target=e.target_config.crowd_target);const p={...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(a=e.target_config)?void 0:a.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(l=e.target_config)?void 0:l.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:(e.target_age||(null==(o=e.target_config)?void 0:o.target_age)||"").includes("#")?(e.target_age||(null==(r=e.target_config)?void 0:r.target_age)).split("#"):[e.target_age||(null==(n=e.target_config)?void 0:n.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(d=e.target_config)?void 0:d.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(i=e.crowd_target)?void 0:i.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(c=null==(s=e.target_config)?void 0:s.crowd_target)?void 0:c.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(u=e.crowd_target)?void 0:u.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(v=null==(g=e.target_config)?void 0:g.crowd_target)?void 0:v.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}};return 3===p.targetType&&ol(p),p})):0===bl.value.length&&(bl.value=[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],selectedContentInterests:[],selectedShoppingInterests:[],crowd_target:{crowd_pkg:[]},intelligent_expansion:0}]);u((()=>{ml.value.length>0&&bl.value.forEach((e=>{if(e.location.includes("custom")&&e.selectedNodes&&e.selectedNodes.length>0){if("string"==typeof e.selectedNodes[0]&&isNaN(Number(e.selectedNodes[0]))){const t=Yl(e.selectedNodes);t.length>0&&(e.selectedNodes=t)}Tl(e)}}))}));const K=vl.value.filter((e=>"all"!==e.code)).map((e=>e.code)),z=pl.value.filter((e=>"all"!==e.code)).map((e=>e.code)),L=_l.value.filter((e=>"all"!==e.code)).map((e=>e.code));return bl.value.forEach((e=>{e.gender.includes("all")&&(e.gender=["all",...K]),e.ageRanges.includes("all")&&(e.ageRanges=["all",...z]),e.platform.includes("all")&&(e.platform=["all",...L])})),!0}catch(R){return c.error(R.message||"获取定向数据失败，请检查网络连接或刷新页面重试"),!1}finally{gl.value=!1}},bl=n((null==(xa=null==(Na=La.savedState)?void 0:Na.targetCards)?void 0:xa.length)>0?JSON.parse(JSON.stringify(La.savedState.targetCards)).map((e=>{var t,a,l,o,r,n,d,i,s,c,u,g;return{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(t=e.target_config)?void 0:t.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(a=e.target_config)?void 0:a.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:(e.target_age||(null==(l=e.target_config)?void 0:l.target_age)||"").includes("#")?(e.target_age||(null==(o=e.target_config)?void 0:o.target_age)).split("#"):[e.target_age||(null==(r=e.target_config)?void 0:r.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(n=e.target_config)?void 0:n.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(d=e.crowd_target)?void 0:d.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(s=null==(i=e.target_config)?void 0:i.crowd_target)?void 0:s.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(c=e.crowd_target)?void 0:c.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(g=null==(u=e.target_config)?void 0:u.crowd_target)?void 0:g.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):(null==(Ra=La.rowData)?void 0:Ra.unit)?[{targetPackage:"",targetType:La.rowData.unit.target_type||1,gender:(La.rowData.unit.target_config.target_gender||"").includes("#")?La.rowData.unit.target_config.target_gender.split("#"):[La.rowData.unit.target_config.target_gender||"all"],location:(La.rowData.unit.target_config.target_city||"").includes("#")?La.rowData.unit.target_config.target_city.split("#"):[La.rowData.unit.target_config.target_city||"all"],ageRanges:(La.rowData.unit.target_config.target_age||"").includes("#")?La.rowData.unit.target_config.target_age.split("#"):[La.rowData.unit.target_config.target_age||"all"],platform:(La.rowData.unit.target_config.target_device||"").includes("#")?La.rowData.unit.target_config.target_device.split("#"):[La.rowData.unit.target_config.target_device||"all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:(null==(Sa=La.rowData.unit.target_config.interest_keywords)?void 0:Sa.map((e=>({id:e,keyword:e}))))||[],selectedContentInterests:[],selectedShoppingInterests:[],intelligent_expansion:La.rowData.unit.target_config.intelligent_expansion||0}]:(null==(Ka=null==(Ea=null==(Ua=La.campaignData)?void 0:Ua.unit)?void 0:Ea.targetCards)?void 0:Ka.length)>0?JSON.parse(JSON.stringify(La.campaignData.unit.targetCards)).map((e=>{var t,a,l,o,r,n,d,i,s,c,u,g;return{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(t=e.target_config)?void 0:t.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(a=e.target_config)?void 0:a.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:(e.target_age||(null==(l=e.target_config)?void 0:l.target_age)||"").includes("#")?(e.target_age||(null==(o=e.target_config)?void 0:o.target_age)).split("#"):[e.target_age||(null==(r=e.target_config)?void 0:r.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(n=e.target_config)?void 0:n.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(d=e.crowd_target)?void 0:d.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(s=null==(i=e.target_config)?void 0:i.crowd_target)?void 0:s.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(c=e.crowd_target)?void 0:c.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(g=null==(u=e.target_config)?void 0:u.crowd_target)?void 0:g.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}]);i(bl,(e=>{e.forEach((e=>{0===e.gender.length&&(e.gender=["all"]),0===e.location.length&&(e.location=["all"]),0===e.ageRanges.length&&(e.ageRanges=["all"]),0===e.platform.length&&(e.platform=["all"]),void 0===e.targetType||null===e.targetType?e.targetType=1:e.targetType=Number(e.targetType)||1}))}),{deep:!0});i([ja,bl,Ha],(()=>{var e,t,a,l,o,r,n,d,i,s,c;const u=bl.value.map((e=>{const t=Number(e.targetType);return{...e,targetType:t||1}}));if(Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.notes=[...ja.value],Fa.event_bid=100*Ha.bid,Fa.noteList=ja.value,Fa.target_cards=u,Fa.targetCards=u,Fa.bid_form={...Ha},bl.value.length>0){let u="all",g="all",v="all";(null==(e=bl.value[0])?void 0:e.gender)&&!bl.value[0].gender.includes("all")&&(u=bl.value[0].gender.join("#")),(null==(t=bl.value[0])?void 0:t.ageRanges)&&!bl.value[0].ageRanges.includes("all")&&(g=bl.value[0].ageRanges.join("#")),(null==(a=bl.value[0])?void 0:a.platform)&&!bl.value[0].platform.includes("all")&&(v=bl.value[0].platform.join("#")),Fa.target_config={...La.unitData.target_config,target_gender:u,target_age:g,target_city:"all"===(null==(l=bl.value[0])?void 0:l.location[0])?"all":{mode:null==(o=bl.value[0])?void 0:o.locationMode,countries:null==(r=bl.value[0])?void 0:r.selectedLocations,cities:null==(n=bl.value[0])?void 0:n.selectedCities,filter:null==(d=bl.value[0])?void 0:d.locationFilter},target_device:v,crowd_target:{...(null==(i=La.unitData.target_config)?void 0:i.crowd_target)||{},crowd_pkg:(null==(c=null==(s=bl.value[0])?void 0:s.crowds)?void 0:c.map((e=>{var t;return{value:e,name:(null==(t=wl.value.find((t=>t.value===e)))?void 0:t.name)||""}})))||[]}}}}),{deep:!0}),i(sl,(async()=>{Wa.currentPage=1,Ba.value=[],Ja.value=[],await Ya()})),i((()=>bl.value.map((e=>e.location))),(e=>{bl.value.forEach(((t,a)=>{var l;const o=e[a];if(o.includes("custom")&&(o.includes("all")&&(t.location=["custom"]),!t.selectedNodes||0===t.selectedNodes.length)){const e=t.target_city||(null==(l=t.target_config)?void 0:l.target_city);if(e&&"all"!==e&&"string"==typeof e&&e.includes("#")){const a=e.split("#");t.selectedNodes=a,u((()=>{if(ml.value.length>0){const e=Yl(a);e.length>0&&(t.selectedNodes=e,Tl(t))}}))}}if(o.includes("all")&&o.includes("custom")){"all"===o[o.length-1]?(t.location=["all"],t.selectedNodes=[]):t.location=["custom"]}}))}),{deep:!0});const Al=e=>e.selectedNodes&&e.selectedNodes.length>0,Il={multiple:!0,checkStrictly:!1,value:"code",label:"name",children:"children",expandTrigger:"click",emitPath:!1,lazy:!1,leafOnly:!1,includeAllLevels:!1,strictMode:!1},Vl=g((()=>ml.value.map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.map((e=>({code:e.code,name:e.name})))}}))}})))),Tl=e=>{var t;(null==(t=e.selectedNodes)?void 0:t.length)>0&&!e.location.includes("custom")&&(e.location=["custom"]),e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={};(e.selectedNodes||[]).forEach((t=>{if(!(e=>{const t=a=>{for(const l of a){if(l.code===e)return!0;if(l.children&&t(l.children))return!0}return!1};return t(Vl.value)})(t))return;const a=e=>{for(const l of e){if(l.code===t)return l;if(l.children){const e=a(l.children);if(e)return e}}return null},l=a(Vl.value);if(!l)return;const o=((e,t)=>{for(const a of e)if(a.children)for(const e of a.children){if(e.code===t)return[a,e];if(e.children)for(const l of e.children)if(l.code===t)return[a,e,l]}return[]})(Vl.value,l.code);if(0===o.length)e.selectedLocations.some((e=>e.code===l.code))||e.selectedLocations.push(l);else if(2===o.length){const[t,a]=o;e.selectedLocations.some((e=>e.code===t.code))||e.selectedLocations.push(t),e.selectedCities[t.code]||(e.selectedCities[t.code]=[]),e.selectedCities[t.code].some((e=>e.code===a.code))||e.selectedCities[t.code].push(a)}else if(3===o.length){const[t,a,l]=o;e.selectedLocations.some((e=>e.code===t.code))||e.selectedLocations.push(t),e.selectedCities[t.code]||(e.selectedCities[t.code]=[]),e.selectedCities[t.code].some((e=>e.code===a.code))||e.selectedCities[t.code].push(a),e.selectedDistricts[a.code]||(e.selectedDistricts[a.code]=[]),e.selectedDistricts[a.code].some((e=>e.code===l.code))||e.selectedDistricts[a.code].push(l)}}))},Nl=e=>{const t=[];return(e.selectedNodes||[]).forEach((e=>{const a=t=>{for(const l of t){if(l.code===e)return l;if(l.children){const e=a(l.children);if(e)return e}}return null},l=a(Vl.value);l&&t.push({code:l.code,name:l.name})})),t},xl=(e,t)=>{e.selectedNodes=e.selectedNodes.filter((e=>!((e,t)=>{const a=Array.isArray(t)?t[t.length-1]:t,l=(e,t=[])=>{for(const o of e){if(o.code===a)return[...t,o];if(o.children){const e=l(o.children,[...t,o]);if(e.length>0)return e}}return[]};return l(e)})(Vl.value,e).some((e=>e.code===t.code)))),Tl(e)},Rl=n(!1),Sl=n(""),Ul=n([]),El=n(null),Kl=()=>{El.value&&(El.value.targetPackage=Sl.value),Rl.value=!1},zl=()=>{Sl.value="",Rl.value=!1},Ll=n(""),Pl=n([]),jl=n(!1),Fl=async()=>{var e;jl.value=!0;try{const t=await a({advertiser_id:944495,keyword:Ll.value,note_ids:ja.value.map((e=>e.id))});990===t.code&&(null==(e=t.data)?void 0:e.data)&&(Pl.value=t.data.data)}catch(t){c.error("获取推荐关键词失败")}finally{jl.value=!1}},$l=l.debounce((()=>{Fl()}),300),Ml=(e,t)=>{var a;return(null==(a=e.selectedKeywords)?void 0:a.some((e=>e.id===t.target_word)))??!1};s((()=>{Fl()})),i((()=>sl.value),(e=>{"keywords"===e&&Fl()}));const Bl=e=>{e.targetType&&0!==e.targetType?e.targetType=Number(e.targetType):e.targetType=1},Ol=()=>({targetType:1,gender:["all",...vl.value.filter((e=>"all"!==e.code)).map((e=>e.code))],ageRanges:["all",...pl.value.filter((e=>"all"!==e.code)).map((e=>e.code))],location:["all"],platform:["all",..._l.value.filter((e=>"all"!==e.code)).map((e=>e.code))],crowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}),Jl=()=>{const e=Ql();if(Pa("update:unit-data",e.unit),La.rowData){const t={...La.rowData,unit:{...e.unit,unit_name:e.unit.unit_name||La.rowData.unit.unit_name||Fa.unit_name,advertiser_id:e.unit.advertiser_id||La.rowData.unit.advertiser_id||La.rowData.campaign.advertiser_id||Fa.advertiser_id}};Pa("update:rowData",t)}},ql=()=>{var e,t;return!(!(null==(e=La.rowData)?void 0:e.unit)&&!(null==(t=La.campaignData)?void 0:t.unit))};i((()=>La.campaignData),(e=>{}),{deep:!0});i(ul,(()=>{bl.value.forEach((e=>{(e=>{e.industry_interest_target=kl.value,e.selectedContentInterests||(e.selectedContentInterests=[]),e.selectedShoppingInterests||(e.selectedShoppingInterests=[])})(e)}))}));const Ql=()=>{var e,t,a,l,o,r,n,d,i,s,c,u,g,v,p,_,m,w,f,y,h,k,D,C,b,A,I,V,T,N,x,R,S,U,E,K,z,L,P,j,F,$,M,B,O,J;let q="all";if(null==(t=null==(e=bl.value[0])?void 0:e.location)?void 0:t.includes("all"))q="all";else if((null==(l=null==(a=bl.value[0])?void 0:a.selectedNodes)?void 0:l.length)>0){q=eo(null==(o=bl.value[0])?void 0:o.selectedNodes).join("#")}else(null==(n=null==(r=bl.value[0])?void 0:r.location)?void 0:n.includes("custom"))&&(null==(d=bl.value[0])?void 0:d.target_city)&&"all"!==(null==(i=bl.value[0])?void 0:i.target_city)&&(q=null==(s=bl.value[0])?void 0:s.target_city);const Q=[];if(null==(g=null==(u=null==(c=bl.value[0])?void 0:c.crowd_target)?void 0:u.crowd_pkg)?void 0:g.length){bl.value[0].crowd_target.crowd_pkg.forEach((e=>{Q.some((t=>t.value===e.value))||Q.push(e)}))}if(null==(p=null==(v=bl.value[0])?void 0:v.crowds)?void 0:p.length){bl.value[0].crowds.forEach((e=>{var t,a,l;if(!Q.some((t=>t.value===e))){const o=(null==(l=null==(a=null==(t=bl.value[0])?void 0:t.selectedCrowds)?void 0:a.find((t=>t.value===e)))?void 0:l.name)||"";Q.push({value:e,name:o})}}))}if(null==(f=null==(w=null==(m=null==(_=La.rowData)?void 0:_.unit)?void 0:m.target_config)?void 0:w.crowd_target)?void 0:f.crowd_pkg){La.rowData.unit.target_config.crowd_target.crowd_pkg.forEach((e=>{Q.some((t=>t.value===e.value))||Q.push(e)}))}if(null==(k=null==(h=null==(y=La.unitData)?void 0:y.target_config)?void 0:h.crowd_target)?void 0:k.crowd_pkg){La.unitData.target_config.crowd_target.crowd_pkg.forEach((e=>{Q.some((t=>t.value===e.value))||Q.push(e)}))}let G="all",H="all",W="all";(null==(D=bl.value[0])?void 0:D.gender)&&!bl.value[0].gender.includes("all")&&(G=bl.value[0].gender.join("#")),(null==(C=bl.value[0])?void 0:C.ageRanges)&&!bl.value[0].ageRanges.includes("all")&&(H=bl.value[0].ageRanges.join("#")),(null==(b=bl.value[0])?void 0:b.platform)&&!bl.value[0].platform.includes("all")&&(W=bl.value[0].platform.join("#"));const X={...null==(A=La.rowData)?void 0:A.unit,unit_name:Fa.unit_name||(null==(V=null==(I=La.rowData)?void 0:I.unit)?void 0:V.unit_name),advertiser_id:Fa.advertiser_id||(null==(N=null==(T=La.rowData)?void 0:T.campaign)?void 0:N.advertiser_id)||(null==(R=null==(x=La.rowData)?void 0:x.unit)?void 0:R.advertiser_id)||(null==(U=null==(S=La.campaignData)?void 0:S.plan)?void 0:U.advertiser_id)||(null==(E=La.unitData)?void 0:E.advertiser_id),note_ids:ja.value.map((e=>e.noteId)),event_bid:100*Ha.bid,target_type:(null==(K=bl.value[0])?void 0:K.targetType)||1,target_config:{target_gender:G,target_age:H,target_city:q,target_device:W,industry_interest_target:{content_interests:Gl(null==(z=bl.value[0])?void 0:z.selectedContentInterests,kl.value.content_interests),shopping_interests:Gl(null==(L=bl.value[0])?void 0:L.selectedShoppingInterests,kl.value.shopping_interests)},interest_keywords:(null==(j=null==(P=bl.value[0])?void 0:P.selectedKeywords)?void 0:j.map((e=>e.keyword)))||[],intelligent_expansion:(null==(F=bl.value[0])?void 0:F.intelligent_expansion)||0,...Q&&Q.length>0?{crowd_target:{...(null==(B=null==(M=null==($=La.rowData)?void 0:$.unit)?void 0:M.target_config)?void 0:B.crowd_target)||{},...(null==(J=null==(O=La.unitData)?void 0:O.target_config)?void 0:J.crowd_target)||{},crowd_pkg:Q,dmp_permission:!0}}:{}}};return X.target_config.crowd_target&&X.target_config.crowd_target.crowd_pkg&&0!==X.target_config.crowd_target.crowd_pkg.length||delete X.target_config.crowd_target,{unit:X}};function Gl(e,t){if(!e||!t)return[];return function t(a){return a.reduce(((a,l)=>{if(e.includes(l.code))a.push(l);else if(l.children){const e=t(l.children);e.length&&a.push({...l,children:e})}return a}),[])}(t)}const Hl=e=>e?"string"==typeof e&&e.includes("#")?e.split("#"):Array.isArray(e)?e:[e]:["all"];i((()=>bl.value.map((e=>e.targetType))),((e,t)=>{e&&t&&e.forEach(((e,a)=>{const l=t[a];3===e&&3!==l&&ol(bl.value[a])}))}),{deep:!0});const Wl=g((()=>{const e=ul.value;return kl.value&&kl.value[e]&&Array.isArray(kl.value[e])&&kl.value[e].length>0})),Xl=async()=>{var e,a,l,o,r,n,d,i;try{gl.value=!0;const s=(null==(a=null==(e=La.rowData)?void 0:e.campaign)?void 0:a.advertiser_id)||(null==(o=null==(l=La.campaignData)?void 0:l.campaign)?void 0:o.advertiser_id)||(null==(n=null==(r=La.campaignData)?void 0:r.plan)?void 0:n.advertiser_id)||(null==(d=La.unitData)?void 0:d.advertiser_id);if(!s)throw new Error("广告主ID不能为空");const u=await t({advertiser_id:s});if(!u||990!==u.code||!(null==(i=u.data)?void 0:i.data))throw new Error("获取行业兴趣数据失败");const g=u.data.data;if(!g.industry_interest_target)throw new Error("接口返回的数据中没有行业兴趣数据");kl.value={content_interests:g.industry_interest_target.content_interests||[],shopping_interests:g.industry_interest_target.shopping_interests||[]},c.success("行业兴趣数据已重新加载")}catch(s){c.error(s.message||"重新加载行业兴趣数据失败")}finally{gl.value=!1}};N({scrollToSection:il,validate:async()=>{var e;if(bl.value.forEach((e=>{Bl(e),e.crowd_target||(e.crowd_target={crowd_pkg:[]})})),0===ja.value.length)return c.error("请至少添加一条笔记"),il("notes"),!1;if(0===bl.value.length)return c.error("请至少添加一个定向组合"),il("targeting"),!1;for(let a=0;a<bl.value.length;a++){const t=bl.value[a];void 0!==t.targetType&&null!==t.targetType&&0!==t.targetType||(t.targetType=1);const l=[...t.selectedKeywords,...t.selectedContentInterests||[],...t.selectedShoppingInterests||[],...(null==(e=t.crowd_target)?void 0:e.crowd_pkg)||[]];if(3===t.targetType&&(0===l.length||l.length>150))return c.error(`定向组合${a+1}：高级定向不能为空且不能超过150`),!1;if(!t.gender||0===t.gender.length)return c.error(`定向组合${a+1}：请选择性别`),!1;if(!t.location||0===t.location.length)return c.error(`定向组合${a+1}：请选择地域`),!1;if(t.location.includes("custom")&&!t.location.includes("all")&&(!t.selectedNodes||0===t.selectedNodes.length))return c.error(`定向组合${a+1}：请选择具体地域`),!1;if(!t.ageRanges||0===t.ageRanges.length)return c.error(`定向组合${a+1}：请选择年龄范围`),!1;if(!t.platform||0===t.platform.length)return c.error(`定向组合${a+1}：请选择平台`),!1}if(!Ha.bid)return c.error("请设置目标成本"),il("bidding"),!1;const t=bl.value.map((e=>{const t={...e,targetType:Number(e.targetType)||1};return t.gender&&t.gender.includes("all")&&(t.gender=["all"]),t.ageRanges&&t.ageRanges.includes("all")&&(t.ageRanges=["all"]),t.platform&&t.platform.includes("all")&&(t.platform=["all"]),t}));return Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.notes=[...ja.value],Fa.target_cards=t,Fa.bid_form=Ha,Jl(),!0},getUnitData:Ql});const Yl=e=>{if(!e||!e.length||!ml.value)return[];const t=e=>e?e.replace(/市$|省$|自治区$|特别行政区$|壮族$|回族$|维吾尔$|自治州$|地区$/,""):"",a=[],l=[],o=[];ml.value.forEach((e=>{o.push(e),e.children&&e.children.forEach((o=>{l.push(o),o.children&&o.children.forEach((l=>{a.push({...l,provinceName:o.name,areaName:e.name,normalizedName:t(l.name)})}))}))}));return e.map((e=>{const r=t(e);let n=a.find((t=>t.name===e));if(n)return n.code;if(n=a.find((e=>e.normalizedName===r)),n)return n.code;let d=l.find((t=>t.name===e));if(d)return d.code;if(d=l.find((e=>t(e.name)===r)),d)return d.code;const i=o.find((a=>a.name===e||t(a.name)===r));return i?i.code:e}))},Zl=e=>{var t;if(e.location.includes("custom")&&(!e.selectedNodes||0===e.selectedNodes.length)){const a=e.target_city||(null==(t=e.target_config)?void 0:t.target_city);if(a&&"all"!==a&&"string"==typeof a&&a.includes("#")){const t=a.split("#");e.selectedNodes=t,setTimeout((()=>{if(ml.value.length>0){const a=Yl(t);a.length>0&&(e.selectedNodes=a,Tl(e))}}),500)}}},eo=e=>{if(!e||!e.length||!ml.value)return e;const t=[],a=[];ml.value.forEach((e=>{e.children&&e.children.forEach((e=>{a.push(e),e.children&&e.children.forEach((e=>{t.push(e)}))}))}));return e.map((e=>{const l=t.find((t=>t.code===e));if(l)return l.name;const o=a.find((t=>t.code===e));return o?o.name:e}))},to=l.debounce((()=>{fl.value?yl.value=wl.value.filter((e=>e.name.includes(fl.value)||e.value.includes(fl.value))):yl.value=[...wl.value]}),300),ao=(e,t)=>!!e.selectedCrowds&&e.selectedCrowds.some((e=>e.value===t.value)),lo=e=>{!e.crowd_target||e.crowd_target.crowd_pkg&&0!==e.crowd_target.crowd_pkg.length||delete e.crowd_target};return s((()=>{var e,t,a,l,o,r,n,d,i;null==(l=null==(a=null==(t=null==(e=La.unitData)?void 0:e.target_config)?void 0:t.crowd_target)?void 0:a.crowd_pkg)||l.length,null==(d=null==(n=null==(r=null==(o=La.savedState)?void 0:o.targetCards)?void 0:r[0])?void 0:n.selectedCrowds)||d.length,(null==(i=bl.value)?void 0:i.length)&&bl.value.forEach((e=>{var t,a;e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),(null==(t=e.selectedCrowds)?void 0:t.length)&&!e.crowd_target.crowd_pkg.length?e.crowd_target.crowd_pkg=e.selectedCrowds.map((e=>({name:e.name,value:e.value}))):!(null==(a=e.crowd_target.crowd_pkg)?void 0:a.length)||e.selectedCrowds&&e.selectedCrowds.length||(e.selectedCrowds||(e.selectedCrowds=[]),e.selectedCrowds=e.crowd_target.crowd_pkg.map((e=>({name:e.name,value:e.value}))))}))})),(e,t)=>{const a=v("el-table-column"),l=v("el-image"),r=v("el-table"),n=v("el-button"),d=v("el-radio"),i=v("el-radio-group"),s=v("el-radio-button"),u=v("el-cascader"),g=v("el-cascader-panel"),N=v("el-tab-pane"),ot=v("el-input"),rt=v("el-tag"),nt=v("el-tabs"),dt=v("el-switch"),it=v("el-checkbox"),st=v("el-checkbox-group"),ct=v("el-skeleton"),ut=v("el-input-number"),gt=v("el-form-item"),vt=v("el-form"),pt=v("el-pagination"),_t=v("el-drawer"),mt=v("el-option"),wt=v("el-select"),ft=v("el-dialog"),yt=p("loading");return m(),_("div",x,[w(o,{anchors:Ga}),f("div",R,[t[13]||(t[13]=f("div",{class:"section-title"},"推广笔记",-1)),f("div",S,[t[12]||(t[12]=f("p",{class:"section-desc"},"单元层级添加的笔记/商品将自动为您生成多个创意，并用于广告投放。在新建流程中，若想新增或删除笔记/商品，请在单元层级修改。",-1)),f("div",U,[w(r,{data:ja.value,border:"",style:{width:"100%"}},{default:y((()=>[w(a,{prop:"noteId",label:"笔记ID",width:"180"}),w(a,{label:"图片",width:"100"},{default:y((e=>[w(l,{src:e.row.image,preview:!1,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"])])),_:1}),w(a,{prop:"desc",label:"描述","show-overflow-tooltip":"","min-width":"300"},{default:y((e=>[f("div",E,h(e.row.desc),1)])),_:1}),w(a,{prop:"desc",label:"SPU","show-overflow-tooltip":"","min-width":"160"},{default:y((e=>[f("span",K,"累计绑定"+h(e.row.spu)+"个SPU",1)])),_:1}),w(a,{prop:"author",label:"作者",width:"120"}),w(a,{prop:"noteType",label:"笔记形式",width:"100"}),w(a,{prop:"createTime",label:"创建时间",width:"180"})])),_:1},8,["data"])])])],512),f("div",z,[t[55]||(t[55]=f("div",{class:"section-title"},"定向组合",-1)),f("div",L,[f("div",P,[(m(!0),_(k,null,D(bl.value,((e,l)=>(m(),_("div",{key:l,class:"target-card"},[t[54]||(t[54]=f("div",{class:"card-header"},[f("span",null,"定向组合")],-1)),f("div",j,[f("div",F,[t[14]||(t[14]=f("div",{class:"section-left"},[f("div",{class:"section-title"},"定向")],-1)),f("div",$,[w(n,{link:"",type:"primary",onClick:t=>(e=>{Sl.value=e.targetPackage||"",Rl.value=!0,El.value=e})(e)},{default:y((()=>[b(h(e.targetPackage?"修改定向包":"关联定向包"),1)])),_:2},1032,["onClick"])])]),f("div",M,[t[18]||(t[18]=f("div",{class:"section-left"},[f("div",{class:"section-title"},"定向类型")],-1)),f("div",B,[w(i,{"model-value":Number(e.targetType)||1,"onUpdate:modelValue":t=>e.targetType=t},{default:y((()=>[w(d,{label:1},{default:y((()=>t[15]||(t[15]=[b("通投")]))),_:1}),w(d,{label:2},{default:y((()=>t[16]||(t[16]=[b("智能定向")]))),_:1}),w(d,{label:3},{default:y((()=>t[17]||(t[17]=[b("高级定向")]))),_:1})])),_:2},1032,["model-value","onUpdate:modelValue"]),f("div",O,h(za(e.targetType||1)),1)])]),3===Number(e.targetType)?(m(),_("div",J,[t[38]||(t[38]=f("div",{class:"section-left"},[f("div",{class:"section-title"},"高级定向")],-1)),f("div",q,[w(nt,null,{default:y((()=>[w(N,{label:"行业兴趣"},{default:y((()=>[f("div",null,[w(i,{modelValue:ul.value,"onUpdate:modelValue":t[0]||(t[0]=e=>ul.value=e),size:"small",class:"switch-group"},{default:y((()=>[w(s,{label:"content_interests"},{default:y((()=>t[19]||(t[19]=[b("行业阅读兴趣")]))),_:1}),w(s,{label:"shopping_interests"},{default:y((()=>t[20]||(t[20]=[b("行业购物兴趣")]))),_:1})])),_:1},8,["modelValue"]),"content_interests"===ul.value?(m(),_(k,{key:0},[f("div",Q,[t[22]||(t[22]=f("span",null,"选择行业阅读兴趣：",-1)),Wl.value?C("",!0):(m(),A(n,{key:0,type:"primary",size:"small",onClick:Xl},{default:y((()=>t[21]||(t[21]=[b(" 加载行业兴趣数据 ")]))),_:1}))]),w(u,{modelValue:e.selectedContentInterests,"onUpdate:modelValue":t=>e.selectedContentInterests=t,options:Dl.value,props:Il,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"]),w(g,{modelValue:e.selectedContentInterests,"onUpdate:modelValue":t=>e.selectedContentInterests=t,options:Dl.value,props:Il,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"])],64)):(m(),_(k,{key:1},[f("div",G,[t[24]||(t[24]=f("span",null,"选择行业购物兴趣：",-1)),Wl.value?C("",!0):(m(),A(n,{key:0,type:"primary",size:"small",onClick:Xl},{default:y((()=>t[23]||(t[23]=[b(" 加载行业兴趣数据 ")]))),_:1}))]),w(u,{modelValue:e.selectedShoppingInterests,"onUpdate:modelValue":t=>e.selectedShoppingInterests=t,options:Dl.value,props:Il,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"]),w(g,{modelValue:e.selectedShoppingInterests,"onUpdate:modelValue":t=>e.selectedShoppingInterests=t,options:Dl.value,props:Il,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"])],64))])])),_:2},1024),w(N,{label:"关键词"},{default:y((()=>{var l;return[f("div",H,[f("div",W,[w(ot,{modelValue:Ll.value,"onUpdate:modelValue":t[1]||(t[1]=e=>Ll.value=e),placeholder:"请输入关键词","prefix-icon":"Search",clearable:"",onInput:I($l)},{append:y((()=>[w(n,{onClick:I($l)},{default:y((()=>t[25]||(t[25]=[b("搜索")]))),_:1},8,["onClick"])])),_:1},8,["modelValue","onInput"])]),f("div",X,[f("div",Y,[f("div",Z,[t[27]||(t[27]=f("span",null,"推荐关键词",-1)),w(n,{type:"primary",link:"",onClick:t=>(e=>{e.selectedKeywords||(e.selectedKeywords=[]),Pl.value.forEach((t=>{Ml(e,t)||e.selectedKeywords.push({id:t.target_word,keyword:t.target_word,cover_num:t.cover_num,recommend_reason:t.recommend_reason})})),c.success("已添加全部关键词")})(e)},{default:y((()=>t[26]||(t[26]=[b(" 全选 ")]))),_:2},1032,["onClick"])]),f("div",ee,[V((m(),A(r,{data:Pl.value,style:{width:"100%"},height:"400px"},{default:y((()=>[w(a,{label:"关键词",prop:"target_word"}),w(a,{label:"覆盖人群",prop:"cover_num"},{default:y((e=>{return[b(h((t=e.row.cover_num,(new Intl.NumberFormat).format(t))),1)];var t})),_:1}),w(a,{label:"推荐理由"},{default:y((e=>[(m(!0),_(k,null,D(e.row.recommend_reason,(e=>(m(),A(rt,{key:e,size:"small",style:{"margin-right":"4px"}},{default:y((()=>[b(h(e),1)])),_:2},1024)))),128))])),_:1}),w(a,{label:"操作",width:"100",fixed:"right"},{default:y((t=>[w(n,{type:"primary",link:"",onClick:a=>((e,t)=>{e.selectedKeywords||(e.selectedKeywords=[]),Ml(e,t)||(e.selectedKeywords.push({id:t.target_word,keyword:t.target_word,cover_num:t.cover_num,recommend_reason:t.recommend_reason}),c.success("添加关键词成功"))})(e,t.row),disabled:Ml(e,t.row)},{default:y((()=>[b(h(Ml(e,t.row)?"已添加":"添加"),1)])),_:2},1032,["onClick","disabled"])])),_:2},1024)])),_:2},1032,["data"])),[[yt,jl.value]])])]),f("div",te,[f("div",ae,[t[29]||(t[29]=f("span",null,"已选关键词",-1)),w(n,{type:"primary",link:"",onClick:t=>(e=>{e.selectedKeywords=[],c.success("已清空所有关键词")})(e)},{default:y((()=>t[28]||(t[28]=[b(" 清空 ")]))),_:2},1032,["onClick"])]),f("div",le,[(null==(l=e.selectedKeywords)?void 0:l.length)?(m(!0),_(k,{key:0},D(e.selectedKeywords,(a=>(m(),_("div",{key:a.id,class:"keyword-item"},[f("div",oe,[f("span",re,h(a.keyword),1)]),w(n,{type:"primary",link:"",onClick:t=>((e,t)=>{e.selectedKeywords&&(e.selectedKeywords=e.selectedKeywords.filter((e=>e.id!==t.id)))})(e,a)},{default:y((()=>t[30]||(t[30]=[b(" 删除 ")]))),_:2},1032,["onClick"])])))),128)):(m(),_("div",ne," 暂无选中的关键词 "))])])])])]})),_:2},1024),w(N,{label:"人群包"},{default:y((()=>{var l;return[f("div",de,[f("div",ie,[w(ot,{modelValue:fl.value,"onUpdate:modelValue":t[2]||(t[2]=e=>fl.value=e),placeholder:"请输入人群包名称","prefix-icon":"Search",clearable:"",onInput:I(to)},{append:y((()=>[w(n,{onClick:I(to)},{default:y((()=>t[31]||(t[31]=[b("搜索")]))),_:1},8,["onClick"])])),_:1},8,["modelValue","onInput"])]),f("div",se,[f("div",ce,[f("div",ue,[t[33]||(t[33]=f("span",null,null,-1)),w(n,{type:"primary",link:"",onClick:t=>(e=>{e.selectedCrowds||(e.selectedCrowds=[]),e.crowds||(e.crowds=[]),e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),yl.value.forEach((t=>{ao(e,t)||(e.selectedCrowds.push({name:t.name,value:t.value}),e.crowd_target.crowd_pkg.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))}))})(e)},{default:y((()=>t[32]||(t[32]=[b(" 全选 ")]))),_:2},1032,["onClick"])]),f("div",ge,[V((m(),A(r,{data:yl.value,style:{width:"100%"},height:"400px"},{default:y((()=>[w(a,{label:"人群包名称",prop:"name"}),w(a,{label:"人群包ID",prop:"value"}),w(a,{label:"操作",width:"100",fixed:"right"},{default:y((t=>[w(n,{type:"primary",link:"",onClick:a=>((e,t)=>{var a,l,o,r,n,d,i;e.selectedCrowds||(e.selectedCrowds=[]),e.crowds||(e.crowds=[]),e.crowd_target||(e.crowd_target={crowd_pkg:[]},(null==(r=null==(o=null==(l=null==(a=La.rowData)?void 0:a.unit)?void 0:l.target_config)?void 0:o.crowd_target)?void 0:r.crowd_pkg)&&La.rowData.unit.target_config.crowd_target.crowd_pkg.forEach((t=>{e.crowd_target.crowd_pkg.some((e=>e.value===t.value))||(e.crowd_target.crowd_pkg.push({...t}),e.selectedCrowds.some((e=>e.value===t.value))||e.selectedCrowds.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))})),(null==(i=null==(d=null==(n=La.unitData)?void 0:n.target_config)?void 0:d.crowd_target)?void 0:i.crowd_pkg)&&La.unitData.target_config.crowd_target.crowd_pkg.forEach((t=>{e.crowd_target.crowd_pkg.some((e=>e.value===t.value))||(e.crowd_target.crowd_pkg.push({...t}),e.selectedCrowds.some((e=>e.value===t.value))||e.selectedCrowds.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))}))),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),ao(e,t)||(e.selectedCrowds.push({name:t.name,value:t.value}),e.crowd_target.crowd_pkg.push({name:t.name,value:t.value}),e.crowds.includes(t.value)||e.crowds.push(t.value))})(e,t.row),disabled:ao(e,t.row)},{default:y((()=>[b(h(ao(e,t.row)?"已添加":"添加"),1)])),_:2},1032,["onClick","disabled"])])),_:2},1024)])),_:2},1032,["data"])),[[yt,hl.value]])])]),f("div",ve,[f("div",pe,[t[35]||(t[35]=f("span",null,"已选人群包",-1)),w(n,{type:"primary",link:"",onClick:t=>(e=>{e.selectedCrowds=[],e.crowd_target&&delete e.crowd_target,e.crowds=[]})(e)},{default:y((()=>t[34]||(t[34]=[b(" 清空 ")]))),_:2},1032,["onClick"])]),f("div",_e,[(null==(l=e.selectedCrowds)?void 0:l.length)?(m(!0),_(k,{key:0},D(e.selectedCrowds,(a=>(m(),_("div",{key:a.value,class:"crowd-item"},[f("div",me,[f("span",we,h(a.name),1),f("span",fe,"ID: "+h(a.value),1)]),w(n,{type:"primary",link:"",onClick:t=>((e,t)=>{var a;e.selectedCrowds&&(e.selectedCrowds=e.selectedCrowds.filter((e=>e.value!==t.value))),(null==(a=e.crowd_target)?void 0:a.crowd_pkg)&&(e.crowd_target.crowd_pkg=e.crowd_target.crowd_pkg.filter((e=>e.value!==t.value)),lo(e)),e.crowds&&(e.crowds=e.crowds.filter((e=>e!==t.value)))})(e,a)},{default:y((()=>t[36]||(t[36]=[b(" 删除 ")]))),_:2},1032,["onClick"])])))),128)):(m(),_("div",ye," 暂无选中的人群包 "))])])])])]})),_:2},1024)])),_:2},1024)]),f("div",null,[f("div",he,[w(dt,{modelValue:e.intelligent_expansion,"onUpdate:modelValue":t=>e.intelligent_expansion=t,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"]),t[37]||(t[37]=f("div",{style:{"margin-left":"10px"}},[f("div",{class:"unit-section-header-top"},[f("span",null,"智能扩量"),f("span",{class:"unit-section-header-top-recommend"},"推荐开启")]),f("div",{class:"unit-section-header-bottom"}," 开启后，系统将基于「高级定向」探索更多人群，可提升整体跑量效果。 ")],-1))])])])):C("",!0),f("div",ke,[t[53]||(t[53]=f("div",{class:"section-left"},[f("div",{class:"section-title"},"更多定向")],-1)),f("div",De,[f("div",Ce,[t[40]||(t[40]=f("span",{class:"detail-label"},"性别",-1)),w(st,{modelValue:e.gender,"onUpdate:modelValue":t=>e.gender=t,onChange:t=>(e=>{const t="all",a=vl.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.gender.length?e.gender.includes(t)?1===e.gender.length?e.gender=[t,...a]:e.gender.length>1&&(e.gender[e.gender.length-1]===t?e.gender=[t,...a]:e.gender=e.gender.filter((e=>e!==t))):e.gender.includes(t)||a.every((t=>e.gender.includes(t)))&&a.length>0&&(e.gender=[t,...a]):e.gender=[t]})(e)},{default:y((()=>[vl.value.length>0?(m(!0),_(k,{key:0},D(vl.value,(e=>(m(),A(it,{key:e.code,label:e.code},{default:y((()=>[b(h(e.name),1)])),_:2},1032,["label"])))),128)):(m(),A(it,{key:1,label:"all"},{default:y((()=>t[39]||(t[39]=[b("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),f("div",be,[t[48]||(t[48]=f("span",{class:"detail-label",style:{"line-height":"32px"}},"地域",-1)),f("div",Ae,[w(st,{modelValue:e.location,"onUpdate:modelValue":t=>e.location=t,onChange:t=>(e=>{const t="all",a="custom";if(0===e.location.length)return void(e.location=[t]);const l=e.location[e.location.length-1];if(l===t)return e.location=[t],e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={},void(e.selectedNodes=[]);l!==a||(e.location=[a])})(e)},{default:y((()=>[w(it,{label:"all"},{default:y((()=>t[41]||(t[41]=[b("不限")]))),_:1}),w(it,{label:"custom"},{default:y((()=>t[42]||(t[42]=[b("自定义")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),e.location.includes("custom")&&!e.location.includes("all")?V((m(),_("div",Ie,[f("div",Ve,[w(i,{modelValue:e.locationMode,"onUpdate:modelValue":t=>e.locationMode=t},{default:y((()=>[w(d,{label:"country"},{default:y((()=>t[43]||(t[43]=[b("按国家划分")]))),_:1}),w(d,{label:"development"},{default:y((()=>t[44]||(t[44]=[b("按发展划分")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),f("div",Te,[w(ot,{modelValue:e.locationFilter,"onUpdate:modelValue":t=>e.locationFilter=t,placeholder:"请输入国家/省份/城市名称进行搜索","prefix-icon":"Search",clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),f("div",Ne,[f("div",xe,[gl.value?(m(),_("div",Re,[w(ct,{rows:5,animated:""})])):0===ml.value.length?(m(),_("div",Se," 暂无地域数据 ")):(m(),A(g,{key:2,modelValue:e.selectedNodes,"onUpdate:modelValue":t=>e.selectedNodes=t,options:Vl.value,props:Il,onChange:t=>Tl(e)},null,8,["modelValue","onUpdate:modelValue","options","onChange"]))]),f("div",Ue,[f("div",Ee,[t[46]||(t[46]=f("span",null,"已选",-1)),w(n,{type:"primary",link:"",onClick:t=>(e=>{e.selectedNodes=[],e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={}})(e)},{default:y((()=>t[45]||(t[45]=[b(" 清空 ")]))),_:2},1032,["onClick"])]),f("div",Ke,[Al(e)?(m(!0),_(k,{key:0},D(Nl(e),(a=>(m(),_("div",{key:a.code,class:"selected-item"},[f("div",ze,[f("span",null,h(a.name),1),w(n,{type:"primary",link:"",onClick:t=>xl(e,a)},{default:y((()=>t[47]||(t[47]=[b(" 删除 ")]))),_:2},1032,["onClick"])])])))),128)):(m(),_("div",Le," 暂无选中项 "))])])])])),[[yt,gl.value]]):C("",!0)])]),f("div",Pe,[t[50]||(t[50]=f("span",{class:"detail-label"},"年龄",-1)),w(st,{modelValue:e.ageRanges,"onUpdate:modelValue":t=>e.ageRanges=t,onChange:t=>(e=>{const t="all",a=pl.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.ageRanges.length?e.ageRanges.includes(t)?1===e.ageRanges.length?e.ageRanges=[t,...a]:e.ageRanges.length>1&&(e.ageRanges[e.ageRanges.length-1]===t?e.ageRanges=[t,...a]:e.ageRanges=e.ageRanges.filter((e=>e!==t))):e.ageRanges.includes(t)||a.every((t=>e.ageRanges.includes(t)))&&a.length>0&&(e.ageRanges=[t,...a]):e.ageRanges=[t]})(e)},{default:y((()=>[pl.value.length>0?(m(!0),_(k,{key:0},D(pl.value,(e=>(m(),A(it,{key:e.code,label:e.code},{default:y((()=>[b(h(e.name),1)])),_:2},1032,["label"])))),128)):(m(),A(it,{key:1,label:"all"},{default:y((()=>t[49]||(t[49]=[b("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),f("div",je,[t[52]||(t[52]=f("span",{class:"detail-label"},"平台",-1)),w(st,{modelValue:e.platform,"onUpdate:modelValue":t=>e.platform=t,onChange:t=>(e=>{const t="all",a=_l.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.platform.length?e.platform.includes(t)?1===e.platform.length?e.platform=[t,...a]:e.platform.length>1&&(e.platform[e.platform.length-1]===t?e.platform=[t,...a]:e.platform=e.platform.filter((e=>e!==t))):e.platform.includes(t)||a.every((t=>e.platform.includes(t)))&&a.length>0&&(e.platform=[t,...a]):e.platform=[t]})(e)},{default:y((()=>[_l.value.length>0?(m(!0),_(k,{key:0},D(_l.value,(e=>(m(),A(it,{key:e.code,label:e.code},{default:y((()=>[b(h(e.name),1)])),_:2},1032,["label"])))),128)):(m(),A(it,{key:1,label:"all"},{default:y((()=>t[51]||(t[51]=[b("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])])])])])))),128))])])],512),f("div",Fe,[t[59]||(t[59]=f("div",{class:"section-title"},"出价",-1)),f("div",$e,[w(vt,{model:Ha,"label-width":"120px"},{default:y((()=>[w(gt,{label:"目标成本"},{default:y((()=>[w(ut,{modelValue:Ha.bid,"onUpdate:modelValue":t[3]||(t[3]=e=>Ha.bid=e),min:.1,max:100,step:.1,precision:2},null,8,["modelValue"]),t[56]||(t[56]=f("span",{class:"bid-unit"},"元",-1))])),_:1}),Ha.smartBid?(m(),A(gt,{key:0,label:"调价范围"},{default:y((()=>[f("div",Me,[w(ut,{modelValue:Ha.minBid,"onUpdate:modelValue":t[4]||(t[4]=e=>Ha.minBid=e),min:.1,max:Ha.bid,step:.1,precision:2},null,8,["modelValue","max"]),t[57]||(t[57]=f("span",{class:"range-separator"},"至",-1)),w(ut,{modelValue:Ha.maxBid,"onUpdate:modelValue":t[5]||(t[5]=e=>Ha.maxBid=e),min:Ha.bid,max:100,step:.1,precision:2},null,8,["modelValue","min"]),t[58]||(t[58]=f("span",{class:"bid-unit"},"元",-1))])])),_:1})):C("",!0)])),_:1},8,["model"])])],512),w(_t,{modelValue:$a.value,"onUpdate:modelValue":t[9]||(t[9]=e=>$a.value=e),size:"1000px","destroy-on-close":!0,direction:"rtl",class:"note-drawer"},{header:y((()=>[f("div",Be,[f("div",Oe,[(m(),_(k,null,D(cl,(e=>f("div",{key:e.key,class:T(["tab-item",{active:sl.value===e.key}]),onClick:t=>sl.value=e.key},h(e.name),11,Je))),64))])])])),default:y((()=>[f("div",qe,[f("div",Qe,[w(ot,{type:"textarea",modelValue:Ma.value,"onUpdate:modelValue":t[6]||(t[6]=e=>Ma.value=e),placeholder:"请输入笔记ID，多个ID之间使用回车、逗号或空格分隔",style:{width:"460px"},rows:3,autosize:{minRows:3,maxRows:5}},null,8,["modelValue"]),w(n,{type:"primary",class:"search-btn",onClick:tl},{default:y((()=>t[60]||(t[60]=[b(" 搜索并选中 ")]))),_:1})]),f("div",Ge,[w(n,{type:"primary",onClick:rl},{default:y((()=>t[61]||(t[61]=[b("确定")]))),_:1})])]),f("div",He,[f("div",We,[V((m(),A(r,{data:Ba.value,border:"","element-loading-text":"加载中...",onSelectionChange:ll,"row-key":e=>e.noteId,ref_key:"tableRef",ref:qa,"default-selection":Ja.value,height:"calc(100% - 54px)"},{default:y((()=>[w(a,{type:"selection",width:"55"}),w(a,{prop:"noteId",label:"笔记ID",width:"180"}),w(a,{label:"图片",width:"100"},{default:y((e=>[w(l,{src:e.row.image,preview:!1,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"])])),_:1}),w(a,{prop:"desc",label:"描述","show-overflow-tooltip":"","min-width":"300"},{default:y((e=>[f("div",Xe,h(e.row.desc),1)])),_:1}),w(a,{prop:"desc",label:"SPU","show-overflow-tooltip":"","min-width":"160"},{default:y((e=>[f("span",Ye,"累计绑定"+h(e.row.spu)+"个SPU",1)])),_:1}),w(a,{prop:"author",label:"作者",width:"120"}),w(a,{prop:"noteType",label:"笔记形式",width:"100"}),w(a,{prop:"createTime",label:"创建时间",width:"180"}),w(a,{label:"操作",width:"100",fixed:"right"},{default:y((e=>[w(n,{type:"primary",link:"",onClick:t=>(e=>{var t,a;al(e)?(Ja.value=Ja.value.filter((t=>t.noteId!==e.noteId)),null==(t=qa.value)||t.toggleRowSelection(e,!1),ja.value=ja.value.filter((t=>t.noteId!==e.noteId))):(Ja.value=[...Ja.value,e],null==(a=qa.value)||a.toggleRowSelection(e,!0),new Set(ja.value.map((e=>e.noteId))).has(e.noteId)||ja.value.push(e));Fa.note_ids=ja.value.map((e=>e.noteId)),Fa.notes=[...ja.value]})(e.row)},{default:y((()=>[b(h(al(e.row)?"取消选择":"点击选择"),1)])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","row-key","default-selection"])),[[yt,Xa.value]]),f("div",Ze,[w(pt,{"current-page":Wa.currentPage,"onUpdate:currentPage":t[7]||(t[7]=e=>Wa.currentPage=e),"page-size":Wa.pageSize,"onUpdate:pageSize":t[8]||(t[8]=e=>Wa.pageSize=e),"page-sizes":[10,20,50,100],total:Wa.total,onSizeChange:el,onCurrentChange:Za,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])])])])),_:1},8,["modelValue"]),w(ft,{modelValue:Rl.value,"onUpdate:modelValue":t[11]||(t[11]=e=>Rl.value=e),title:"关联定向包",width:"500px","destroy-on-close":""},{footer:y((()=>[f("span",lt,[w(n,{onClick:zl},{default:y((()=>t[63]||(t[63]=[b("取消")]))),_:1}),w(n,{type:"primary",onClick:Kl},{default:y((()=>t[64]||(t[64]=[b(" 确认关联 ")]))),_:1})])])),default:y((()=>[t[65]||(t[65]=f("span",{style:{"font-size":"12px",color:"#999","margin-bottom":"10px"}}," 已有定向包只展示当前广告场景下可用的定向包，高级定向中不包含蒲公英人群和部分平台精选中的人群 ",-1)),f("div",et,[f("div",tt,[t[62]||(t[62]=f("span",{style:{"font-size":"14px",width:"100px"}},"已有定向包",-1)),w(wt,{modelValue:Sl.value,"onUpdate:modelValue":t[10]||(t[10]=e=>Sl.value=e),placeholder:"请选择定向包",style:{width:"100%"}},{default:y((()=>[(m(!0),_(k,null,D(Ul.value,(e=>(m(),A(mt,{key:e.id,label:e.name,value:e.id},{default:y((()=>[f("span",null,h(e.name),1),f("span",at,h(e.type),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-a36d8d38"]]);export{ot as default};
