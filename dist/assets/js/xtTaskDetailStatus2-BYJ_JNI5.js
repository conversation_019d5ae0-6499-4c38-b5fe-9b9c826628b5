import{r as e,H as l,f as a,e as t,a as u,c as d,o,h as i,b as n,w as s,d as r,g as _,n as v,t as m,F as c,i as p,E as f}from"./index-C2bfFjZ1.js";import{a as b,e as w,d as g,f as h}from"./index-P0VwV2wd.js";import{_ as V}from"./_plugin-vue_export-helper-BXFjo1rG.js";const k={class:"container_form mt-2 px-2 pb-20"},x={class:"flex items-center"},y={class:"form_title flex",style:{width:"97%","justify-content":"space-between"}},U={class:"grey-6"},D={class:"mt-2"},j={key:0,class:"grid grid-cols-3 gap-4"},Y={key:1,class:"grid grid-cols-3 gap-4"},N={class:"flex items-center"},I={class:"overflow-auto",style:{height:"87vh"}},S={key:0,class:"px-4 overflow-auto pb-40"},C={class:"flex items-center"},M={class:"flex items-center"},q={class:"flex items-center"},H={class:"flex items-center"},E={class:"flex items-center"},J={class:"form-input"},L={class:"float-right"},O={key:1,class:"px-4 overflow-auto h-4/5 pb-40"},z={class:"flex items-center"},F={class:"flex items-center"},P={class:"flex items-center"},A={class:"flex items-center"},K={class:"float-right"},T=V({__name:"xtTaskDetailStatus2",props:["type","id","kolList","taskDetail"],emits:["addKolHandler","closeDrawer"],setup(V,{expose:T,emit:B}){const Q=e(""),R=e([]),G=e(""),W=e(""),X=e(!1);l();const Z=e(1),$=e(null),ee=e({}),le=e([]),ae=V,te=B,ue=e(""),de=e("");ae.kolList,ae&&(ue.value=ae.type,de.value=ae.id),a([()=>ae.kolList,()=>ae.taskDetail],(e=>{Q.value=ae.kolList,G.value=ae.taskDetail})),a((()=>ae.type),(e=>{ue.value=e})),a((()=>ae.id),(e=>{de.value=e,Q.value=ae.kolList,G.value=ae.taskDetail}));const oe=e({actual_amount_price:"",star_price:"",advertising_brand_id:"",advertising_doujia_id:"",advertising_doujia_tiktok_id:"",advertising_effect_id:"",advertising_qianchuan_id:"",anticipation_reserve_day:0,anticipation_time:null,change_status:0,component_copywriting:"",component_link:"",component_name:"",component_notes:"",customer_rebate:0,customer_service_price:0,gross_profit:"",gross_profit_margin:"",invest_status:[],kol_base_price:"",kol_licensing_fee:"",kol_name:"",mcn_name:"",other_notes:"",predict_cost:0,predict_receivable_customer_price:0,predict_receivable_medium_price:0,spu_bind:"",good_id:"",dandelion_order_price:""}),ie=e({}),ne=JSON.parse(localStorage.getItem("taskDetail"));ne&&(G.value=ne,ie.value=ne,Z.value=ne.platform,Q.value=ne.kol_data&&ne.kol_data.length>0?ne.kol_data:ne.kol_select_list,"info"==ue.value&&(ee.value=ne.order_personnel_object));const se={1:"指派任务",2:"招募任务",3:"投稿任务",4:"星广联投",5:"小红星",6:"小红盟",7:"蒲公英"},re={1:"投放效果广告",2:"投放品牌广告",3:"投放巨量千川",4:"投放至Dou+"},_e={1:{label:"1-20S视频",value:"price_1_20"},2:{label:"21-60S视频",value:"price_20_60"},3:{label:"60S以上视频",value:"price_60"},4:{label:"图文笔记一口价",value:"redbook_graphic"},5:{label:"视频笔记一口价",value:"redbook_video"}},ve={project_id:[{required:!0,message:"请选择项目名称",trigger:"change"}],task_type:[{required:!0,message:"请选择任务类型",trigger:"change"}],task_date:[{type:"array",required:!0,message:"请选择任务发布时间",trigger:"change"}],task_name:[{required:!0,trigger:"blur",validator:(e,l,a)=>{ie.value.area?ie.value.brand_name?ie.value.short_name?ie.value.task_time?a():a(new Error("请填写任务时间")):a(new Error("请填写客户简称")):a(new Error("请填写品牌")):a(new Error("请选择地区"))}}]},me=e=>{le.value.includes(e.id)?le.value=le.value.filter((l=>l!==e.id)):(le.value=[],le.value.push(e.id))},ce=e=>{ie.alliance_personnel_id=e.id,ie.alliance_personnel=e.name},pe=()=>{let e=0,{kol_base_price:l,kol_licensing_fee:a,actual_amount_price:t,change_status:u,star_price:d,predict_receivable_medium_price:o,customer_rebate:i,customer_service_price:n,mcn_name:s,anticipation_time:r,anticipation_reserve_day:_,component_name:v,component_copywriting:m,component_link:c,component_notes:p,other_notes:f,invest_status:b,advertising_effect_id:w,advertising_brand_id:g,advertising_qianchuan_id:V,advertising_doujia_id:k,advertising_doujia_tiktok_id:x,spu_bind:y,good_id:U,dandelion_order_price:D,product_name:j,report_brand:Y}=oe.value;if(5==Z.value&&Q.value.length>0){let y=Q.value;y.map(((h,y)=>{h.platform_uid==W.value.platform_uid&&(e=y,h.kol_base_price=l||0,h.kol_licensing_fee=a||0,h.actual_amount_price=t||0,h.change_status=u,h.star_price=d||0,h.predict_receivable_medium_price=o||0,h.customer_rebate=i||0,h.customer_service_price=n||0,h.mcn_name=s,h.anticipation_time=r||"",h.anticipation_reserve_day=_||0,h.component_name=v,h.component_copywriting=m,h.component_link=c,h.component_notes=p,h.other_notes=f,h.invest_status=b&&0!=b?b.join(","):"",h.advertising_effect_id=w,h.advertising_brand_id=g,h.advertising_qianchuan_id=V,h.advertising_doujia_id=k,h.advertising_doujia_tiktok_id=x,h.predict_cost=be||0,h.predict_receivable_customer_price=we,h.gross_profit=ge||0,h.gross_profit_margin=he||0,me(h))})),h({id:G.value.task_id,platform:G.value.platform,status:G.value.status,kol_data:[y[e]]}).then((e=>{Q.value=JSON.parse(JSON.stringify(y)),X.value=!1}))}else{let a=Q.value;a.map((e=>{e.platform_uid==W.value.platform_uid&&(e.kol_base_price=l||0,e.actual_amount_price=t||0,e.predict_receivable_medium_price=o||0,e.customer_rebate=i||0,e.customer_service_price=n||0,e.mcn_name=s,e.anticipation_time=r||"",e.other_notes=f,e.predict_cost=be||0,e.predict_receivable_customer_price=we||0,e.gross_profit=ge||0,e.gross_profit_margin=he||0,e.spu_bind=y,e.good_id=U,e.product_name=j,e.dandelion_order_price=D||0,e.report_brand=Y,me(e))})),h({id:G.value.task_id,platform:G.value.platform,status:G.value.status,kol_data:[a[e]]}).then((e=>{Q.value=JSON.parse(JSON.stringify(a)),X.value=!1}))}localStorage.setItem("kolList",JSON.stringify(Q.value))},fe=()=>{oe.value.star_price=(1.05*(Number(oe.value.kol_base_price)+Number(oe.value.kol_licensing_fee))).toFixed(2)},be=t((()=>{let e=0,l=oe.value.actual_amount_price>0?oe.value.actual_amount_price:0,a=oe.value.predict_receivable_medium_price>0?oe.value.predict_receivable_medium_price:0;return e=Number(l)-Number(a),e>0?e:0})),we=t((()=>{let e=0,l=oe.value.actual_amount_price>0?oe.value.actual_amount_price:0,a=oe.value.customer_rebate>0?oe.value.customer_rebate:0,t=oe.value.customer_service_price>0?oe.value.customer_service_price:0;return e=Number(l)-Number(a)+Number(t),e>0?e:0})),ge=t((()=>{let e=0,l=we.value>0?we.value:0,a=be.value>0?be.value:0;return e=Number(l)-Number(a),e>0?e:0})),he=t((()=>{let e=0,l=ge.value>0?Number(ge.value):0,a=Number(we.value)>0?Number(we.value):0;return l&&a&&(e=(Number(l)/Number(a)*100).toFixed(2)),e})),Ve=e=>{let l="";return e&&e.split(",").map((e=>{l+=re[e]+","})),l},ke=()=>{te("addKolHandler")},xe=async e=>{let l=ye();return!l.kol_data.filter((e=>2==e.order_status)).length>0?(f.warning("请先选择下单达人"),!1):l.order_personnel?void($.value&&await $.value.validate(((a,t)=>{a&&(l.id=de.value,l.status=e,w(l).then((e=>{f.success(e.msg),te("closeDrawer","refresh")})))}))):(f.warning("请先指派下单人员"),!1)},ye=e=>{const l=[];return"add"==ue.value?(ie.value.area,ie.value.short_name,ie.value.brand_name,ie.value.task_time):(ie.value.task_name.split("-")[0],ie.value.area,ie.value.short_name,ie.value.brand_name,ie.value.task_time),Q.value.length>0&&Q.value.map((e=>{e.cooperation_type=e.placement_type,l.push(e)})),{status:e,task_name:ie.value.task_name,area:ie.value.area,task_time:ie.value.task_time,short_name:ie.value.short_name,brand_name:ie.value.brand_name,project_id:ie.value.project_id,other_demand:ie.value.other_demand,marketing_target:ie.value.marketing_target,task_type:ie.value.task_type,platform_status:ie.value.platform_status,platform:G.value.platform,anticipation_time:ie.value.anticipation_time,order_personnel:ee.value.order_personnel,order_personnel_id:ee.value.order_personnel_id,kol_data:l}};return b({role_id:86}).then((e=>{R.value=e.data.list})),T({onSubmit:xe}),(e,l)=>{const a=u("el-input"),t=u("el-form-item"),f=u("el-col"),b=u("el-row"),w=u("el-radio"),h=u("el-radio-group"),V=u("QuestionFilled"),T=u("el-icon"),B=u("el-tooltip"),Z=u("el-option"),ae=u("el-select"),de=u("el-date-picker"),ne=u("el-button"),re=u("el-checkbox"),ye=u("el-table-column"),Ue=u("el-popconfirm"),De=u("el-table"),je=u("el-form"),Ye=u("el-checkbox-group"),Ne=u("el-drawer");return o(),d("div",null,[i("div",k,[n(je,{model:ie.value,ref_key:"ruleFormRef",ref:$,rules:ve,"label-width":"110px","label-position":"right"},{default:s((()=>{var e,u;return[l[111]||(l[111]=i("div",{class:"form_title"},[i("h3",null,"基本信息")],-1)),n(b,{gutter:24},{default:s((()=>[n(f,{span:10},{default:s((()=>[n(t,{label:"项目名称",prop:"project_id"},{default:s((()=>{var e;return[n(a,{modelValue:ie.value.project_name,"onUpdate:modelValue":l[0]||(l[0]=e=>ie.value.project_name=e),disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status)},null,8,["modelValue","disabled"])]})),_:1})])),_:1}),n(f,{span:10},{default:s((()=>[n(t,{label:"营销目标",prop:"marketing_target"},{default:s((()=>{var e;return[n(a,{"show-word-limit":"",maxlength:"20",disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status),modelValue:ie.value.marketing_target,"onUpdate:modelValue":l[1]||(l[1]=e=>ie.value.marketing_target=e)},null,8,["disabled","modelValue"])]})),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:10},{default:s((()=>[n(t,{label:"任务类型"},{default:s((()=>{var e;return[n(a,{modelValue:se[ie.value.task_type],"onUpdate:modelValue":l[2]||(l[2]=e=>se[ie.value.task_type]=e),disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status)},null,8,["modelValue","disabled"])]})),_:1})])),_:1}),n(f,{span:10},{default:s((()=>[n(t,{label:"平台任务"},{default:s((()=>{var e;return[n(h,{modelValue:ie.value.platform_status,"onUpdate:modelValue":l[3]||(l[3]=e=>ie.value.platform_status=e),disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status)},{default:s((()=>[n(w,{label:1},{default:s((()=>l[60]||(l[60]=[r("是")]))),_:1}),n(w,{label:0},{default:s((()=>l[61]||(l[61]=[r("否")]))),_:1})])),_:1},8,["modelValue","disabled"])]})),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:10},{default:s((()=>[n(t,{label:"任务名称",prop:"task_name"},{default:s((()=>{var e,t,u,d;return[i("div",x,[n(B,{class:"relative right-24",effect:"dark",content:"任务名称由自增码_地区_客户简称_品牌名称_时间构成，自增码系统会自动生成，无需填写",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(ae,{modelValue:ie.value.area,"onUpdate:modelValue":l[4]||(l[4]=e=>ie.value.area=e),style:{width:"90px","margin-left":"-15px"},placeholder:"地区",disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status)},{default:s((()=>[n(Z,{value:"华南"}),n(Z,{value:"华北"}),n(Z,{value:"华东"}),n(Z,{value:"伊利"})])),_:1},8,["modelValue","disabled"]),n(a,{style:{width:"130px"},placeholder:"简称",modelValue:ie.value.short_name,"onUpdate:modelValue":l[5]||(l[5]=e=>ie.value.short_name=e),disabled:"info"==ue.value&&2==(null==(t=G.value)?void 0:t.status)},null,8,["modelValue","disabled"]),n(a,{style:{width:"180px"},placeholder:"请填写品牌",modelValue:ie.value.brand_name,"onUpdate:modelValue":l[6]||(l[6]=e=>ie.value.brand_name=e),disabled:"info"==ue.value&&2==(null==(u=G.value)?void 0:u.status)},null,8,["modelValue","disabled"]),n(a,{style:{width:"130px"},modelValue:ie.value.task_time,"onUpdate:modelValue":l[7]||(l[7]=e=>ie.value.task_time=e),disabled:"info"==ue.value&&2==(null==(d=G.value)?void 0:d.status)},null,8,["modelValue","disabled"])])]})),_:1})])),_:1})])),_:1}),l[112]||(l[112]=i("div",{class:"form_title"},[i("h3",null,"任务要求")],-1)),n(b,{gutter:24},{default:s((()=>[n(f,{span:10},{default:s((()=>[n(t,{label:"期望发布时间",prop:"task_date"},{default:s((()=>{var e;return[n(de,{modelValue:ie.value.anticipation_time,"onUpdate:modelValue":l[8]||(l[8]=e=>ie.value.anticipation_time=e),type:"date",disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status),style:{width:"310px"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",placeholder:"期望发布时间"},null,8,["modelValue","disabled"])]})),_:1})])),_:1}),n(f,{span:10},{default:s((()=>[n(t,{label:"其他要求"},{default:s((()=>{var e;return[n(a,{modelValue:ie.value.other_demand,"onUpdate:modelValue":l[9]||(l[9]=e=>ie.value.other_demand=e),disabled:"info"==ue.value&&2==(null==(e=G.value)?void 0:e.status)},null,8,["modelValue","disabled"])]})),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:13})])),_:1}),i("div",y,[l[63]||(l[63]=i("h3",null,"达人信息",-1)),"info"===ue.value||1!=(null==(e=G.value)?void 0:e.status)&&"reEdit2"!=ue.value?v("",!0):(o(),_(ne,{key:0,size:"small",onClick:ke},{default:s((()=>l[62]||(l[62]=[r("新增达人")]))),_:1}))]),i("p",U,"媒体平台："+m(5==(null==(u=ie.value)?void 0:u.platform)?"抖音":"小红书"),1),i("div",D,[n(De,{data:Q.value,border:"",style:{width:"100%"},ref:"expandTable","row-key":"id","expand-row-keys":le.value,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"}},{default:s((()=>{var e;return[n(ye,{width:"55"},{default:s((e=>{var l,a;return[n(re,{onChange:l=>{return a=l,void((null==e?void 0:e.row).order_status=a?2:1);var a},disabled:"info"==ue.value||2==(null==(l=G.value)?void 0:l.status),checked:2==(null==(a=e.row)?void 0:a.order_status)},null,8,["onChange","disabled","checked"])]})),_:1}),n(ye,{type:"expand",width:"1"},{default:s((e=>{var a,t,u,n,s,_,v,c,p,f,b,w,g,h,V,k,x,y,U,D,N,I,S,C,M,q,H,E,J,L,O,z,F,P,A,K,T,B,Q,R,G,W,X;return[5==ie.value.platform?(o(),d("div",j,[i("div",null,[l[64]||(l[64]=i("span",{class:"font-semibold"},"达人裸价（不含服务费）",-1)),r(":"+m(null==(a=null==e?void 0:e.row)?void 0:a.kol_base_price),1)]),i("div",null,[l[65]||(l[65]=i("span",{class:"font-semibold"},"达人授权费（不含服务费）",-1)),r(": "+m(null==(t=null==e?void 0:e.row)?void 0:t.kol_licensing_fee),1)]),i("div",null,[l[66]||(l[66]=i("span",{class:"font-semibold"},"实际下单价（改价后含服务费）",-1)),r("："+m(null==(u=null==e?void 0:e.row)?void 0:u.actual_amount_price),1)]),i("div",null,[l[67]||(l[67]=i("span",{class:"font-semibold"},"是否改价",-1)),r("："+m(0==(null==(n=null==e?void 0:e.row)?void 0:n.change_status)?"否":"是"),1)]),i("div",null,[l[68]||(l[68]=i("span",{class:"font-semibold"},"星图价格（含服务费）",-1)),r("："+m(null==(s=null==e?void 0:e.row)?void 0:s.star_price),1)]),i("div",null,[l[69]||(l[69]=i("span",{class:"font-semibold"},"预估应收媒体返点",-1)),r("："+m(null==(_=null==e?void 0:e.row)?void 0:_.predict_receivable_medium_price),1)]),i("div",null,[l[70]||(l[70]=i("span",{class:"font-semibold"},"预估成本",-1)),r("："+m(null==(v=null==e?void 0:e.row)?void 0:v.predict_cost),1)]),i("div",null,[l[71]||(l[71]=i("span",{class:"font-semibold"},"客户返佣",-1)),r("："+m(null==(c=null==e?void 0:e.row)?void 0:c.customer_rebate),1)]),i("div",null,[l[72]||(l[72]=i("span",{class:"font-semibold"},"客户服务费",-1)),r("："+m(null==(p=null==e?void 0:e.row)?void 0:p.customer_service_price),1)]),i("div",null,[l[73]||(l[73]=i("span",{class:"font-semibold"},"预估应收客户款",-1)),r("："+m(null==(f=null==e?void 0:e.row)?void 0:f.predict_receivable_customer_price),1)]),i("div",null,[l[74]||(l[74]=i("span",{class:"font-semibold"},"毛利",-1)),r("："+m(null==(b=null==e?void 0:e.row)?void 0:b.gross_profit),1)]),i("div",null,[l[75]||(l[75]=i("span",{class:"font-semibold"},"毛利率",-1)),r("："+m(null==(w=null==e?void 0:e.row)?void 0:w.gross_profit_margin),1)]),i("div",null,[l[76]||(l[76]=i("span",{class:"font-semibold"},"达人所属机构",-1)),r("："+m(null==(g=null==e?void 0:e.row)?void 0:g.mcn_name),1)]),i("div",null,[l[77]||(l[77]=i("span",{class:"font-semibold"},"期望发布日期",-1)),r("："+m(null==(h=null==e?void 0:e.row)?void 0:h.anticipation_time),1)]),i("div",null,[l[78]||(l[78]=i("span",{class:"font-semibold"},"期望保留时长",-1)),r("："+m(null==(V=null==e?void 0:e.row)?void 0:V.anticipation_reserve_day),1)]),i("div",null,[l[79]||(l[79]=i("span",{class:"font-semibold"},"组件名称",-1)),r("："+m(null==(k=null==e?void 0:e.row)?void 0:k.component_name),1)]),i("div",null,[l[80]||(l[80]=i("span",{class:"font-semibold"},"组件-文案",-1)),r("："+m(null==(x=null==e?void 0:e.row)?void 0:x.component_copywriting),1)]),i("div",null,[l[81]||(l[81]=i("span",{class:"font-semibold"},"组件-链接",-1)),r("："+m(null==(y=null==e?void 0:e.row)?void 0:y.component_link),1)]),i("div",null,[l[82]||(l[82]=i("span",{class:"font-semibold"},"组件-备注",-1)),r("："+m(null==(U=null==e?void 0:e.row)?void 0:U.component_notes),1)]),i("div",null,[l[83]||(l[83]=i("span",{class:"font-semibold"},"其他特殊备注",-1)),r("："+m(null==(D=null==e?void 0:e.row)?void 0:D.other_notes),1)]),i("div",null,[l[84]||(l[84]=i("span",{class:"font-semibold"},"是否投放广告",-1)),r("："+m(Ve(null==(N=null==e?void 0:e.row)?void 0:N.invest_status)),1)]),i("div",null,[l[85]||(l[85]=i("span",{class:"font-semibold"},"巨量广告广告主ID（效果广告）",-1)),r("："+m(null==(I=null==e?void 0:e.row)?void 0:I.advertising_effect_id),1)]),i("div",null,[l[86]||(l[86]=i("span",{class:"font-semibold"},"巨量广告广告主ID（品牌广告）",-1)),r("："+m(null==(S=null==e?void 0:e.row)?void 0:S.advertising_brand_id),1)]),i("div",null,[l[87]||(l[87]=i("span",{class:"font-semibold"},"巨量千川广告主ID",-1)),r("："+m(null==(C=null==e?void 0:e.row)?void 0:C.advertising_qianchuan_id),1)]),i("div",null,[l[88]||(l[88]=i("span",{class:"font-semibold"},"Dou+广告主ID",-1)),r("："+m(null==(M=null==e?void 0:e.row)?void 0:M.advertising_doujia_id),1)]),i("div",null,[l[89]||(l[89]=i("span",{class:"font-semibold"},"Dou+广告主抖音UID",-1)),r("："+m(null==(q=null==e?void 0:e.row)?void 0:q.advertising_doujia_tiktok_id),1)])])):(o(),d("div",Y,[i("div",null,[l[90]||(l[90]=i("span",{class:"font-semibold"},"平台裸价（不含服务费）",-1)),r(":"+m(null==(H=null==e?void 0:e.row)?void 0:H.kol_base_price),1)]),i("div",null,[l[91]||(l[91]=i("span",{class:"font-semibold"},"蒲公英下单价（含服务费）",-1)),r(": "+m(null==(E=null==e?void 0:e.row)?void 0:E.dandelion_order_price),1)]),i("div",null,[l[92]||(l[92]=i("span",{class:"font-semibold"},"实际下单价（改价后含服务费）",-1)),r("："+m(null==(J=null==e?void 0:e.row)?void 0:J.actual_amount_price),1)]),i("div",null,[l[93]||(l[93]=i("span",{class:"font-semibold"},"备注",-1)),r("："+m(null==(L=null==e?void 0:e.row)?void 0:L.other_notes),1)]),i("div",null,[l[94]||(l[94]=i("span",{class:"font-semibold"},"预估成本",-1)),r("："+m(null==(O=null==e?void 0:e.row)?void 0:O.predict_cost),1)]),i("div",null,[l[95]||(l[95]=i("span",{class:"font-semibold"},"预估应收媒体返点",-1)),r("："+m(null==(z=null==e?void 0:e.row)?void 0:z.predict_receivable_medium_price),1)]),i("div",null,[l[96]||(l[96]=i("span",{class:"font-semibold"},"客户返佣",-1)),r("："+m(null==(F=null==e?void 0:e.row)?void 0:F.customer_rebate),1)]),i("div",null,[l[97]||(l[97]=i("span",{class:"font-semibold"},"客户服务费",-1)),r("："+m(null==(P=null==e?void 0:e.row)?void 0:P.customer_service_price),1)]),i("div",null,[l[98]||(l[98]=i("span",{class:"font-semibold"},"预估应收客户款",-1)),r("："+m(null==(A=null==e?void 0:e.row)?void 0:A.predict_receivable_customer_price),1)]),i("div",null,[l[99]||(l[99]=i("span",{class:"font-semibold"},"毛利",-1)),r("："+m(null==(K=null==e?void 0:e.row)?void 0:K.gross_profit),1)]),i("div",null,[l[100]||(l[100]=i("span",{class:"font-semibold"},"毛利率",-1)),r("："+m(null==(T=null==e?void 0:e.row)?void 0:T.gross_profit_margin),1)]),i("div",null,[l[101]||(l[101]=i("span",{class:"font-semibold"},"达人所属机构",-1)),r("："+m(null==(B=null==e?void 0:e.row)?void 0:B.mcn_name),1)]),i("div",null,[l[102]||(l[102]=i("span",{class:"font-semibold"},"期望发布日期",-1)),r("："+m(null==(Q=null==e?void 0:e.row)?void 0:Q.anticipation_time),1)]),i("div",null,[l[103]||(l[103]=i("span",{class:"font-semibold"},"报备品牌（务必精准对应品牌专业号）",-1)),r("："+m(null==(R=null==e?void 0:e.row)?void 0:R.report_brand),1)]),i("div",null,[l[104]||(l[104]=i("span",{class:"font-semibold"},"产品名称",-1)),r("："+m(null==(G=null==e?void 0:e.row)?void 0:G.product_name),1)]),i("div",null,[l[105]||(l[105]=i("span",{class:"font-semibold"},"是否绑定SPU（提供准确名称/SPUID）",-1)),r("："+m(null==(W=null==e?void 0:e.row)?void 0:W.spu_bind),1)]),i("div",null,[l[106]||(l[106]=i("span",{class:"font-semibold"},"商品ID",-1)),r("："+m(null==(X=null==e?void 0:e.row)?void 0:X.good_id),1)])]))]})),_:1}),n(ye,{prop:"kol_name",label:"达人名称",width:"180"}),n(ye,{prop:"platform_uid",label:"达人ID",width:"180"}),n(ye,{prop:"name",label:"合作形式",width:"180"},{default:s((e=>{var l,a,t,u,d;return[r(m((null==(l=e.row)?void 0:l.placement_type)?null==(t=_e[null==(a=e.row)?void 0:a.placement_type])?void 0:t.label:null==(d=_e[null==(u=e.row)?void 0:u.cooperation_type])?void 0:d.label),1)]})),_:1}),n(ye,{prop:"name",label:"平台裸价"},{default:s((e=>{var l,a,t;return[i("div",null,m((null==(l=e.row)?void 0:l.price)?null==(a=e.row)?void 0:a.price:null==(t=e.row)?void 0:t.kol_base_price),1)]})),_:1}),n(ye,{prop:"name",label:"合作返点"},{default:s((e=>{var l;return[r(m(100*(null==(l=e.row)?void 0:l.rebate_ratio))+"% ",1)]})),_:1}),1==(null==(e=G.value)?void 0:e.status)||"reEdit2"==ue.value?(o(),_(ye,{key:0,prop:"name",label:"操作",width:"250px"},{default:s((e=>[i("div",N,[n(ne,{size:"small",type:"primary",onClick:l=>(async e=>{for(var l in oe.value={actual_amount_price:0,star_price:"",advertising_brand_id:"",advertising_doujia_id:"",advertising_doujia_tiktok_id:"",advertising_effect_id:"",advertising_qianchuan_id:"",anticipation_reserve_day:0,anticipation_time:null,change_status:0,component_copywriting:"",component_link:"",component_name:"",component_notes:"",customer_rebate:0,customer_service_price:0,gross_profit:"",gross_profit_margin:"",invest_status:[],kol_base_price:"",kol_licensing_fee:"",kol_name:"",mcn_name:"",other_notes:"",predict_cost:0,predict_receivable_customer_price:0,predict_receivable_medium_price:0,spu_bind:"",good_id:"",dandelion_order_price:0},X.value=!0,oe.value.kol_base_price=e.price,W.value=e,oe.value)e[l]&&0!=e[l]&&""!==e[l]&&null!==e[l]&&(oe.value[l]=e[l]),"invest_status"==l&&(oe.value.invest_status=e[l]?e[l].split(","):[]);await g({platform:e.platform,platform_uid:e.platform_uid}).then((e=>{let l=e.data.mcn_name?e.data.mcn_name:"";oe.value.mcn_name=l}))})(e.row)},{default:s((()=>l[107]||(l[107]=[r("填写建联信息")]))),_:2},1032,["onClick"]),n(ne,{link:"",size:"small",class:"btnClass",onClick:l=>me(e.row)},{default:s((()=>{var l;return[r(m((null==(l=null==e?void 0:e.row)?void 0:l.id)==le.value[0]?"收起":"展开"),1)]})),_:2},1032,["onClick"]),n(Ue,{title:"确定删除当前达人么?",onConfirm:l=>{var a;return(e=>{let l=Q.value.findIndex((l=>l.id==e));Q.value.splice(l,1),localStorage.setItem("kolList",JSON.stringify(Q.value))})(null==(a=e.row)?void 0:a.id)},onCancel:l[10]||(l[10]=()=>{})},{reference:s((()=>[n(ne,{size:"small"},{default:s((()=>l[108]||(l[108]=[r("删除")]))),_:1})])),_:2},1032,["onConfirm"])])])),_:1})):v("",!0)]})),_:1},8,["data","expand-row-keys"])]),l[113]||(l[113]=i("div",{class:"form_title mt-2"},[i("h3",null,"指派下单人员")],-1)),n(t,{label:"指派下单人员"},{default:s((()=>{var e;return["edit"==ue.value&&1==(null==(e=G.value)?void 0:e.status)||"reEdit2"==ue.value?(o(),_(ae,{key:0,style:{width:"200px"},modelValue:ee.value,"onUpdate:modelValue":l[11]||(l[11]=e=>ee.value=e),"value-key":"index",clearable:"",onChange:ce,placeholder:"请指派下单人员"},{default:s((()=>[(o(!0),d(c,null,p(R.value,((e,l)=>(o(),_(Z,{label:e.name,value:{order_personnel_id:e.id,order_personnel:e.name,index:l}},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])):(o(),_(a,{key:1,class:"w-20",modelValue:ie.value.order_personnel,"onUpdate:modelValue":l[12]||(l[12]=e=>ie.value.order_personnel=e),disabled:""},null,8,["modelValue"]))]})),_:1}),n(t,{class:"form-footer"},{default:s((()=>{var e;return[n(ne,{type:"primary",onClick:l[13]||(l[13]=e=>{te("closeDrawer","back")})},{default:s((()=>l[109]||(l[109]=[r("返回")]))),_:1}),1==(null==(e=G.value)?void 0:e.status)||"reEdit2"==ue.value?(o(),_(ne,{key:0,type:"primary",onClick:l[14]||(l[14]=e=>xe(2))},{default:s((()=>l[110]||(l[110]=[r("提交")]))),_:1})):v("",!0)]})),_:1})]})),_:1},8,["model"])]),n(Ne,{modelValue:X.value,"onUpdate:modelValue":l[59]||(l[59]=e=>X.value=e),size:"50%",title:"建联信息","show-close":!0},{default:s((()=>[i("div",I,[5==ie.value.platform?(o(),d("div",S,[n(je,{model:e.form},{default:s((()=>[l[122]||(l[122]=i("div",{class:"form_title mt-2"},[i("h3",null,"价格信息")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"达人裸价（不含服务费）","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.kol_base_price,"onUpdate:modelValue":l[15]||(l[15]=e=>oe.value.kol_base_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"达人授权费（不含服务费）","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.kol_licensing_fee,"onUpdate:modelValue":l[16]||(l[16]=e=>oe.value.kol_licensing_fee=e),onBlur:fe},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"星图价格（含服务费）","label-width":"170px"},{default:s((()=>[i("div",C,[n(B,{class:"relative right-3 mr-2",effect:"dark",content:"星图价格=(达人星图裸价+达人授权费裸价）*1.05",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:oe.value.star_price,"onUpdate:modelValue":l[17]||(l[17]=e=>oe.value.star_price=e)},null,8,["modelValue"])])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"是否改价","label-width":"180px"},{default:s((()=>[n(h,{modelValue:oe.value.change_status,"onUpdate:modelValue":l[18]||(l[18]=e=>oe.value.change_status=e)},{default:s((()=>[n(w,{label:1},{default:s((()=>l[114]||(l[114]=[r("是")]))),_:1}),n(w,{label:0},{default:s((()=>l[115]||(l[115]=[r("否")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"预估应收媒体返点","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.predict_receivable_medium_price,"onUpdate:modelValue":l[19]||(l[19]=e=>oe.value.predict_receivable_medium_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"实际下单价（改价后含服务费）","label-width":"210px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.actual_amount_price,"onUpdate:modelValue":l[20]||(l[20]=e=>oe.value.actual_amount_price=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"预估成本","label-width":"170px"},{default:s((()=>[i("div",M,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"预估成本=实际下单价格-预估应收媒体返点 ",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:be.value,"onUpdate:modelValue":l[21]||(l[21]=e=>be.value=e)},null,8,["modelValue"])])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"客户返佣","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.customer_rebate,"onUpdate:modelValue":l[22]||(l[22]=e=>oe.value.customer_rebate=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"客户服务费","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.customer_service_price,"onUpdate:modelValue":l[23]||(l[23]=e=>oe.value.customer_service_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"预估应收客户款","label-width":"180px"},{default:s((()=>[i("div",q,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"预估应收客户款=实际下单价格-客户返佣+客户服务费",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:we.value,"onUpdate:modelValue":l[24]||(l[24]=e=>we.value=e)},null,8,["modelValue"])])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"毛利","label-width":"170px"},{default:s((()=>[i("div",H,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"毛利=预估应收客户款-预估成本",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:ge.value,"onUpdate:modelValue":l[25]||(l[25]=e=>ge.value=e)},null,8,["modelValue"])])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"毛利率","label-width":"180px"},{default:s((()=>[i("div",E,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"毛利率=毛利/预估应收客户款*100%",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:he.value,"onUpdate:modelValue":l[26]||(l[26]=e=>he.value=e)},{append:s((()=>l[116]||(l[116]=[r(" % ")]))),_:1},8,["modelValue"])])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"达人所属机构","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.mcn_name,"onUpdate:modelValue":l[27]||(l[27]=e=>oe.value.mcn_name=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),l[123]||(l[123]=i("div",{class:"form_title mt-2"},[i("h3",null,"发布时间")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"期望发布日期","label-width":"170px"},{default:s((()=>[n(de,{modelValue:oe.value.anticipation_time,"onUpdate:modelValue":l[28]||(l[28]=e=>oe.value.anticipation_time=e),type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"期望发布时间"},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"期望保留时长","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.anticipation_reserve_day,"onUpdate:modelValue":l[29]||(l[29]=e=>oe.value.anticipation_reserve_day=e)},{append:s((()=>l[117]||(l[117]=[r(" 天 ")]))),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})]),l[124]||(l[124]=i("div",{class:"form_title mt-2"},[i("h3",null,"组件信息")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"组件名称","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.component_name,"onUpdate:modelValue":l[30]||(l[30]=e=>oe.value.component_name=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"组件-文案","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.component_copywriting,"onUpdate:modelValue":l[31]||(l[31]=e=>oe.value.component_copywriting=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"组件-链接","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.component_link,"onUpdate:modelValue":l[32]||(l[32]=e=>oe.value.component_link=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"组件-备注","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.component_notes,"onUpdate:modelValue":l[33]||(l[33]=e=>oe.value.component_notes=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"其他特殊备注","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.other_notes,"onUpdate:modelValue":l[34]||(l[34]=e=>oe.value.other_notes=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),l[125]||(l[125]=i("div",{class:"form_title mt-2"},[i("h3",null,"其他要求")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"是否投放广告","label-width":"160px"},{default:s((()=>[n(Ye,{modelValue:oe.value.invest_status,"onUpdate:modelValue":l[35]||(l[35]=e=>oe.value.invest_status=e)},{default:s((()=>[n(re,{label:"1"},{default:s((()=>l[118]||(l[118]=[r("投放效果广告")]))),_:1}),n(re,{label:"2"},{default:s((()=>l[119]||(l[119]=[r("投放品牌广告")]))),_:1}),n(re,{label:"3"},{default:s((()=>l[120]||(l[120]=[r("投放巨量千川")]))),_:1}),n(re,{label:"4"},{default:s((()=>l[121]||(l[121]=[r("投放至Dou+")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>{var e,u,d,r,m,c,p,f,b,w;return[i("div",J,[(null==(u=null==(e=oe.value)?void 0:e.invest_status)?void 0:u.includes("1"))?(o(),_(t,{key:0,label:"巨量广告广告主ID（效果广告）","label-width":"220px"},{default:s((()=>[n(a,{class:"w-full input-h",modelValue:oe.value.advertising_effect_id,"onUpdate:modelValue":l[36]||(l[36]=e=>oe.value.advertising_effect_id=e)},null,8,["modelValue"])])),_:1})):v("",!0),(null==(r=null==(d=oe.value)?void 0:d.invest_status)?void 0:r.includes("2"))?(o(),_(t,{key:1,label:"巨量广告广告主ID（品牌广告）","label-width":"220px"},{default:s((()=>[n(a,{class:"w-full input-h",modelValue:oe.value.advertising_brand_id,"onUpdate:modelValue":l[37]||(l[37]=e=>oe.value.advertising_brand_id=e)},null,8,["modelValue"])])),_:1})):v("",!0),(null==(c=null==(m=oe.value)?void 0:m.invest_status)?void 0:c.includes("3"))?(o(),_(t,{key:2,label:"巨量千川广告主ID","label-width":"220px"},{default:s((()=>[n(a,{class:"w-full input-h",modelValue:oe.value.advertising_qianchuan_id,"onUpdate:modelValue":l[38]||(l[38]=e=>oe.value.advertising_qianchuan_id=e)},null,8,["modelValue"])])),_:1})):v("",!0),(null==(f=null==(p=oe.value)?void 0:p.invest_status)?void 0:f.includes("4"))?(o(),_(t,{key:3,label:"Dou+广告主ID","label-width":"220px"},{default:s((()=>[n(a,{class:"w-full input-h",modelValue:oe.value.advertising_doujia_id,"onUpdate:modelValue":l[39]||(l[39]=e=>oe.value.advertising_doujia_id=e)},null,8,["modelValue"])])),_:1})):v("",!0),(null==(w=null==(b=oe.value)?void 0:b.invest_status)?void 0:w.includes("4"))?(o(),_(t,{key:4,label:"Dou+广告主抖音UID","label-width":"220px"},{default:s((()=>[n(a,{class:"w-full input-h",modelValue:oe.value.advertising_doujia_tiktok_id,"onUpdate:modelValue":l[40]||(l[40]=e=>oe.value.advertising_doujia_tiktok_id=e)},null,8,["modelValue"])])),_:1})):v("",!0)])]})),_:1})])),_:1})])])),_:1},8,["model"]),i("div",L,[n(ne,{onClick:l[41]||(l[41]=e=>X.value=!1)},{default:s((()=>l[126]||(l[126]=[r("取消")]))),_:1}),n(ne,{type:"primary",onClick:pe},{default:s((()=>l[127]||(l[127]=[r("确定")]))),_:1})])])):(o(),d("div",O,[n(je,{model:e.form},{default:s((()=>[l[129]||(l[129]=i("div",{class:"form_title mt-2"},[i("h3",null,"价格信息")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"达人裸价（不含服务费）","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.kol_base_price,"onUpdate:modelValue":l[42]||(l[42]=e=>oe.value.kol_base_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"蒲公英下单价（含服务费）","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.dandelion_order_price,"onUpdate:modelValue":l[43]||(l[43]=e=>oe.value.dandelion_order_price=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"实际下单价（改价后含服务费）","label-width":"210px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.actual_amount_price,"onUpdate:modelValue":l[44]||(l[44]=e=>oe.value.actual_amount_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"备注","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.other_notes,"onUpdate:modelValue":l[45]||(l[45]=e=>oe.value.other_notes=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"预估应收媒体返点","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.predict_receivable_medium_price,"onUpdate:modelValue":l[46]||(l[46]=e=>oe.value.predict_receivable_medium_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"预估成本","label-width":"170px"},{default:s((()=>[i("div",z,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"预估成本=实际下单价格-预估应收媒体返点 ",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:be.value,"onUpdate:modelValue":l[47]||(l[47]=e=>be.value=e)},null,8,["modelValue"])])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"客户服务费","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.customer_service_price,"onUpdate:modelValue":l[48]||(l[48]=e=>oe.value.customer_service_price=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"客户返佣","label-width":"180px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.customer_rebate,"onUpdate:modelValue":l[49]||(l[49]=e=>oe.value.customer_rebate=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"预估应收客户款","label-width":"180px"},{default:s((()=>[i("div",F,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"预估应收客户款=实际下单价格-客户返佣+客户服务费",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:we.value,"onUpdate:modelValue":l[50]||(l[50]=e=>we.value=e)},null,8,["modelValue"])])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"毛利","label-width":"170px"},{default:s((()=>[i("div",P,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"毛利=预估应收客户款-预估成本",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:ge.value,"onUpdate:modelValue":l[51]||(l[51]=e=>ge.value=e)},null,8,["modelValue"])])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"毛利率","label-width":"180px"},{default:s((()=>[i("div",A,[n(B,{class:"relative right-2.5 mr-2",effect:"dark",content:"毛利率=毛利/预估应收客户款*100%",placement:"top"},{default:s((()=>[n(T,{color:"#333333",class:"no-inherit"},{default:s((()=>[n(V)])),_:1})])),_:1}),n(a,{class:"w-full",modelValue:he.value,"onUpdate:modelValue":l[52]||(l[52]=e=>he.value=e)},{append:s((()=>l[128]||(l[128]=[r(" % ")]))),_:1},8,["modelValue"])])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"达人所属机构","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.mcn_name,"onUpdate:modelValue":l[53]||(l[53]=e=>oe.value.mcn_name=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),l[130]||(l[130]=i("div",{class:"form_title mt-2"},[i("h3",null,"发布时间")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"期望发布日期","label-width":"170px"},{default:s((()=>[n(de,{modelValue:oe.value.anticipation_time,"onUpdate:modelValue":l[54]||(l[54]=e=>oe.value.anticipation_time=e),type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"期望发布时间"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),l[131]||(l[131]=i("div",{class:"form_title mt-2"},[i("h3",null,"品牌信息")],-1)),i("div",null,[n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"报备品牌（务必精准对应品牌专业号）","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.report_brand,"onUpdate:modelValue":l[55]||(l[55]=e=>oe.value.report_brand=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"产品名称","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.product_name,"onUpdate:modelValue":l[56]||(l[56]=e=>oe.value.product_name=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(b,{gutter:24},{default:s((()=>[n(f,{span:12},{default:s((()=>[n(t,{label:"是否绑定SPU（提供准确名称/SPUID）","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.spu_bind,"onUpdate:modelValue":l[57]||(l[57]=e=>oe.value.spu_bind=e)},null,8,["modelValue"])])),_:1})])),_:1}),n(f,{span:12},{default:s((()=>[n(t,{label:"商品ID","label-width":"170px"},{default:s((()=>[n(a,{class:"w-full",modelValue:oe.value.good_id,"onUpdate:modelValue":l[58]||(l[58]=e=>oe.value.good_id=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])])),_:1},8,["model"]),i("div",K,[n(ne,null,{default:s((()=>l[132]||(l[132]=[r("取消")]))),_:1}),n(ne,{type:"primary",onClick:pe},{default:s((()=>l[133]||(l[133]=[r("确定")]))),_:1})])]))])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-53feda73"]]);export{T as default};
