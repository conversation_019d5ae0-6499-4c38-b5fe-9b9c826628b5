import{k as e}from"./index-C2bfFjZ1.js";const t=t=>e.get("/getPortrait",t),s=t=>e.get("/getKolIndustry",t),a=t=>e.get("/getStarIds",t),o=t=>e.get("/dandelion/getDandelionBrand",t),n=t=>e.get("/getKolContent",t),i=t=>e.post("/getSelfKolList",t),g=t=>e.get("/getCity",t),l=t=>e.post("/kolHistoryRecord",t),r=t=>e.get("/dandelion/getLabel",t),d=t=>e.get("/dandelion/getContent",t),p=t=>e.get("/dandelion/getCharacters",t),u=t=>e.post("/dandelion/getDandelionSelfList",t),c=t=>e.get("/dandelion/cooperationDetailNew",t),f=t=>e.post("/exportkol",t),m=t=>e.post("/uploadMcnAnnex",t),x=t=>e.post("/getCustomerOption",t),C=t=>e.get("/getQuestionnaire",t),K=t=>e.post("/getKolQuestionnaire",t),L=t=>e.post("/submitQuestionnaire",t),k=t=>e.post("/createInfluence",t),y=t=>e.post("/influenceList",t),D=t=>e.post("/influenceDel",t),I=t=>e.post("/jianlianImport",t),b=t=>e.get("/getKolJianLian",t),j=t=>e.get("/self_resource/get/detail",t);export{d as a,g as b,c,r as d,p as e,x as f,u as g,k as h,f as i,b as j,l as k,o as l,a as m,i as n,s as o,n as p,t as q,K as r,j as s,C as t,m as u,L as v,y as w,D as x,I as y};
