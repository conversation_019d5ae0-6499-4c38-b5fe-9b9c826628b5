import{_ as l}from"./_plugin-vue_export-helper-BXFjo1rG.js";import a from"./linkedUsers-C6XEN6tl.js";import s from"./contentRepresent-DZxTOCdO.js";import{o as e}from"./resource-B9SEwZSg.js";import{u as o,r as i,p as t,a as n,c,o as u,h as r,at as v,t as p,b as d,w as f,F as m,i as k,g as x,E as _}from"./index-C2bfFjZ1.js";import{C as b}from"./clipboard-CcltUhq6.js";/* empty css                 */const y={class:"relative"},g={class:"flex kol-info relative mx-6"},h={class:"user-avatar author-avatar pointer"},w=["src"],j={class:"flex ml-4 kol-info-right"},C={class:"kol-info-col1"},D={class:"kol-info-title"},I={class:"flex flex-col mt-2"},M={class:"mt-3 flex"},N={class:"kol-info-value"},R={href:"javascript:;",class:"copy ml-2",style:{"margin-top":"-1px"}},S={class:"mt-3 flex"},q={class:"kol-info-value"},E={class:"kol-info-value"},F={class:"mt-3 flex"},K={class:"kol-info-value"},U={class:"kol-type-block text-xs ml-1"},$={class:"kol-type-block text-xs ml-1"},z={class:"mt-3 flex"},A={key:0,class:"kol-info-value"},B={class:"kol-type-block orange text-xs ml-1"},G={key:1,class:"kol-info-value"},H={class:"kol-info-col2 ml-6"},J={class:"flex flex-col mt-2"},L={class:"flex mt-3"},O={key:0,class:"kol-info-value"},P={key:1,class:"kol-info-value"},Q={class:"flex mt-3"},T={class:"kol-info-value"},V={key:0},W={key:1},X={key:3},Y={class:"kol-info-col3"},Z={class:"flex flex-col mt-2"},ll={class:"mt-3 flex"},al={class:"kol-info-value price"},sl={class:"mt-3 flex"},el={class:"kol-info-value price"},ol={class:"mt-3 flex"},il={class:"kol-info-value price"},tl={class:"px-5 white-block"},nl=l({__name:"xtKolResourceDetail",setup(l){const nl=o().query.platform_uid,cl=i({}),ul=i(!1),rl=()=>{let l=new b("#copy_text");l.on("success",(a=>{_.success("复制成功"),l.destroy()})),l.on("error",(a=>{_.warning("该浏览器不支持自动复制"),l.destroy()}))};return t((()=>{e({platform_uid:nl}).then((l=>{cl.value=l.data}))})),(l,e)=>{var o,i,t,_,b,nl,vl,pl,dl,fl,ml;const kl=n("DocumentCopy"),xl=n("el-icon"),_l=n("el-tooltip"),bl=n("el-tab-pane"),yl=n("el-tabs");return u(),c("div",null,[r("div",y,[r("div",{class:"kol-info-bg",style:v({backgroundImage:`url(${cl.value.avatar_uri})`})},null,4),r("div",g,[r("div",h,[r("img",{alt:"",class:"user-avatar-image",src:cl.value.avatar_uri,style:{width:"48px",height:"48px"}},null,8,w),e[1]||(e[1]=r("div",{class:"user-avatar-content"},[r("span",{class:"icon-tooltip-label star-hint",style:{"--star-height":"53px","--star-width":"48px"}},[r("span",{class:"el-tooltip icon","aria-describedby":"el-tooltip-2252",tabindex:"-1"},[r("img",{src:"",class:"stars-icon"})])])],-1))]),r("div",j,[r("div",C,[r("div",D,p(cl.value.nick_name),1),r("div",I,[r("div",M,[e[2]||(e[2]=r("span",{class:"kol-info-label"},"星图ID",-1)),r("span",N,p(cl.value.platform_uid),1),r("a",R,[d(xl,{id:"copy_text","data-clipboard-text":null==(o=cl.value)?void 0:o.platform_uid,class:"copy_text el-icon el-icon-document-copy",onClick:rl},{default:f((()=>[d(kl)])),_:1},8,["data-clipboard-text"])])]),r("div",S,[e[3]||(e[3]=r("span",{class:"kol-info-label"},"性別",-1)),r("span",q,p(0!==cl.value.gender?1===cl.value.gender?"男":"女":"未知"),1),e[4]||(e[4]=r("span",{class:"kol-info-label"},"地区",-1)),r("span",E,p(cl.value.city),1)]),r("div",F,[e[5]||(e[5]=r("span",{class:"kol-info-label"},"内容类型",-1)),r("span",K,[(u(!0),c(m,null,k(null==(i=cl.value)?void 0:i.tags_relation,((l,a)=>(u(),x(_l,{class:"item",effect:"light",placement:"bottom-start"},{content:f((()=>[(u(!0),c(m,null,k(l,(l=>(u(),c("span",U,p(l),1)))),256))])),default:f((()=>[r("span",$,p(a),1)])),_:2},1024)))),256))])]),r("div",z,[e[7]||(e[7]=r("span",{class:"kol-info-label"},"行业类型",-1)),(null==(_=null==(t=cl.value)?void 0:t.industry_list)?void 0:_.length)>0?(u(),c("span",A,[(u(!0),c(m,null,k(null==(b=cl.value)?void 0:b.industry_list,((l,a)=>(u(),c("span",B,p(l),1)))),256))])):(u(),c("span",G,e[6]||(e[6]=[r("span",{class:"kol-type-block orange text-xs ml-1"},"暂无",-1)])))])])]),r("div",H,[e[10]||(e[10]=r("div",{class:"kol-info-title"},"MCN信息",-1)),r("div",J,[r("div",L,[e[8]||(e[8]=r("span",{class:"kol-info-label"},"MCN",-1)),(null==(nl=cl.value)?void 0:nl.mcn_name)?(u(),c("span",O,p(null==(vl=cl.value)?void 0:vl.mcn_name),1)):(u(),c("span",P,"无"))]),r("div",Q,[e[9]||(e[9]=r("span",{class:"kol-info-label"},"介绍",-1)),r("span",T,[ul.value?(u(),c("div",V,p(null==(pl=cl.value)?void 0:pl.mcn_introduction),1)):(u(),c("div",W,p(null==(fl=null==(dl=cl.value)?void 0:dl.mcn_introduction)?void 0:fl.slice(0,20)),1)),(null==(ml=cl.value)?void 0:ml.mcn_introduction)?(u(),c("span",{key:2,onClick:e[0]||(e[0]=l=>ul.value=!ul.value),class:"cursor"},p(ul.value?"【收起】":"【展开】"),1)):(u(),c("span",X,"无"))])])])]),r("div",Y,[e[14]||(e[14]=r("div",{class:"kol-info-title"},"服务价格",-1)),r("div",Z,[r("div",ll,[e[11]||(e[11]=r("span",{class:"kol-info-label w-26"},"1-20s视频",-1)),r("span",al,"¥ "+p(cl.value.price_1_20),1)]),r("div",sl,[e[12]||(e[12]=r("span",{class:"kol-info-label w-26"},"21-60S视频",-1)),r("span",el,"¥ "+p(cl.value.price_20_60),1)]),r("div",ol,[e[13]||(e[13]=r("span",{class:"kol-info-label w-26"},"60S以上视频",-1)),r("span",il,"¥ "+p(cl.value.price_60),1)])])])]),e[15]||(e[15]=r("div",null,null,-1))])]),r("div",tl,[d(yl,null,{default:f((()=>[d(bl,{label:"连接用户"},{default:f((()=>[d(a,{kolDetail:cl.value},null,8,["kolDetail"])])),_:1}),d(bl,{label:"内容表现"},{default:f((()=>[d(s)])),_:1})])),_:1})])])}}},[["__scopeId","data-v-b29e1d12"]]);export{nl as default};
