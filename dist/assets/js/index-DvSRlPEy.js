import{_ as e}from"./default-face-CI2vwNfZ.js";import{u as l,r as a,H as t,f as o,I as n,p as i,a as u,Q as s,c as r,o as p,b as c,m as d,n as m,w as _,g as v,y as f,a5 as y,d as g,h,an as k,t as b,F as x,i as w,E as V,q as j,K as C}from"./index-C2bfFjZ1.js";import S from"./xingtuTab-CPChA4NE.js";import{i as I,w as T,x as U,y as D}from"./index-rj4rWfRX.js";import{_ as z}from"./index.vue_vue_type_script_setup_true_lang-Ds10gtGP.js";import L from"./taskDetail-CxznhUhU.js";import"./tools-Cu1QpDc6.js";import"./kol-BVgENpvM.js";/* empty css                                                  */import"./_plugin-vue_export-helper-BXFjo1rG.js";import"./index-P0VwV2wd.js";import"./xtTaskDetailStatus1-CrxFI3li.js";import"./addProject-B2svxEaU.js";import"./xtTaskDetailStatus2-BYJ_JNI5.js";import"./xtTaskDetailStatus3-DeMqToxn.js";const R={class:"talentList-box card content-box"},A={key:1},E={class:"content-search import-box"},O={class:"content-search-input"},q={key:2},F={class:"content-search import-box"},H={class:"content-search-input"},M={class:"w-full pl-2 checked-list"},K={class:"search-div-photo flex items-center"},Q=["src"],Y={key:1,src:e,width:"45",height:"45",alt:""},G={class:"ml-4 font-semibold"},J={class:"pagina_box"},N={class:"dialog-footer"},P={class:"mediaList"},X={class:"dialog-footer"},B={class:"overflow-auto"},W={__name:"index",setup(e,{expose:W}){l();const Z=a("top"),$=a("");let ee=a(!1);const le=a({list:[],count:0});let ae=a(!1),te=a(!1),oe=a(0),ne=a(""),ie=a(),ue=a(),se=a("xingtu");const re=a(null);let pe=[],ce=a(new Map),de=a([]),me=a(1),_e=a([]);const ve=t(),fe=a([{value:"1-20s视频",label:"price_1_20",type:1,money:3e3},{value:"21-60S视频",label:"price_20_60",type:2,money:3e3},{value:"60S以上视频",label:"price_60",type:71,money:3e3}]),ye=(e,l)=>{me.value=1,$.value="",localStorage.setItem("keyword",""),De({platform:5,page:1,page_size:10})},ge=()=>{pe.length>0&&Ve()},he=()=>{ae.value=!0,me.value=1,de.value=[]},ke=()=>{localStorage.setItem("keyword",$.value),"xingtu"==se.value?ie.value&&ie.value.getTalentList():ue.value&&ue.value.getTalentList()},be=async e=>{me.value=e,De({platform:"xingtu"==se.value?5:106,page:e,page_size:10})},xe=e=>{te.value=!1,oe.value++},we=e=>{C.confirm("是否保存修改?").then((()=>{ne.value.status1Ref.onSubmit(1)})).catch((()=>{}))},Ve=()=>{let e=new FormData;pe.map((l=>{e.append("file",l.file)})),Ue(e)},je=()=>{ee.value=!0,_e.value=[],pe=[]},Ce=()=>{let e;"xingtu"==se.value?(e.kol_ids=ie.value.selectedIds,e.kol_type=1):(e.kol_ids=ue.value.selection,e.kol_type=3),I(e).then((e=>{V.success("导出成功，请稍后请到消息页面下载")}))},Se=async e=>{0!=e.length&&ce.value.set(me.value,e)},Ie=()=>{let e=Array.from(ce.value.values()).flat();if(e.length>0){e.map((e=>{e.reality_price=e.price,e.reality_rebate_ratio=e.rebate_ratio,e.editMoney=!1,e.editRebate=!1,e.kol_price=e.price,e.cooperation_type=e.cooperation_type,e.kol_num=e.kol_num,e.mcnObject={mcn_name:"",mcn_short_name:""},e.collection_status=1,e.source=1,e.last_reviews={1:{},2:{},3:{}}}));const l=JSON.stringify(e);localStorage.setItem("kolList",l),se.value,oe.value++,ve.push({path:"/business/task/informationAuthor",query:{path:"talentList"}})}else V.error("请先选择达人")},Te=e=>{let l=e.raw;-1==pe.indexOf(l)&&pe.push({file:l,type:1})};const Ue=e=>{try{D(e).then((e=>{ee.value=!1,990==e.code&&V.success("上传成功"),_e.value=[],pe=[]})).catch((e=>{}))}catch(l){}};W({importStar:je,exportkol:Ce});const De=e=>{T(e).then((l=>{le.value={list:l.data.list.map((e=>{let l=Object.assign({},e);return 1==l.cooperation_type?l.price=l.price_1_20*l.kol_num:2==l.cooperation_type?l.price=l.price_20_60*l.kol_num:71==l.cooperation_type&&(l.price=l.price_60*l.kol_num),l})),count:l.data.count},j((()=>{(e=>{const l=ce.value.get(e);l&&re.value&&(re.value.data||[]).forEach((e=>{l.some((l=>l.id===e.id))&&re.value.toggleRowSelection(e,!0)}))})(e.page,le.value)})),le.value.list.forEach((e=>{}))}))};return o((()=>ae.value),(()=>{ae.value?ye(se.value):ce.value=new Map}),{deep:!0}),n((()=>{localStorage.setItem("keyword","")})),i((async()=>{localStorage.removeItem("taskDetail"),localStorage.removeItem("kolList"),await De({platform:"5",page:1,page_size:10})})),(e,l)=>{var a;const t=u("el-tab-pane"),o=u("el-tabs"),n=u("el-button"),i=u("el-badge"),V=u("el-input"),j=u("el-icon"),C=u("el-table-column"),I=u("el-option"),T=u("el-select"),D=u("el-input-number"),W=u("el-popconfirm"),oe=u("el-table"),ue=u("el-dialog"),pe=u("el-form-item"),ce=u("UploadFilled"),de=u("el-upload"),ve=u("el-form"),Ue=u("Close"),ze=u("el-drawer"),Le=s("permission");return p(),r("div",R,[c(o,{modelValue:f(se),"onUpdate:modelValue":l[1]||(l[1]=e=>y(se)?se.value=e:se=e),style:{height:"100%"},onTabClick:ye,"tab-position":Z.value,class:"search-tabs"},{default:_((()=>[c(t,{label:"巨量星图",name:"xingtu"},{default:_((()=>["xingtu"==f(se)?(p(),v(S,{key:0,onGetInfluenceList:De,onAddTask:l[0]||(l[0]=e=>y(te)?te.value=!0:te=!0),onExportkol:Ce,onImportStar:je,ref_key:"xingtuRef",ref:ie},null,512)):m("",!0)])),_:1})])),_:1},8,["modelValue","tab-position"]),"xingtu"==f(se)?d((p(),v(i,{key:0,value:null==(a=le.value)?void 0:a.count,class:"checkedKol item"},{default:_((()=>[c(n,{onClick:he},{default:_((()=>l[11]||(l[11]=[g("已选达人 ")]))),_:1})])),_:1},8,["value"])),[[Le,"system:talentList:change"]]):m("",!0),"xingtu"==f(se)?(p(),r("div",A,[h("div",E,[h("div",O,[c(V,{modelValue:$.value,"onUpdate:modelValue":l[2]||(l[2]=e=>$.value=e),class:"search-input",placeholder:"请输入达人名称或星图ID",clearable:""},null,8,["modelValue"]),c(n,{onClick:ke,class:"search-button fz18"},{default:_((()=>[c(j,null,{default:_((()=>[c(f(k))])),_:1})])),_:1})])])])):(p(),r("div",q,[h("div",F,[h("div",H,[c(V,{modelValue:$.value,"onUpdate:modelValue":l[3]||(l[3]=e=>$.value=e),class:"search-input",placeholder:"请输入达人名称或蒲公英ID",clearable:""},null,8,["modelValue"]),c(n,{onClick:ke,class:"search-button fz18"},{default:_((()=>[c(j,null,{default:_((()=>[c(f(k))])),_:1})])),_:1})])])])),c(ue,{modelValue:f(ae),"onUpdate:modelValue":l[6]||(l[6]=e=>y(ae)?ae.value=e:ae=e),"destroy-on-close":"",title:"已选达人",width:"70%"},{footer:_((()=>[h("span",N,[c(n,{onClick:l[5]||(l[5]=e=>y(ae)?ae.value=!1:ae=!1)},{default:_((()=>l[13]||(l[13]=[g("取消")]))),_:1}),c(n,{type:"primary",onClick:Ie},{default:_((()=>l[14]||(l[14]=[g("立即下单")]))),_:1})])])),default:_((()=>{var e;return[h("div",M,[c(oe,{ref_key:"multipleTable",ref:re,data:null==(e=le.value)?void 0:e.list,border:"","header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"},onSelectionChange:Se},{default:_((()=>[c(C,{type:"selection",width:"55"}),c(C,{prop:"kol_name",label:"达人名称",width:"150"},{default:_((e=>[h("div",K,[e.row.kol_photo?(p(),r("img",{key:0,src:e.row.kol_photo,width:"45",height:"45",alt:""},null,8,Q)):(p(),r("img",Y)),h("span",G,b(e.row.kol_name),1)])])),_:1}),c(C,{prop:"cooperation_type",label:"服务类型"},{default:_((e=>[c(T,{modelValue:e.row.cooperation_type,"onUpdate:modelValue":l=>e.row.cooperation_type=l,onChange:l=>{var a;1==(a=e.row).cooperation_type?a.price=a.price_1_20*a.kol_num:2==a.cooperation_type?a.price=a.price_20_60*a.kol_num:71==a.cooperation_type&&(a.price=a.price_60*a.kol_num)}},{default:_((()=>[(p(!0),r(x,null,w(fe.value,(e=>(p(),v(I,{value:e.type,label:e.value},null,8,["value","label"])))),256))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(C,{prop:"kol_num",label:"数量"},{default:_((e=>[c(D,{modelValue:e.row.kol_num,"onUpdate:modelValue":l=>e.row.kol_num=l,onChange:l=>{var a;1==(a=e.row).cooperation_type?a.price=a.price_1_20*a.kol_num:2==a.cooperation_type?a.price=a.price_20_60*a.kol_num:71==a.cooperation_type&&(a.price=a.price_60*a.kol_num)},min:1},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),c(C,{prop:"price",label:"价格"},{default:_((e=>{var l;return[h("div",null,"¥"+b(null==(l=e.row)?void 0:l.price),1)]})),_:1}),c(C,null,{default:_((e=>[c(W,{title:"确定删除当前达人么?",onConfirm:l=>{var a,t;return t=null==(a=null==e?void 0:e.row)?void 0:a.id,void U({id:t}).then((e=>{De({platform:"xingtu"==se.value?5:106,page:1,page_size:10})}))},onCancel:l[4]||(l[4]=()=>{})},{reference:_((()=>[c(n,{size:"small"},{default:_((()=>l[12]||(l[12]=[g("删除")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"]),h("div",J,[c(z,{onHandleCurrentChange:be,total:le.value.count,"current-page":f(me),layout:"total, next,prev, pager, jumper"},null,8,["total","current-page"])])])]})),_:1},8,["modelValue"]),c(ue,{title:"导入文件",modelValue:f(ee),"onUpdate:modelValue":l[9]||(l[9]=e=>y(ee)?ee.value=e:ee=e),"append-to-body":!0,width:"40%"},{footer:_((()=>[h("span",X,[c(n,{onClick:l[8]||(l[8]=e=>y(ee)?ee.value=!1:ee=!1)},{default:_((()=>l[17]||(l[17]=[g("取 消")]))),_:1}),c(n,{type:"primary",onClick:ge},{default:_((()=>l[18]||(l[18]=[g("确 定")]))),_:1})])])),default:_((()=>[h("div",null,[c(ve,{"label-width":"80px"},{default:_((()=>[c(pe,{label:"模板下载"},{default:_((()=>[h("div",P,[c(n,{type:"primary",round:"",onClick:l[7]||(l[7]=e=>{return l="https://inly-ai-1.oss-cn-beijing.aliyuncs.com/template-jianlian.xlsx",void(window.location.href=l);var l})},{default:_((()=>l[15]||(l[15]=[g(" 下载 ")]))),_:1})])])),_:1}),c(pe,{label:"导入"},{default:_((()=>[c(de,{class:"upload-demo",ref:"uploadRef",limit:1,"auto-upload":!1,"http-request":Ve,"file-list":f(_e),"on-change":Te,accept:".xlsx,.xls",drag:"",action:"#",multiple:""},{default:_((()=>[c(j,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:_((()=>[c(ce)])),_:1}),l[16]||(l[16]=h("div",{class:"el-upload__text"},[g("将文件拖到此处，或"),h("em",null,"点击上传")],-1))])),_:1},8,["file-list"])])),_:1})])),_:1})])])),_:1},8,["modelValue"]),c(ze,{ref:"drawerRef",title:"任务新增",modelValue:f(te),"onUpdate:modelValue":l[10]||(l[10]=e=>y(te)?te.value=e:te=e),"before-close":we,class:"demo-drawer",size:"1000px"},{default:_((()=>[h("button",{class:"el-drawer__close-btn",onClick:xe,type:"button"},[c(j,{class:"el-drawer__close"},{default:_((()=>[c(Ue)])),_:1})]),h("div",B,[c(L,{ref_key:"taskDetailRef",ref:ne,platform:"xingtu"==f(se)?5:106,type:"add","is-show":f(te),onCloseDrawer:xe},null,8,["platform","is-show"])])])),_:1},8,["modelValue"])])}}};export{W as default};
