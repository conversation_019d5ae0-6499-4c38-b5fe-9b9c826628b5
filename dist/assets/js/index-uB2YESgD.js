import{g as e,s as a,a as t}from"./getDate-DnIXwqdH.js";import{_ as l}from"./_plugin-vue_export-helper-GSmkUi5K.js";import{r as n,a as r,c as o,o as u,b as p,w as i,d as c,E as s}from"./index-BE6Fh1xm.js";const d={class:"card content-box"},v=l({__name:"index",setup(l){const v=n(null),_=n({}),m=n([]),g=n([]);g.value=e();const h=({rowIndex:e,columnIndex:a})=>{if(6===a)return 0===e?{rowspan:m.value.length,colspan:1}:{rowspan:0,colspan:0}},b=n(!1),j=n({page:1,page_size:10,media_personnel:"",created_at:{start_time:g.value[0],end_time:g.value[1]}}),f=(e,a)=>{if(Object.keys(e).length>0){for(let a in j.value)"created_at"!=a&&(j.value[a]=e[a]);(null==e?void 0:e.project_time)&&e.project_time.length>0&&(j.value.created_at.start_time=e.project_time[0],j.value.created_at.end_time=e.project_time[1],_.value.project_time=e.project_time)}else j.value={page:1,page_size:10,media_personnel:"",created_at:{start_time:g.value[0],end_time:g.value[1]}};a(),z()},y=n({total:0,currentPage:1,pageSize:10});function x(e){j.value.page=e,z()}function D(e){j.value.page=1,j.value.page_size=e,z()}let Y=n([]);const w=n({headerAlign:"center",align:"center",gridBtn:!1,menu:!1,border:!0,index:!0,searchEnter:!0,searchSpan:8,rowKey:"id",addBtn:!1,searchMenuPosition:"right",dialogDrag:!0,rowParentKey:"parentId",column:[{label:"媒介人员",prop:"media_personnel",search:!0},{label:"操作人员",prop:"upload_name",align:"center"},{label:"平台",prop:"platform_type",type:"select",dicData:[{label:"抖音",value:1},{label:"小红书",value:2},{label:"B站",value:3},{label:"快手",value:4},{label:"视频号",value:5},{label:"公众号",value:6}],align:"center"},{label:"达人建联新增数量",prop:"jianlian_add_sum"},{label:"达人建联更新数量",prop:"jianlian_update_sum"},{label:"上传时间",type:"daterange",prop:"project_time",align:"center",search:!0,searchRange:!0,format:"YYYY-MM-DD",span:24,valueFormat:"YYYY-MM-DD",startPlaceholder:g.value[0],endPlaceholder:g.value[1]}]}),z=()=>{b.value=!0,a(j.value).then((e=>{990===e.code&&(e.data.list.forEach((e=>{_.value.project_time&&_.value.project_time.length>0?e.project_time=_.value.project_time:e.project_time=g.value})),Y.value=e.data.list,m.value=e.data.list,y.value.total=e.data.count),b.value=!1}))},C=()=>{t(j.value).then((e=>{990===e.code&&s({type:"success",message:"导出成功，请到下载记录中下载文件"})}))};return z(),(e,a)=>{const t=r("el-button"),l=r("avue-crud");return u(),o("div",d,[p(l,{onSearchChange:f,modelValue:_.value,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value=e),"table-loading":b.value,option:w.value,data:m.value,ref_key:"crud",ref:v,page:y.value,"onUpdate:page":a[1]||(a[1]=e=>y.value=e),onCurrentChange:x,onSizeChange:D,"span-method":h},{"menu-left":i((()=>[p(t,{type:"primary",icon:"Download",onClick:C},{default:i((()=>a[2]||(a[2]=[c("导出")]))),_:1})])),_:1},8,["modelValue","table-loading","option","data","page"])])}}},[["__scopeId","data-v-b66d5acb"]]);export{v as default};
