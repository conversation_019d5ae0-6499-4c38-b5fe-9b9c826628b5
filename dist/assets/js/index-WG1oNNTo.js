import{s as e,r as a,a as s,c as l,o,h as t,b as n}from"./index-C2bfFjZ1.js";import{_ as u}from"./_plugin-vue_export-helper-BXFjo1rG.js";const r={class:"card content-box"},p=e({name:"menu23"}),d=u(e({...p,setup(e){const u=a("");return(e,a)=>{const p=s("el-input");return o(),l("div",r,[a[1]||(a[1]=t("span",{class:"text"},"我是menu2-3 🍓🍇🍈🍉",-1)),n(p,{modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=e=>u.value=e),placeholder:"测试缓存"},null,8,["modelValue"])])}}}),[["__scopeId","data-v-41b080e1"]]);export{d as default};
