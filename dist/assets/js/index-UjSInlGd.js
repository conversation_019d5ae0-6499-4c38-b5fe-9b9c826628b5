import{s as e,r as a,a6 as l,x as n,a as r,Q as t,c as d,o as u,m as o,h as i,b as s,v as c,w as m,y as p,d as v,g as _,n as h,F as b,i as f,t as g,E as y,q as V,K as x}from"./index-BE6Fh1xm.js";import{m as k,g as w,b as C,o as z,p as P,q as S}from"./index-DPqYVz5G.js";import{_ as U}from"./_plugin-vue_export-helper-GSmkUi5K.js";const q={class:"app-container"},j={style:{position:"relative"}},B={key:0},E={class:"dialog-footer"},D=e({name:"Role"}),Q=U(Object.assign(D,{setup(e){const U=a([]),D=a(!1),Q=a(!0),Y=a(!0);a(0);const H=a(""),I=a([]);a([]),a("first");const N=l({total:0,pagerCount:7,currentPage:1,pageSize:10}),O=l({menu:!1,menuType:null,search:!1,toolBar:!1,gridBtn:!1,addBtn:!1,searchEnter:!0,page:!0,align:"center",menuAlign:"center",border:!0,index:!0,selection:!1,viewBtn:!1,editBtn:!1,delBtn:!1,pageSize:10,pageSizes:[10,20,30,50],column:[{label:"年框名称",prop:"year_frame_name",width:300,overHidden:!0},{label:"分支名称",prop:"branches",slot:!0},{label:"品牌名称",prop:"brand_name",overHidden:!0},{label:"创建者",prop:"created_name",width:150,overHidden:!0},{label:"操作",prop:"menu",slot:!0,width:200}]}),R=a({value:"value",label:"label",children:"children",checkStrictly:!1,emitPath:!1,multiple:!0});function F(e){K.value.branch_ids=((e,a)=>{const l=[],n=e=>{!e.children&&a.includes(e.value)&&l.push(e.value),e.children&&e.children.forEach(n)};return e.forEach(n),l})(X.value,e)}const M=l({form:{year_frame_name:"",brand_name:"",sort:1},queryParams:{page:1,page_size:10,brand_name:"",branch_ids:[],created_at:{start_time:"",end_time:""},name:void 0},deptParams:{page:1,page_size:1e3,name:void 0},levelQueryParams:{pageNum:1,pageSize:10},brandQueryParams:{page:1,page_size:10,brand_name:void 0,brandChildName:void 0},rules:{year_frame_name:[{required:!0,message:"年框名称不能为空",trigger:"blur"}],branch_ids:[{required:!0,type:"array",message:"请选择分支",trigger:"change"}],brand_id:[{required:!0,message:"请选择品牌",trigger:"change"}]}}),{queryParams:A,form:K,rules:T,deptParams:G,brandQueryParams:J}=n(M),L=a([]);function W(e,a){var l;Q.value=!0,A.value.page=e||N.currentPage,A.value.page_size=a||N.pageSize,A.value.created_at.start_time=I.value?I.value[0]:"",A.value.created_at.end_time=I.value?I.value[1]:"";const n={...A.value,branch_ids:(null==(l=A.value.branch_ids)?void 0:l.length)>0?[A.value.branch_ids[A.value.branch_ids.length-1]]:void 0};C(n).then((e=>{990==e.code&&(L.value=e.data.list,N.total=e.data.count,Q.value=!1)}))}const X=a([]);function Z(){k(G.value).then((e=>{990===e.code?X.value=function(e){const a={};return e.forEach((e=>{const{company_id:l,company_name:n,dep_id:r,dep_name:t,branch_name:d,id:u}=e;a[l]||(a[l]={value:l,label:n,children:{}}),a[l].children[r]||(a[l].children[r]={value:r,label:t,children:[]}),a[l].children[r].children.push({value:u,label:d,branch:!0})})),Object.values(a).map((e=>{const a=Object.values(e.children);return{value:e.value,label:e.label,children:a}}))}(e.data.list):y.error(e.msg)}))}Z();const $=a([]),ee=a(!1),ae=async e=>{if(e){J.value.brand_name=e,ee.value=!0;try{const e=await w(J.value);$.value=e.data.list}catch(a){}finally{ee.value=!1}}else $.value=[]};function le(e){const a=$.value.find((a=>a.id===e));K.value.brand_name=a?a.brand_name:""}function ne(){A.value.pageNum=1,W()}(async()=>{try{const e=await w(J.value);$.value=e.data.list}catch(e){}finally{ee.value=!1}})();const re=a(null);function te(){var e;I.value=[],A.value={page:1,page_size:10,brand_name:"",branch_ids:[],created_at:{start_time:"",end_time:""},name:void 0},(e=re.value).value&&e.value.resetFields(),ne()}function de(){K.value={year_frame_name:"",branch_ids:[],brand_name:"",brand_id:null,sort:1}}function ue(){ie.value=!0,de(),Z(),oe.value=[],D.value=!0,H.value="新增年框"}const oe=a([]);const ie=a(!0),se=a(null);function ce(){se.value.validate((e=>{e&&(ie.value?P(K.value).then((e=>{990===e.code&&(y.success(e.msg),D.value=!1,W())})):S(K.value).then((e=>{990===e.code&&(y.success(e.msg),D.value=!1,W())})))}))}function me(){D.value=!1,de()}function pe(e){N.pageSize=e,W(1,e)}function ve(e){N.currentPage=e,W(e,N.pageSize)}return W(),(e,a)=>{const l=r("el-input"),n=r("el-form-item"),k=r("el-cascader"),w=r("el-date-picker"),C=r("el-button"),P=r("el-form"),S=r("el-tooltip"),M=r("avue-crud"),G=r("el-option"),J=r("el-select"),ee=r("el-dialog"),_e=t("loading");return u(),d("div",q,[o(s(P,{model:p(A),ref_key:"queryRef",ref:re,inline:!0,"label-width":"88px"},{default:m((()=>[s(n,{label:"年框名称",prop:"name"},{default:m((()=>[s(l,{modelValue:p(A).name,"onUpdate:modelValue":a[0]||(a[0]=e=>p(A).name=e),placeholder:"请输入年框名称",clearable:"",maxlength:"50",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),s(n,{label:"分支",prop:"branch_ids"},{default:m((()=>[s(k,{modelValue:p(A).branch_ids,"onUpdate:modelValue":a[1]||(a[1]=e=>p(A).branch_ids=e),options:X.value,placeholder:"请选择分支",style:{width:"200px"}},null,8,["modelValue","options"])])),_:1}),s(n,{label:"品牌名称",prop:"brand_name"},{default:m((()=>[s(l,{modelValue:p(A).brand_name,"onUpdate:modelValue":a[2]||(a[2]=e=>p(A).brand_name=e),placeholder:"请输入品牌名称",clearable:"",maxlength:"50",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),s(n,{label:"创建时间",style:{width:"308px"}},{default:m((()=>[s(w,{modelValue:I.value,"onUpdate:modelValue":a[3]||(a[3]=e=>I.value=e),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),s(n,null,{default:m((()=>[s(C,{type:"primary",icon:"Search",onClick:ne},{default:m((()=>a[9]||(a[9]=[v("搜索")]))),_:1}),s(C,{icon:"Delete",onClick:te},{default:m((()=>a[10]||(a[10]=[v("清空")]))),_:1})])),_:1})])),_:1},8,["model"]),[[c,Y.value]]),i("div",j,[s(C,{type:"primary",icon:"Plus",onClick:ue,style:{position:"absolute",top:"20px",left:"20px","z-index":"2"}},{default:m((()=>a[11]||(a[11]=[v("添加年框")]))),_:1}),o((u(),_(M,{modelValue:U.value,"onUpdate:modelValue":a[4]||(a[4]=e=>U.value=e),data:L.value,option:O,page:N,onSizeChange:pe,onCurrentChange:ve},{branches:m((({row:e})=>[(u(!0),d(b,null,f(e.branches,((a,l)=>(u(),d("span",{key:l},[i("span",null,g(a.branch_name),1),l<e.branches.length-1?(u(),d("span",B,"/")):h("",!0)])))),128))])),menu:m((({row:e})=>[1!==e.roleId?(u(),_(S,{key:0,content:"编辑",placement:"top"},{default:m((()=>[s(C,{link:"",type:"primary",icon:"Edit",onClick:a=>function(e){ie.value=!1,de(),Z(),ae(e.brand_id),K.value.created_name=e.created_name,K.value.year_frame_name=e.year_frame_name,K.value.sort=e.sort,K.value.brand_name=e.brand_name,K.value.brand_id=e.brand_id,K.value.year_frame_id=e.id,D.value=!0,V((()=>{var a;oe.value=(a=X.value,e.branches.map((e=>function(e,a){const l=[],n=(e,r)=>{for(const t of e){const e=[...r,t.value];if(t.value===a)return l.push(...e),!0;if(t.children&&n(t.children,e))return!0}return!1};return n(e,[]),l}(a,e.branch_id)))),F(e.branches.map((e=>e.branch_id)))})),H.value="修改年框"}(e)},null,8,["onClick"])])),_:2},1024)):h("",!0),1!==e.roleId?(u(),_(S,{key:1,content:"删除",placement:"top"},{default:m((()=>[s(C,{link:"",type:"primary",icon:"Delete",onClick:a=>function(e){x.confirm('是否确认删除"'+e.year_frame_name+'"?').then((function(){return z({year_frame_id:e.id})})).then((e=>{990===e.code&&(W(),y.success("删除成功"))})).catch((()=>{}))}(e)},null,8,["onClick"])])),_:2},1024)):h("",!0)])),_:1},8,["modelValue","data","option","page"])),[[_e,Q.value]])]),s(ee,{title:H.value,modelValue:D.value,"onUpdate:modelValue":a[8]||(a[8]=e=>D.value=e),width:"500px","append-to-body":""},{footer:m((()=>[i("div",E,[s(C,{type:"primary",onClick:ce},{default:m((()=>a[12]||(a[12]=[v("确 定")]))),_:1}),s(C,{onClick:me},{default:m((()=>a[13]||(a[13]=[v("取 消")]))),_:1})])])),default:m((()=>[s(P,{ref_key:"roleRef",ref:se,model:p(K),rules:p(T),"label-width":"100px"},{default:m((()=>[s(n,{label:"年框名称",prop:"year_frame_name",style:{width:"300px"}},{default:m((()=>[s(l,{modelValue:p(K).year_frame_name,"onUpdate:modelValue":a[5]||(a[5]=e=>p(K).year_frame_name=e),maxlength:"50",placeholder:"请输入年框名称"},null,8,["modelValue"])])),_:1}),s(n,{label:"选择分支",prop:"branch_ids"},{default:m((()=>[s(k,{modelValue:oe.value,"onUpdate:modelValue":a[6]||(a[6]=e=>oe.value=e),options:X.value,props:R.value,onChange:F,clearable:"",multiple:"","collapse-tags":"",placeholder:"请选择分支",style:{width:"300px"}},null,8,["modelValue","options","props"])])),_:1}),s(n,{label:"选择品牌",prop:"brand_id"},{default:m((()=>[s(J,{modelValue:p(K).brand_id,"onUpdate:modelValue":a[7]||(a[7]=e=>p(K).brand_id=e),filterable:"",clearable:"",placeholder:"请搜索品牌","remote-method":ae,loading:Q.value,onChange:le,style:{width:"300px"}},{default:m((()=>[(u(!0),d(b,null,f($.value,(e=>(u(),_(G,{key:e.id,label:e.brand_name,value:e.id,"data-id":e.id},null,8,["label","value","data-id"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}}}),[["__scopeId","data-v-c4412822"]]);export{Q as default};
