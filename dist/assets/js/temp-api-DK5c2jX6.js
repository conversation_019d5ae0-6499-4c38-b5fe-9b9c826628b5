import{j as e,k as t}from"./index-BE6Fh1xm.js";const s=e.create({baseURL:"http://gitlab.yinlimedia.com:8970/admin",timeout:6e4,withCredentials:!0,responseType:"blob"});s.interceptors.request.use((e=>{const t=localStorage.getItem("Authorization");return t&&(e.headers.Authorization=t),e}),(e=>Promise.reject(e))),s.interceptors.response.use((e=>e),(e=>Promise.reject(e)));const o=e=>t.post("/mcnList",e),a=e=>t.post("/mcnDel",e),n=e=>t.post("/mcnAdd",e),c=e=>t.post("/mcnEdit",e),i=e=>t.get("/mcnDetail",e),p=e=>t.get("/mcnStar",e),r=e=>t.post("/importMcnMedia",e),m=e=>t.post("/importPublicationMedia",e),d=e=>t.post("/uploadMcnAnnex",e),l=e=>t.post("/mcnInfoDel",e),h=e=>s.post("/mcn_export",e,{headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}),u=e=>s.post("/mcn_change_log_export",e,{headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}});export{h as a,n as b,o as c,a as d,u as e,r as f,p as g,d as h,m as i,l as j,i as m,c as u};
