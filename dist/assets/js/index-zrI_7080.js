import{B as e,e as t}from"./user-fUPUQseu.js";import{s as a,r as l,a as s,c as i,o as p,h as o,b as r,w as n,d as m,y as c,_ as d}from"./index-C2bfFjZ1.js";import{I as f}from"./index-DzBJk0gh.js";import{_ as u}from"./_plugin-vue_export-helper-BXFjo1rG.js";const _={class:"card content-box"},b=a({name:"batchImport"}),x=u(a({...b,setup(a){const u=l(),b=()=>{let a={title:"数据",tempApi:t,importApi:e};u.value.acceptParams(a)};return(e,t)=>{const a=s("el-button"),l=s("el-descriptions-item"),x=s("el-descriptions");return p(),i("div",_,[t[7]||(t[7]=o("span",{class:"text"},"批量添加数据 🍓🍇🍈🍉",-1)),r(a,{type:"primary",icon:c(d),onClick:b},{default:n((()=>t[0]||(t[0]=[m(" 批量添加数据 ")]))),_:1},8,["icon"]),r(f,{ref_key:"importRef",ref:u},null,512),r(x,{title:"配置项（通过 ref 传递） 📚",column:1,border:""},{default:n((()=>[r(l,{label:"title"},{default:n((()=>t[1]||(t[1]=[m(" 组件显示标题 && 上传成功之后提示信息 ")]))),_:1}),r(l,{label:"fileSize"},{default:n((()=>t[2]||(t[2]=[m(" 上传文件大小，默认为 5M ")]))),_:1}),r(l,{label:"fileType"},{default:n((()=>t[3]||(t[3]=[m(' 上传文件类型限制，默认类型为 ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"] ')]))),_:1}),r(l,{label:"tempApi"},{default:n((()=>t[4]||(t[4]=[m(" 下载模板的 Api ")]))),_:1}),r(l,{label:"importApi"},{default:n((()=>t[5]||(t[5]=[m(" 上传数据的 Api ")]))),_:1}),r(l,{label:"getTableList"},{default:n((()=>t[6]||(t[6]=[m(" 上传数据成功之后，刷新表格数据的 Api ")]))),_:1})])),_:1})])}}}),[["__scopeId","data-v-16882309"]]);export{x as default};
