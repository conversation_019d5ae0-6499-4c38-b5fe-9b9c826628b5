import{o as e}from"./kolOrder-E7NJTBcc.js";import a from"./settlementInfo-DZ0CCS-V.js";import{f as l,x as t,y as s,g as i}from"./order-AmV_cyoN.js";import{_ as d}from"./_plugin-vue_export-helper-BXFjo1rG.js";import{r,f as u,a as n,c as o,o as c,h as v,b as m,C as _,n as p,w as y,F as f,i as g,g as b,d as j,E as x}from"./index-C2bfFjZ1.js";import"./lodash-Y6-DaIz7.js";import"./default-face-CI2vwNfZ.js";const h={class:"project-info"},V={id:"custom",class:"module-primary"},w={class:"module-secondary-group"},k={class:"project-filter-list"},U={class:"project-filter-item"},C={class:"project-filter-item-content"},P={class:"project-filter-item"},q={class:"project-filter-item-content"},z={class:"project-filter-item"},I={class:"project-filter-item-content"},L={class:"project-filter-item"},S={class:"project-filter-item-content flex"},E={key:0,class:"project-filter-item"},O={class:"project-filter-item-content flex"},R={class:"project-filter-item"},T={class:"project-filter-item-content flex"},D={class:"project-filter-item"},F={class:"project-filter-item-content flex"},M={class:"project-filter-item"},A={class:"project-filter-item-content",style:{display:"flex","align-items":"center"}},B={style:{"margin-left":"10px"}},G={id:"task",class:"module-primary",style:{"margin-top":"20px"}},H={class:"module-secondary-group"},J={class:"module-secondary"},K={class:"module-secondary-group"},N={class:"project-selector"},Q={class:"flex"},W={class:"card-option-command"},X={class:"mt-2 flex items-center"},Y={class:"card-option-command"},Z={class:"mt-2 flex items-center"},$=d({__name:"projectInfo",props:["task_info","result_brand_list","result_star_list"],setup(d){const $=e();r(1),r(1);const ee=r(),ae=r(),le=r(!1);r("");const te=r([]),se=r([]),ie=r(),de=r([]),re=r([]),ue=r([]);ee.value=$.kolOrder;const ne=((e,a)=>{let l;return function(){const t=this,s=arguments;clearTimeout(l),l=setTimeout((()=>{e.apply(t,s)}),a)}})((async e=>{e?(le.value=!0,l({star_name:e}).then((e=>{le.value=!1,re.value=e.data.result_brand_list,ue.value=e.data.result_star_list,me("brand_name","result_brand_list")}))):ue.value=[]}),500);u((()=>ee.value),(()=>{if(0==re.value.length&&0==ue.value.length){const e=ee.value.star_id||ee.value.star_name;ne(e)}}),{deep:!0}),u((()=>ee.value.star_name),(()=>{ee.value.star_name&&pe()}));const oe=e=>{e?(le.value=!0,l({star_name:e.toString()}).then((e=>{le.value=!1,re.value=e.data.result_brand_list,ue.value=e.data.result_star_list,me("brand_name","result_brand_list")}))):ue.value=[]},ce=()=>{re.value=[],ue.value=[],ee.value.custom_name="",ee.value.star_name="",ee.value.star_id="",ee.value.xingtu_brand="",ee.value.primary_industry="",ee.value.secondary_industry="",ee.value.media_affiliate_company="",ee.value.product_category="",ee.value.yuntu_industry_ids=[]},ve=()=>{ie.value&&ee.value.yuntu_main_brand&&ie.value.map((e=>{e.yuntu_brand_id==ee.value.yuntu_main_brand&&(se.value=e.yuntu_industry)}))},me=(e,a)=>{"result_brand_list"==a?re.value.map((a=>{"brand_name"==e&&ee.value.xingtu_brand==a[e]&&(ee.value.primary_industry=a.primary_industry,ee.value.secondary_industry=a.secondary_industry,te.value=a.category_json,ee.value.media_affiliate_company=a.agent_account_full_name)})):(ee.value.yuntu_main_brand="",ee.value.yuntu_industry_ids=[],ee.value.starProjectList="",ue.value.map((a=>{"star_name"==e&&ee.value[e]==a[e]&&(ee.value.star_id=a.star_id,ee.value.custom_name=a.customer_name,l({star_name:a.star_id}).then((e=>{if(re.value=e.data.result_brand_list,e.data.result_brand_list&&e.data.result_brand_list.length>0){let a=e.data.result_brand_list[0];ee.value.xingtu_brand=a.brand_name,ee.value.primary_industry=a.primary_industry,ee.value.secondary_industry=a.secondary_industry,te.value=a.category_json,ee.value.media_affiliate_company=a.agent_account_full_name}pe()})))})))},_e=async()=>{if(ee.value.star_name){const e=x({message:"项目信息更新中...",type:"info",duration:0});await t({advertiser_id:ee.value.star_id,type:1}).then((a=>{990==a.code?(e.close(),pe(),x({message:"项目信息更新成功",type:"success"})):(e.close(),x({message:"项目信息更新失败",type:"error"}))}))}else x({message:"请先选择星图账号",type:"warning"})},pe=async()=>{await s({star_id:ee.value.star_id,page:1,page_size:999}).then((e=>{ie.value=e.data.list,ie.value&&ee.value.yuntu_main_brand&&ie.value.map((e=>{e.yuntu_brand_id==ee.value.yuntu_main_brand&&(ee.value.yuntu_main_brand=e.yuntu_brand_id,se.value=e.yuntu_industry)}))})),await i({star_id:ee.value.star_id,page:1,page_size:999}).then((e=>{de.value=e.data.list,ee.value.starProjectList="0"==ee.value.xingtu_project_id.toString()?"":ee.value.xingtu_project_id.toString()}))};return ae.value=$.isCommit,u((()=>$.isCommit),(()=>{ae.value=$.isCommit}),{deep:!0}),u((()=>ee.value.yuntu_main_brand),(()=>{ee.value.yuntu_main_brand&&ve()})),(e,l)=>{var t;const s=n("el-option"),i=n("el-select"),r=n("el-input"),u=n("RefreshRight"),x=n("el-icon"),$=n("el-tooltip");return c(),o("div",h,[v("div",V,[l[28]||(l[28]=v("div",{class:"module-primary-legend"},"客户信息",-1)),v("div",w,[v("div",k,[v("div",U,[l[16]||(l[16]=v("div",{class:"project-filter-item-title is-required"},"星图账号",-1)),v("div",C,[m(i,{style:{width:"350px"},modelValue:ee.value.star_name,"onUpdate:modelValue":l[0]||(l[0]=e=>ee.value.star_name=e),filterable:"",remote:"","reserve-keyword":"","remote-method":oe,loading:le.value,onChange:l[1]||(l[1]=e=>me("star_name","result_star_list")),clearable:"",onClear:ce,placeholder:"请输入星图账号昵称或ID"},{default:y((()=>[(c(!0),o(f,null,g(ue.value,(e=>(c(),b(s,{key:e.value,label:e.star_name+"("+e.star_id+")",value:e.star_name},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])]),v("div",P,[l[17]||(l[17]=v("div",{class:"project-filter-item-title"},"客户名称",-1)),v("div",q,[m(r,{disabled:"",style:{width:"350px"},modelValue:ee.value.custom_name,"onUpdate:modelValue":l[2]||(l[2]=e=>ee.value.custom_name=e),placeholder:"请选择星图账号昵称"},null,8,["modelValue"])])]),l[27]||(l[27]=v("div",null,null,-1)),v("div",z,[l[18]||(l[18]=v("div",{class:"project-filter-item-title is-required"},"星图品牌",-1)),v("div",I,[m(i,{style:{width:"200px"},modelValue:ee.value.xingtu_brand,"onUpdate:modelValue":l[3]||(l[3]=e=>ee.value.xingtu_brand=e),onChange:l[4]||(l[4]=e=>me("brand_name","result_brand_list")),placeholder:"请选择星图品牌"},{default:y((()=>[(c(!0),o(f,null,g(re.value,(e=>(c(),b(s,{key:e.brand_id,label:e.brand_name,value:e.brand_name},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])]),v("div",L,[l[21]||(l[21]=v("div",{class:"project-filter-item-title is-required"},"行业",-1)),v("div",S,[m(r,{modelValue:ee.value.primary_industry,"onUpdate:modelValue":l[5]||(l[5]=e=>ee.value.primary_industry=e),style:{width:"220px"}},{prepend:y((()=>l[19]||(l[19]=[j("一级行业")]))),_:1},8,["modelValue"]),m(r,{modelValue:ee.value.secondary_industry,"onUpdate:modelValue":l[6]||(l[6]=e=>ee.value.secondary_industry=e),style:{width:"240px","margin-left":"10px"}},{prepend:y((()=>l[20]||(l[20]=[j("二级行业")]))),_:1},8,["modelValue"])])]),(null==(t=te.value)?void 0:t.length)>0?(c(),o("div",E,[l[22]||(l[22]=v("div",{class:"project-filter-item-title"},"产品类目",-1)),v("div",O,[m(i,{style:{width:"200px"},modelValue:ee.value.product_category,"onUpdate:modelValue":l[7]||(l[7]=e=>ee.value.product_category=e),filterable:"",placeholder:"请选择产品类目"},{default:y((()=>[(c(!0),o(f,null,g(te.value,(e=>(c(),b(s,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])])):p("",!0),v("div",R,[l[23]||(l[23]=v("div",{class:"project-filter-item-title"},"云图品牌",-1)),v("div",T,[m(i,{style:{width:"200px"},modelValue:ee.value.yuntu_main_brand,"onUpdate:modelValue":l[8]||(l[8]=e=>ee.value.yuntu_main_brand=e),"value-key":"yuntu_brand_id",filterable:"",placeholder:"请选择云图品牌",onChange:l[9]||(l[9]=e=>ve())},{default:y((()=>[(c(!0),o(f,null,g(ie.value,(e=>(c(),b(s,{key:e.id,label:e.brand_name,value:e.yuntu_brand_id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])]),v("div",D,[l[24]||(l[24]=v("div",{class:"project-filter-item-title"},"云图行业",-1)),v("div",F,[m(i,{style:{width:"200px"},modelValue:ee.value.yuntu_industry_ids,"onUpdate:modelValue":l[10]||(l[10]=e=>ee.value.yuntu_industry_ids=e),multiple:"","multiple-limit":1,filterable:"",placeholder:"请选择云图行业"},{default:y((()=>[(c(!0),o(f,null,g(se.value,(e=>(c(),b(s,{key:e,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])]),v("div",M,[l[26]||(l[26]=v("div",{class:"project-filter-item-title is-required"},"星图项目",-1)),v("div",A,[m(i,{style:{width:"300px"},modelValue:ee.value.starProjectList,"onUpdate:modelValue":l[11]||(l[11]=e=>ee.value.starProjectList=e),filterable:"",placeholder:"请选择星图项目"},{default:y((()=>[(c(!0),o(f,null,g(de.value,(e=>(c(),b(s,{key:e.id,label:e.name+"("+e.xingtu_project_id+")",value:e.xingtu_project_id.toString()},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),v("div",B,[v("span",null,[m($,{class:"box-item",effect:"dark",content:"获取最新项目信息（星图创建项目未同步至星推时使用）",placement:"top"},{default:y((()=>[v("div",{style:{display:"flex",color:"var(--el-color-primary)","align-items":"center"},onClick:_e},[l[25]||(l[25]=v("span",{style:{"font-size":"14px",cursor:"pointer"}},"同步",-1)),m(x,{style:{"font-size":"16px","font-weight":"bold",cursor:"pointer"}},{default:y((()=>[m(u)])),_:1})])])),_:1})])])])])])])]),m(a,{task_info:d.task_info,star_id:ee.value.star_id},null,8,["task_info","star_id"]),v("div",G,[l[39]||(l[39]=v("div",{class:"module-primary-legend"},"任务信息",-1)),v("div",H,[l[38]||(l[38]=_('<div class="module-secondary" data-v-232e943a><div class="module-secondary-legend" data-v-232e943a>任务类型</div><div class="module-secondary-group" data-v-232e943a><div class="project-selector" data-v-232e943a><div class="card-select2" data-v-232e943a><div class="card-title" data-v-232e943a>指派</div><div class="card-desc" data-v-232e943a>有明确达人或想指定达人接单</div></div></div></div></div>',1)),v("div",J,[l[37]||(l[37]=v("div",{class:"module-secondary-legend"},"考核要求",-1)),v("div",K,[v("div",N,[v("div",Q,[v("div",W,[l[32]||(l[32]=v("div",null,[v("div",{class:"card-title"},"CPM"),v("div",{class:"card-desc"},"千次播放成本")],-1)),v("div",X,[l[29]||(l[29]=v("span",{class:"mr-2"},"最小值:",-1)),m(r,{modelValue:ee.value.cpm_min_price,"onUpdate:modelValue":l[12]||(l[12]=e=>ee.value.cpm_min_price=e),style:{width:"50px"},"controls-position":"right"},null,8,["modelValue"]),l[30]||(l[30]=v("span",{class:"ml-2 mr-2"}," - ",-1)),l[31]||(l[31]=v("span",{class:"mr-2"},"最大值:",-1)),m(r,{modelValue:ee.value.cpm_max_price,"onUpdate:modelValue":l[13]||(l[13]=e=>ee.value.cpm_max_price=e),style:{width:"50px"},"controls-position":"right"},null,8,["modelValue"])])]),v("div",Y,[l[36]||(l[36]=v("div",null,[v("div",{class:"card-title"},"CPE"),v("div",{class:"card-desc"},"单次互动成本")],-1)),v("div",Z,[l[33]||(l[33]=v("span",{class:"mr-2"},"最小值:",-1)),m(r,{modelValue:ee.value.cpe_min_price,"onUpdate:modelValue":l[14]||(l[14]=e=>ee.value.cpe_min_price=e),style:{width:"50px"},"controls-position":"right"},null,8,["modelValue"]),l[34]||(l[34]=v("span",{class:"ml-2 mr-2"}," - ",-1)),l[35]||(l[35]=v("span",{class:"mr-2"},"最大值:",-1)),m(r,{modelValue:ee.value.cpe_max_price,"onUpdate:modelValue":l[15]||(l[15]=e=>ee.value.cpe_max_price=e),style:{width:"50px"},"controls-position":"right"},null,8,["modelValue"])])])])])])])])]),l[40]||(l[40]=_('<div id="settlement" class="module-primary" style="margin-top:20px;" data-v-232e943a><div class="module-primary-legend" data-v-232e943a>结算方式</div><div class="module-secondary-group" style="margin-top:20px;" data-v-232e943a><div class="project-selector" data-v-232e943a><div class="card-select2" data-v-232e943a><div class="card-title" data-v-232e943a>按一口价结算</div><div class="card-desc" data-v-232e943a>以固体价格与达人合作</div></div></div></div></div>',1))])}}},[["__scopeId","data-v-232e943a"]]);export{$ as default};
