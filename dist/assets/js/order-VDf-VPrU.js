import{k as t}from"./index-BE6Fh1xm.js";const s=s=>t.get("/xingtu/projects/get",s),n=s=>t.post("/xingtu/projects/update",s),e=s=>t.post("/xingtuOrderCancel",s),o=s=>t.post("/xingtuBillPending",s),a=s=>t.post("/xingtuBillPay",s),i=s=>t.post("/xingtuOrderCancelAmount",s),g=s=>t.post("/xingtuOrderDemanderRejectResource",s),r=s=>t.post("/xingtuOrderDetail",s),p=s=>t.post("/xingtu/projects",s),u=s=>t.get("/xingtu/projects/tasks",s),d=s=>t.get("/xingtu/customer_profile",s),x=s=>t.post("/xingtuAttachmentUpload",s),l=s=>t.post("/xingtuTaskOrderInfo",s),c=s=>t.get("/xingtu/get_role_users/117",s),m=s=>t.post("/xingtuComponent",s,{loading:!1}),O=s=>t.post("/xingtuIndustryComponent",s),j=s=>t.post("/xingtuBrand",s,{loading:!1}),y=s=>t.post("/xingtuOrder",s),B=s=>t.post("/xingtuOrderDemanderApproveResource",s),C=s=>t.post("/xingtuOrderFinish",s),_=s=>t.post("/xingtuInvestmentBase",s),f=s=>t.post("/xingtuOrderExecutorUpdate",s,{loading:!1}),k=s=>t.post("/yuntuBrand",s,{loading:!1}),A=s=>t.post("/xingtuProjectOnline",s,{loading:!1}),h=s=>t.get("/xingtu/get_role_users/119",s,{loading:!1}),v=s=>t.post("/syncdata",s,{loading:!1}),D=s=>t.post("/xingtuContact",s,{loading:!1});export{c as A,O as a,x as b,D as c,l as d,y as e,j as f,A as g,h,_ as i,r as j,e as k,a as l,f as m,o as n,C as o,u as p,m as q,g as r,i as s,B as t,n as u,p as v,s as w,v as x,k as y,d as z};
