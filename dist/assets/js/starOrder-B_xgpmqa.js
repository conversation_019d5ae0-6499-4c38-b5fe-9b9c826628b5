import{s as e,H as l,u as a,aA as t,r as o,f as r,a6 as n,e as u,aK as i,p as s,I as d,a as c,Q as v,c as p,o as _,m,h,b,v as g,w as f,F as w,i as y,g as k,d as x,t as j,n as z,E as S}from"./index-C2bfFjZ1.js";import{S as C}from"./index-BMv7HewC.js";import P from"./taskDrawer-B_9GrKaI.js";import{N as D,O as I,P as L}from"./business-D2QRQb4T.js";import{_ as M}from"./_plugin-vue_export-helper-BXFjo1rG.js";const V={class:"table-box"},Y={class:"bg-white card table-main"},$={class:"content_box","element-loading-text":"loading...",style:{position:"relative"},"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},E={class:"task-detail-tabs-filter"},O={class:"task-detail-tabs-content"},U={class:"flex align-center"},A={class:"task-detail-tabs-content"},F={class:"task-detail-tabs-content"},R={class:"task-detail-tabs-content"},W={key:0,class:"avatar-stack"},X={key:1,class:"avatar-stack"},N={class:"avatar-stack-name"},q={style:{display:"flex","align-items":"center"}},B={style:{"font-size":"12px",color:"#999"}},H={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"180px","margin-bottom":"10px"}},J={style:{"font-size":"16px",color:"#000"}},K={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"180px","margin-bottom":"10px"}},Q={style:{"font-size":"16px",color:"#000"}},G={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"180px","margin-bottom":"10px","padding-bottom":"10px","border-bottom":"1px solid #eee"}},T={style:{"font-size":"16px",color:"#000"}},Z={style:{display:"flex","align-items":"center","justify-content":"space-between",width:"180px"}},ee={style:{"font-size":"16px",color:"#000"}},le={class:"cursor-pointer"},ae={style:{cursor:"pointer"}},te={key:1},oe={class:"flex justify-end",style:{"margin-bottom":"10px"}},re={key:0,style:{"font-size":"16px"}},ne={style:{"font-weight":"bold"}},ue={class:"dialog-footer"},ie=M(e({...e({name:"businessProject"}),props:{projectId:{type:Number,default:0},isSearch:{type:Boolean,default:!0}},setup(e){var M;const ie=l();a();const se=t(),de=o(!0),ce=o({}),ve={xs:1,sm:2,md:2,lg:3,xl:4},pe=o("second"),_e=o(!1),me=o([]),he=o([]),be=o([]),ge=o({1:"指派",2:"招募",3:"投稿",4:"星广联投"}),fe=o({"-3":"待付款","-1":"待接收",1:"达人已接单",2:"已关闭",3:"已完成",4:"已取消",10:"待付尾款",41:"脚本已上传",42:"脚本已拒绝",43:"脚本已确认",44:"脚本已跳过",51:"视频已上传",52:"视频已拒绝",53:"视频已确认",54:"视频已发布",66:"下单失败"}),we=o([]),ye=e;r(se.messages,(()=>{let e=se.messages[se.messages.length-1];"error_message"==e.event&&S.error(e.data.msg),"success_message"==e.event&&S.success(e.data.msg)}),{deep:!0});const ke=e=>{$e.value.page_size=e,Ve($e.value.order_status)},xe=e=>{$e.value.page=e,Ve($e.value.order_status)},je=e=>{"second"==pe.value&&((null==e?void 0:e.created_at)&&($e.value.created_at={start_time:e.created_at[0],end_time:e.created_at[1]}),$e.value={...$e.value,...e},Ve($e.value.order_status))},ze=o(null),Se=()=>{var e;"second"==pe.value&&($e.value={task_type:"",task_name:"",customer_name:"",project_name:"",project_id:"",order_status:"",page_size:10,page:1,created_at:{start_time:"",end_time:""}},ce.value.created_at=null,we.value=[],null==(e=ze.value)||e.reset(),Ve($e.value.order_status))};o([]),o(!1);const Ce=n([{prop:"task_type",label:"任务类型",search:{el:"select",props:{placeholder:"全部"}},enum:[{label:"指派任务",value:1},{label:"招募任务",value:2},{label:"投稿任务",value:3},{label:"星广联投",value:4}]},{prop:"task_search",label:"任务信息",search:{el:"input",props:{placeholder:"请输入任务名称或ID"}}},{prop:"custom_name",label:"客户名称",search:{el:"input",props:{placeholder:"请输入客户名称"}}},{prop:"project_name",label:"项目名称",search:{el:"input",props:{placeholder:"请输入项目名称"}}},{prop:"task_time",label:"创建时间",search:{el:"date-picker",span:1,props:{type:"daterange",valueFormat:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始时间",endPlaceholder:"结束时间"},defaultValue:we.value}},{prop:"operation",label:"操作",fixed:"right",width:100}]);null==(M=u((()=>null==Ce?void 0:Ce.filter((e=>{var l,a;return(null==(l=e.search)?void 0:l.el)||(null==(a=e.search)?void 0:a.render)})).sort(((e,l)=>e.search.order-l.search.order)))).value)||M.forEach(((e,l)=>{var a,t,o;e.search.order=(null==(a=e.search)?void 0:a.order)??l+2;const r=(null==(t=e.search)?void 0:t.key)??i(e.prop),n=null==(o=e.search)?void 0:o.defaultValue;null!=n&&(ce.value[r]=n)}));const Pe=(e,l)=>{l?(localStorage.setItem("kol_info",JSON.stringify([e.process_kol_id])),ie.push({path:"/business/task/createOrder",query:{page_type:"add",id:e.task_id,process_id:e.id}})):ie.push({path:"/business/task/taskDetail",query:{task_id:e.task_id,order_id:e.order_id,star_id:e.star_id,key:"4-4",id:e.id}})};o(!1);const De=o([]);o("");const Ie=o([]),Le=o(!1);r(De,(e=>{Ie.value=e.map((e=>0!==e.order_status&&null))}),{immediate:!0}),r(Ie,(e=>{const l=De.value.every(((l,a)=>0!==l.order_status||e[a]));Le.value=l}));const Me=e=>{he.value.length>1&&(he.value=[he.value[he.value.length-1]]),$e.value.order_status=he.value[0],Ve(he.value[0])},Ve=async e=>{_e.value=!0,ye.isSearch||($e.value.project_id=ye.projectId.toString()),$e.value.order_status=e||he.value[0];let l={...$e.value,created_at:{start_time:we.value[0],end_time:we.value[1]}};await D(l).then((e=>{_e.value=!1;const l=null==e?void 0:e.data,a=null==l?void 0:l.list;me.value=a;let t=(null==l?void 0:l.order_count_list)||{};const o=null==l?void 0:l.count;Ye.total=o;let r=0;Object.values(t).forEach((e=>{r+=e})),(null==l?void 0:l.order_count_list)?be.value=[{label:`全部（${r||0}）`,value:"",count:r||0},{label:`待接收(${t[-1]||0})`,value:"-1",count:t[-1]||0},{label:`待付款(${t[-3]||0})`,value:"-3",count:t[-3]||0},{label:`进行中(${t.ongoing||0})`,value:100,count:t.ongoing||0},{label:`已完成(${t[3]||0})`,value:"3",count:t[3]||0},{label:`已取消(${t[4]||0})`,value:"4",count:t[4]||0},{label:`已关闭(${t[2]||0})`,value:"2",count:t[2]||0},{label:`下单失败(${t[66]||0})`,value:"66",count:t[66]||0}]:be.value=[{label:"全部（0）",value:"",count:0},{label:"待接收(0)",value:"-1",count:0},{label:"待付款(0)",value:"-3",count:0},{label:"进行中(0)",value:"100",count:0},{label:"已完成(0)",value:"3",count:0},{label:"已取消(0)",value:"4",count:0},{label:"已关闭(0)",value:"2",count:0},{label:"下单失败(0)",value:"66",count:0}]}))},Ye=n({total:0}),$e=o({task_type:"",task_name:"",customer_name:"",project_name:"",project_id:"",order_status:"",page_size:10,page:1}),Ee=o("");s((()=>{je()})),d((()=>{window.removeEventListener("resize",(()=>{Ee.value=(()=>{let e=0;const l=window.screen,a=navigator.userAgent.toLowerCase();return void 0!==window.devicePixelRatio?e=window.devicePixelRatio:~a.indexOf("msie")?l.deviceXDPI&&l.logicalXDPI&&(e=l.deviceXDPI/l.logicalXDPI):void 0!==window.outerWidth&&void 0!==window.innerWidth&&(e=window.outerWidth/window.innerWidth),e&&(e=Math.round(100*e)),e})().toString()}))}));const Oe=o(!1),Ue=o(null),Ae=o(0),Fe=()=>{const e=Ue.value;if(!e)return;let l={star_id:e.star_id,order_id:e.order_id,campaign_id:e.campaign_id};_e.value=!0,L(l).then((e=>{_e.value=!1,Oe.value=!1,S.success("支付成功"),Ve($e.value.order_status)})).catch((e=>{_e.value=!1,S.error("支付失败")}))},Re=()=>{Oe.value=!1},We=u((()=>[{prop:"order_type",label:"订单类型",search:{el:"select",props:{placeholder:"全部"}},enum:[{label:"自运营",value:1},{label:"走单（不含代理服务费）",value:2},{label:"代下单（含代理服务费）",value:3},{label:"资源包订单",value:4},{label:"水下订单",value:5}]},{prop:"platform_type",label:"媒体平台",search:{el:"select",props:{placeholder:"全部"}},enum:[{label:"抖音-不含星立方",value:1},{label:"抖音-星立方",value:2},{label:"小红书",value:3},{label:"快手",value:4},{label:"B站",value:5},{label:"腾讯互选",value:6}]},{prop:"task_name",label:"任务名称",search:{el:"input",props:{placeholder:"请输入任务名称"}}},{prop:"customer_name",label:"客户名称",search:{el:"input",props:{placeholder:"请输入客户名称"}}},{prop:"project_name",label:"项目名称",search:{el:"input",props:{placeholder:"请输入项目名称"}}},{prop:"created_at",label:"创建时间",search:{el:"date-picker",span:1,props:{type:"daterange",valueFormat:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始时间",endPlaceholder:"结束时间"}}}]));return(e,l)=>{const a=c("el-checkbox"),t=c("el-checkbox-group"),o=c("el-tag"),r=c("el-table-column"),n=c("el-avatar"),u=c("MoreFilled"),i=c("el-icon"),s=c("el-tooltip"),d=c("el-button"),D=c("el-table"),L=c("el-pagination"),M=c("el-dialog"),ie=v("loading");return _(),p("div",V,[m(b(C,{onSearch:je,onReset:Se,columns:We.value,"search-param":ce.value,"search-col":ve,created_at:we.value,"onUpdate:created_at":l[0]||(l[0]=e=>we.value=e)},null,8,["columns","search-param","created_at"]),[[g,de.value&&ye.isSearch&&"second"===pe.value]]),h("div",Y,[m((_(),p("div",$,[h("div",E,[b(t,{modelValue:he.value,"onUpdate:modelValue":l[1]||(l[1]=e=>he.value=e),size:"default",onChange:Me},{default:f((()=>[(_(!0),p(w,null,y(be.value,(e=>(_(),k(a,{class:"task-detail-tabs-filter-item",disabled:!e.count,key:e.value,label:e.value},{default:f((()=>[x(j(e.label),1)])),_:2},1032,["disabled","label"])))),128))])),_:1},8,["modelValue"])]),b(D,{data:me.value,style:{width:"100%",height:"calc(100% - 100px)"}},{default:f((()=>[b(r,{prop:"date",label:"任务信息",width:"280px"},{default:f((e=>{var a,t,r,n,u,i;return[h("div",null,j(null==(a=null==e?void 0:e.row)?void 0:a.task_name),1),h("div",O,[h("div",null,"任务ID："+j(null==(r=null==(t=null==e?void 0:e.row)?void 0:t.order_id)?void 0:r.toString()),1),h("div",null,"创建时间："+j(null==(n=null==e?void 0:e.row)?void 0:n.created_at),1),h("div",U,[(null==(u=null==e?void 0:e.row)?void 0:u.task_type)?(_(),k(o,{key:0,class:"mr-2",type:"success",size:"small"},{default:f((()=>{var l;return[x(j(ge.value[null==(l=null==e?void 0:e.row)?void 0:l.task_type]),1)]})),_:2},1024)):z("",!0),1==(null==(i=null==e?void 0:e.row)?void 0:i.promotion_platforms_genres)?(_(),k(o,{key:1,size:"small"},{default:f((()=>l[6]||(l[6]=[x("抖音短视频")]))),_:1})):z("",!0)])])]})),_:1}),b(r,{prop:"date",label:"项目信息",width:"220px"},{default:f((e=>{var l,a;return[h("div",null,j(null==(l=null==e?void 0:e.row)?void 0:l.project_name),1),h("div",A,"需求ID："+j(null==(a=null==e?void 0:e.row)?void 0:a.campaign_id),1)]})),_:1}),b(r,{prop:"date",label:"客户信息",width:"270px"},{default:f((e=>{var l,a,t;return[h("div",null,j(null==(l=null==e?void 0:e.row)?void 0:l.customer_name),1),h("div",F,j(null==(a=null==e?void 0:e.row)?void 0:a.customer_contract_name),1),h("div",R,j(null==(t=null==e?void 0:e.row)?void 0:t.customer_code),1)]})),_:1}),b(r,{prop:"name",label:"任务参与信息",width:"180px"},{default:f((e=>{var l,a,t,o,r,u,i,s,d,c,v,f,x;return["array"==(x=null==(l=null==e?void 0:e.row)?void 0:l.author_info,Array.isArray(x)?"array":"object"==typeof x&&null!==x?"object":"neither")?(_(),p("div",W,[(_(!0),p(w,null,y(null==(a=null==e?void 0:e.row)?void 0:a.author_info.filter(((e,l)=>l<=3)),(e=>(_(),k(n,{key:e.author_id,class:"avatar",shape:"circle",size:"small",src:e.avatar_uri},null,8,["src"])))),128)),m(h("div",{class:"number-avatar"}," +"+j((null==(t=null==e?void 0:e.row)?void 0:t.author_info.length)-3),513),[[g,(null==(o=null==e?void 0:e.row)?void 0:o.author_info.length)>3]])])):(_(),p("div",X,[b(n,{class:"avatar-stack-avatar",shape:"circle",size:"small",src:(null==(u=null==(r=null==e?void 0:e.row)?void 0:r.author_info)?void 0:u.avatar_uri)||""},null,8,["src"]),h("div",N,[h("span",null,j(null==(s=null==(i=null==e?void 0:e.row)?void 0:i.author_info)?void 0:s.author_name),1),m(h("span",null,"（"+j(null==(c=null==(d=null==e?void 0:e.row)?void 0:d.author_info)?void 0:c.contact_phone)+"）",513),[[g,null==(f=null==(v=null==e?void 0:e.row)?void 0:v.author_info)?void 0:f.contact_phone]])])]))]})),_:1}),b(r,{prop:"date",label:"考核要求",width:"150"},{default:f((e=>[b(s,{placement:"bottom",effect:"light"},{content:f((()=>{var a,t,o,r;return[x(" CPM:"+j(null==(a=null==e?void 0:e.row)?void 0:a.cpm_min_price)+"-"+j(null==(t=null==e?void 0:e.row)?void 0:t.cpm_max_price),1),l[7]||(l[7]=h("br",null,null,-1)),x("CPE:"+j(null==(o=null==e?void 0:e.row)?void 0:o.cpe_min_price)+"-"+j(null==(r=null==e?void 0:e.row)?void 0:r.cpe_max_price),1)]})),default:f((()=>{var l,a;return[h("div",q,[x(" CPM:"+j(null==(l=null==e?void 0:e.row)?void 0:l.cpm_min_price)+"-"+j(null==(a=null==e?void 0:e.row)?void 0:a.cpm_max_price),1),b(i,{style:{transform:"rotate(90deg)"}},{default:f((()=>[b(u)])),_:1})])]})),_:2},1024)])),_:1}),b(r,{prop:"money",label:"下单账户",width:"200px"},{default:f((e=>{var l,a;return[h("p",null,j(null==(l=null==e?void 0:e.row)?void 0:l.star_name),1),h("p",B,"账户ID："+j(null==(a=null==e?void 0:e.row)?void 0:a.star_id),1)]})),_:1}),b(r,{prop:"status",label:"结算方式",width:"120"},{default:f((e=>{var l;return[h("div",null,j(1==(null==(l=null==e?void 0:e.row)?void 0:l.settlement_method)?"一口价结算":"其他"),1)]})),_:1}),b(r,{prop:"amount",label:"任务金额",width:"120"},{default:f((e=>[b(s,{placement:"bottom",effect:"light"},{content:f((()=>{var a,t,o,r,n,u,i,s,d,c,v,p;return[h("div",H,[l[8]||(l[8]=h("p",null,"任务金额",-1)),h("p",J," ¥ "+j(null==(o=(null==(t=null==(a=null==e?void 0:e.row)?void 0:a.payment_info)?void 0:t.precise_task_cost)||0)?void 0:o.toLocaleString()),1)]),h("div",K,[l[9]||(l[9]=h("p",null,"平台服务费",-1)),h("p",Q," ¥ "+j(null==(u=(null==(n=null==(r=null==e?void 0:e.row)?void 0:r.payment_info)?void 0:n.precise_platform_fee)||0)?void 0:u.toLocaleString()),1)]),h("div",G,[l[10]||(l[10]=h("p",null,"赔付金额",-1)),h("p",T," ¥ "+j(null==(d=(null==(s=null==(i=null==e?void 0:e.row)?void 0:i.payment_info)?void 0:s.precise_deduct_amount)||0)?void 0:d.toLocaleString()),1)]),h("div",Z,[l[11]||(l[11]=h("p",null,"总计",-1)),h("p",ee," ¥ "+j(null==(p=(null==(v=null==(c=null==e?void 0:e.row)?void 0:c.payment_info)?void 0:v.precise_total)||0)?void 0:p.toLocaleString()),1)])]})),default:f((()=>{var l,a,t,o,r,n;return[h("p",le,"¥ "+j(null==(t=(null==(a=null==(l=null==e?void 0:e.row)?void 0:l.payment_info)?void 0:a.precise_total)||0)?void 0:t.toLocaleString()),1),h("p",null,"已结算："+j(null==(n=(null==(r=null==(o=null==e?void 0:e.row)?void 0:o.payment_info)?void 0:r.precise_total_paid)||0)?void 0:n.toLocaleString()),1)]})),_:2},1024)])),_:1}),b(r,{prop:"stats",label:"任务状态",width:"120"},{default:f((e=>{var l,a,t;return[66==(null==(l=null==e?void 0:e.row)?void 0:l.order_status)?(_(),k(s,{key:0,class:"box-item",effect:"dark",content:null==(a=null==e?void 0:e.row)?void 0:a.reason,placement:"top"},{default:f((()=>{var l;return[h("span",ae,j(fe.value[null==(l=null==e?void 0:e.row)?void 0:l.order_status]),1)]})),_:2},1032,["content"])):(_(),p("span",te,j(fe.value[null==(t=null==e?void 0:e.row)?void 0:t.order_status]),1))]})),_:1}),b(r,{label:"操作",width:"180px",fixed:"right",align:"center"},{default:f((e=>{var a,t;return[b(d,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:l=>Pe(null==e?void 0:e.row)},{default:f((()=>l[12]||(l[12]=[x("详情")]))),_:2},1032,["onClick"]),m(b(d,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:l=>Pe(null==e?void 0:e.row,"reset")},{default:f((()=>l[13]||(l[13]=[x("重新下单")]))),_:2},1032,["onClick"]),[[g,[4,66].includes(null==(a=e.row)?void 0:a.order_status)]]),m(b(d,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:l=>(e=>{Ue.value=e,_e.value=!0;let l={star_id:e.star_id,campaign_id:e.campaign_id,order_id:e.order_id};I(l).then((e=>{_e.value=!1;const l=null==e?void 0:e.data;l?(Ae.value=l.precise_total_remaining,Oe.value=!0):S.error("获取支付金额失败")})).catch((e=>{_e.value=!1,S.error("获取支付金额失败")}))})(null==e?void 0:e.row)},{default:f((()=>l[14]||(l[14]=[x("付款")]))),_:2},1032,["onClick"]),[[g,[10,-3].includes(null==(t=e.row)?void 0:t.order_status)]])]})),_:1})])),_:1},8,["data"]),h("div",oe,[b(L,{"current-page":$e.value.page,"onUpdate:currentPage":l[2]||(l[2]=e=>$e.value.page=e),"page-size":$e.value.page_size,"onUpdate:pageSize":l[3]||(l[3]=e=>$e.value.page_size=e),total:Ye.total,"page-sizes":[10,20,30,50],onSizeChange:ke,onCurrentChange:xe,background:"",layout:"total ,sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])])),[[ie,_e.value]])]),b(P,{ref:"drawerRef"},null,512),b(M,{modelValue:Oe.value,"onUpdate:modelValue":l[5]||(l[5]=e=>Oe.value=e),title:"支付尾款",width:"400px","before-close":Re},{footer:f((()=>[h("div",ue,[b(d,{onClick:l[4]||(l[4]=e=>Oe.value=!1)},{default:f((()=>l[16]||(l[16]=[x("取消")]))),_:1}),b(d,{type:"primary",onClick:Fe},{default:f((()=>l[17]||(l[17]=[x("确认支付")]))),_:1})])])),default:f((()=>[Ue.value?(_(),p("span",re,[l[15]||(l[15]=x(" 支付金额： ")),h("span",ne," ¥ "+j(Ae.value.toLocaleString()),1)])):z("",!0)])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-9c2d87ca"]]);export{ie as default};
