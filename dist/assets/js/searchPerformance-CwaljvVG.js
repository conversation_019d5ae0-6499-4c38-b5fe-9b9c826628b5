import{r as e,f as a,a2 as t,a3 as o,a as s,c as r,o as n,h as l,m as d,F as i,i as c,t as u,d as v,n as g,b as h,w as p,v as m}from"./index-BE6Fh1xm.js";import{t as _}from"./tools-Cu1QpDc6.js";import{_ as w}from"./_plugin-vue_export-helper-GSmkUi5K.js";const f={class:"author-page-content"},b={class:"author-overview"},y={class:"card-panel module-card composite"},C={class:"card-panel-body"},x={class:"overall-data"},k={class:"overall-data-block"},M={class:"data-note"},S={class:"data-value"},F={key:0},A={class:"card-panel module-card composite"},D={class:"title-wrapper"},z={class:"card-panel-body"},j={class:"card-panel module-card composite"},B=w({__name:"searchPerformance",props:["orderContent","activeName"],setup(w){const B=w;e(0);const O=e();e([]);const Y=[{name:"看后搜次数",label:"long_aftersearch"},{name:"看后搜率",label:"long_after_search_rate",content:"%"},{name:"回搜次数",label:"long_backsearch"},{name:"回搜率",label:"long_back_search_rate",content:"%"}],E=e([]),I=e("long_aftersearch"),V=e([{type:"wordCloud",shape:"circle",keepAspect:!1,left:"center",top:"center",width:"100%",height:"100%",right:null,bottom:null,sizeRange:[12,60],rotationRange:[-45,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,layoutAnimation:!0,textStyle:{fontFamily:"sans-serif",fontWeight:"bold",color:function(){return"rgb("+[Math.round(160*Math.random()),Math.round(160*Math.random()),Math.round(160*Math.random())].join(",")+")"}},emphasis:{focus:"self",textStyle:{textShadowBlur:10,textShadowColor:"#999"}},data:[]}]),$=e({color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272"],tooltip:{show:!0,trigger:"item",formatter:function(e){return"日期："+e.name+"<br />数量："+L(e.data)}},legend:{},toolbox:{show:!0},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{type:"value"},series:[{data:[820,932,901,934,1290,1330,1320],type:"line",smooth:!0}]}),L=e=>e>=1e4?(e/1e4).toFixed(0)+"w":e;a((()=>B.orderContent.kw_suggest_words),((e,a)=>{if(B.orderContent&&B.orderContent.kw_suggest_words&&B.orderContent.kw_suggest_words.length>0)for(var t of(V.value[0].data=[],B.orderContent.kw_suggest_words))V.value[0].data.push({name:t,value:Math.round(160*Math.random())});R()})),t((()=>{var e,a;"second"==B.activeName&&($.value.series[0].data=null==(e=B.orderContent[I.value])?void 0:e.map((e=>e.value)),$.value.xAxis.data=null==(a=B.orderContent[I.value])?void 0:a.map((e=>(""+e.date).slice(4,6)+"-"+(""+e.date).slice(6,8))),T(),o.init(document.getElementById("charts-content")).setOption({series:V.value}))}));const N=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,R=()=>{const e=new Date,a=new Date(e.getFullYear(),e.getMonth(),e.getDate()-6);a.setHours(0,0,0,0);const t=new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59);E.value=[N(a),N(t)]},T=()=>{var e=document.getElementById("lineSearchChart");O.value=o.init(e),$.value&&O.value.setOption($.value),window.addEventListener("resize",(()=>{W()}))},W=()=>{O.value&&O.value.resize()};return t((()=>{if(B.orderContent&&B.orderContent.kw_suggest_words&&B.orderContent.kw_suggest_words.length>0)for(var e of(V.value[0].data=[],B.orderContent.kw_suggest_words))V.value[0].data.push({name:e,value:Math.round(160*Math.random())});R()})),(e,a)=>{const t=s("el-radio-button"),o=s("el-radio-group");return n(),r("div",f,[l("div",b,[l("div",y,[a[1]||(a[1]=l("div",{class:"title-wrapper"},[l("div",{class:"title"}," 搜索分析 "),l("span",{class:"desc"})],-1)),l("div",C,[l("div",x,[(n(),r(i,null,c(Y,(e=>{return l("div",k,[l("span",M,u(e.name),1),l("span",S,[v(u((a=B.orderContent[e.label],_(a instanceof Array?a[a.length-1].value:a)))+" ",1),e.content?(n(),r("span",F,"%")):g("",!0)])]);var a})),64))])])]),l("div",A,[l("div",D,[a[4]||(a[4]=l("div",{class:"title"}," 看后搜/回搜 趋势 ",-1)),a[5]||(a[5]=l("span",{class:"desc"},null,-1)),h(o,{modelValue:I.value,"onUpdate:modelValue":a[0]||(a[0]=e=>I.value=e),size:"small"},{default:p((()=>[h(t,{label:"long_aftersearch"},{default:p((()=>a[2]||(a[2]=[v("看后搜")]))),_:1}),h(t,{label:"long_backsearch"},{default:p((()=>a[3]||(a[3]=[v("回搜")]))),_:1})])),_:1},8,["modelValue"])]),l("div",z,[(n(),r("div",{id:"lineSearchChart",key:I.value,ref:"chartLine",style:{width:"100%",height:"500px"}}))])]),d(l("div",j,a[6]||(a[6]=[l("div",{class:"title-wrapper"},[l("div",{class:"title"}," 小蓝词 "),l("span",{class:"desc"})],-1)]),512),[[m,w.orderContent.kw_suggest_words&&w.orderContent.kw_suggest_words.length>0]]),a[7]||(a[7]=l("div",{id:"charts-content",style:{width:"600px",height:"200px"}},null,-1))])])}}},[["__scopeId","data-v-9e5c5228"]]);export{B as default};
