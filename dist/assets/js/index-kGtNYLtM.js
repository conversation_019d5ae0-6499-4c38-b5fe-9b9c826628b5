import{_ as a}from"./_plugin-vue_export-helper-GSmkUi5K.js";import{Q as s,c as e,o as t,h as o,m as r,d as n}from"./index-BE6Fh1xm.js";const c={class:"card content-box"},d={class:"drag-box flx-center"};const l=a({},[["render",function(a,l){const p=s("draggable");return t(),e("div",c,[l[1]||(l[1]=o("span",{class:"text"},"拖拽指令 🍇🍇🍇🍓🍓🍓",-1)),r((t(),e("div",d,l[0]||(l[0]=[n("我可以拖拽哦~")]))),[[p]])])}],["__scopeId","data-v-ae0a9654"]]);export{l as default};
