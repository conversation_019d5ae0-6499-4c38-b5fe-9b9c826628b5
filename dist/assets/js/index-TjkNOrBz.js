import{s as e,r as l,a6 as a,a as t,c as r,o as d,b as o,w as i,h as u,d as s,E as n}from"./index-BE6Fh1xm.js";import{_ as p}from"./_plugin-vue_export-helper-GSmkUi5K.js";function m(e,l,a){if(""===l&&a("请输入手机号码"),/^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/.test(l))return a();a(new Error("请输入正确的手机号码"))}const c={class:"card content-box"},g=e({name:"dynamicForm"}),f=p(e({...g,setup(e){const p=l(),g=a({name:"Gee<PERSON>-Admin",phone:"",region:"",date1:"",date2:"",delivery:!1,resource:"",desc:""}),f=a({name:[{required:!0,message:"Please input Activity name",trigger:"blur"},{min:3,max:5,message:"Length should be 3 to 5",trigger:"blur"}],phone:[{required:!0,validator:m,trigger:"blur"}],region:[{required:!0,message:"Please select Activity zone",trigger:"change"}],date1:[{type:"date",required:!0,message:"Please pick a date",trigger:"change"}],date2:[{type:"date",required:!0,message:"Please pick a time",trigger:"change"}],resource:[{required:!0,message:"Please select activity resource",trigger:"change"}],desc:[{required:!0,message:"Please input activity form",trigger:"blur"}]});return(e,l)=>{const a=t("el-input"),m=t("el-form-item"),y=t("el-option"),h=t("el-select"),v=t("el-date-picker"),V=t("el-col"),b=t("el-time-picker"),_=t("el-switch"),k=t("el-radio"),A=t("el-radio-group"),x=t("el-button"),q=t("el-form");return d(),r("div",c,[o(q,{ref_key:"ruleFormRef",ref:p,model:g,rules:f,"label-width":"140px"},{default:i((()=>[o(m,{label:"Activity name",prop:"name"},{default:i((()=>[o(a,{modelValue:g.name,"onUpdate:modelValue":l[0]||(l[0]=e=>g.name=e)},null,8,["modelValue"])])),_:1}),o(m,{label:"Activity phone",prop:"phone"},{default:i((()=>[o(a,{modelValue:g.phone,"onUpdate:modelValue":l[1]||(l[1]=e=>g.phone=e),placeholder:"Activity phone"},null,8,["modelValue"])])),_:1}),o(m,{label:"Activity zone",prop:"region"},{default:i((()=>[o(h,{modelValue:g.region,"onUpdate:modelValue":l[2]||(l[2]=e=>g.region=e),placeholder:"Activity zone"},{default:i((()=>[o(y,{label:"Zone one",value:"shanghai"}),o(y,{label:"Zone two",value:"beijing"})])),_:1},8,["modelValue"])])),_:1}),o(m,{label:"Activity time",required:""},{default:i((()=>[o(m,{prop:"date1"},{default:i((()=>[o(v,{modelValue:g.date1,"onUpdate:modelValue":l[3]||(l[3]=e=>g.date1=e),type:"date",placeholder:"Pick a date",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),o(V,{class:"text-center",span:1},{default:i((()=>l[10]||(l[10]=[u("span",{class:"text-gray-500"},"-",-1)]))),_:1}),o(m,{prop:"date2"},{default:i((()=>[o(b,{modelValue:g.date2,"onUpdate:modelValue":l[4]||(l[4]=e=>g.date2=e),placeholder:"Pick a time",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),o(m,{label:"Instant delivery",prop:"delivery"},{default:i((()=>[o(_,{modelValue:g.delivery,"onUpdate:modelValue":l[5]||(l[5]=e=>g.delivery=e)},null,8,["modelValue"])])),_:1}),o(m,{label:"Resources",prop:"resource"},{default:i((()=>[o(A,{modelValue:g.resource,"onUpdate:modelValue":l[6]||(l[6]=e=>g.resource=e)},{default:i((()=>[o(k,{label:"Sponsorship"}),o(k,{label:"Venue"})])),_:1},8,["modelValue"])])),_:1}),o(m,{label:"Activity form",prop:"desc"},{default:i((()=>[o(a,{modelValue:g.desc,"onUpdate:modelValue":l[7]||(l[7]=e=>g.desc=e),type:"textarea"},null,8,["modelValue"])])),_:1}),o(m,null,{default:i((()=>[o(x,{type:"primary",onClick:l[8]||(l[8]=e=>(async e=>{e&&await e.validate(((e,l)=>{e&&n.success("提交的数据为 : "+JSON.stringify(g))}))})(p.value))},{default:i((()=>l[11]||(l[11]=[s(" Create ")]))),_:1}),o(x,{onClick:l[9]||(l[9]=e=>{var l;(l=p.value)&&l.resetFields()})},{default:i((()=>l[12]||(l[12]=[s(" Reset ")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])}}}),[["__scopeId","data-v-65eabb62"]]);export{f as default};
