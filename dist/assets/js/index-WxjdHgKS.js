import{s as e,U as l,V as a,bn as t,r as n,g as s,a as u,w as o,b as i,h as d,d as r,z as c,y as m,n as p,a5 as v,bo as f,o as _,c as h,t as b,u as g,H as k,aa as y,q as w,ah as V,l as x,e as C,p as L,f as E,F as S,i as U,a0 as F,I,m as M,v as T,T as A,bp as q,aC as z,b8 as P,bq as j,br as D,an as B,E as G,aA as O,aB as R,al as $,K as N,bs as H,am as K,Q as J,at as W,a6 as Q}from"./index-C2bfFjZ1.js";import{_ as X}from"./index.vue_vue_type_script_setup_true_name_SwitchDark_lang-gMvYCGAE.js";import{_ as Y}from"./_plugin-vue_export-helper-BXFjo1rG.js";import{u as Z,a as ee}from"./tabs-B2gAxMzC.js";import{S as le}from"./sortable.esm-DeWNWKFU.js";import{u as ae,o as te,g as ne,_ as se,a as ue}from"./message-BJt-zJH1.js";import{r as oe}from"./user-R3PiK8LT.js";const ie={all:de=de||new Map,on:function(e,l){var a=de.get(e);a?a.push(l):de.set(e,[l])},off:function(e,l){var a=de.get(e);a&&(l?a.splice(a.indexOf(l)>>>0,1):de.set(e,[]))},emit:function(e,l){var a=de.get(e);a&&a.slice().map((function(e){e(l)})),(a=de.get("*"))&&a.slice().map((function(a){a(e,l)}))}};var de;const re={class:"layout-box"},ce={class:"theme-item"},me={class:"theme-item mb50"},pe={class:"theme-item"},ve={class:"theme-item"},fe={class:"theme-item"},_e={class:"theme-item mb40"},he={class:"theme-item"},be={class:"theme-item"},ge={class:"theme-item"},ke={class:"theme-item"},ye={class:"theme-item"},we={class:"theme-item"},Ve={class:"theme-item"},xe={class:"theme-item"},Ce=Y(e({__name:"index",setup(e){const{changePrimary:h,changeGreyOrWeak:b,setAsideTheme:g,setHeaderTheme:k}=l(),y=a(),{layout:w,primary:V,isGrey:x,isWeak:C,asideInverted:L,headerInverted:E,isCollapse:S,accordion:U,watermark:F,breadcrumb:I,breadcrumbIcon:M,tabs:T,tabsIcon:A,footer:q}=t(y),z=[f,"#daa96e","#0c819f","#409eff","#27ae60","#ff5c93","#e74c3c","#fd726d","#f39c12","#9b59b6"],P=e=>{y.setGlobalState("layout",e),g()},j=n(!1);return ie.on("openThemeDrawer",(()=>j.value=!0)),(e,l)=>{const a=u("Notification"),t=u("el-icon"),n=u("el-divider"),f=u("CircleCheckFilled"),y=u("el-tooltip"),D=u("QuestionFilled"),B=u("el-switch"),G=u("ColdDrink"),O=u("el-color-picker"),R=u("Setting"),$=u("el-drawer");return _(),s($,{modelValue:j.value,"onUpdate:modelValue":l[19]||(l[19]=e=>j.value=e),title:"布局设置",size:"290px"},{default:o((()=>[i(n,{class:"divider","content-position":"center"},{default:o((()=>[i(t,null,{default:o((()=>[i(a)])),_:1}),l[20]||(l[20]=r(" 布局样式 "))])),_:1}),d("div",re,[i(y,{effect:"dark",content:"纵向",placement:"top","show-after":200},{default:o((()=>[d("div",{class:c(["layout-item layout-vertical",{"is-active":"vertical"==m(w)}]),onClick:l[0]||(l[0]=e=>P("vertical"))},[l[21]||(l[21]=d("div",{class:"layout-dark"},null,-1)),l[22]||(l[22]=d("div",{class:"layout-container"},[d("div",{class:"layout-light"}),d("div",{class:"layout-content"})],-1)),"vertical"==m(w)?(_(),s(t,{key:0},{default:o((()=>[i(f)])),_:1})):p("",!0)],2)])),_:1}),i(y,{effect:"dark",content:"经典",placement:"top","show-after":200},{default:o((()=>[d("div",{class:c(["layout-item layout-classic",{"is-active":"classic"==m(w)}]),onClick:l[1]||(l[1]=e=>P("classic"))},[l[23]||(l[23]=d("div",{class:"layout-dark"},null,-1)),l[24]||(l[24]=d("div",{class:"layout-container"},[d("div",{class:"layout-light"}),d("div",{class:"layout-content"})],-1)),"classic"==m(w)?(_(),s(t,{key:0},{default:o((()=>[i(f)])),_:1})):p("",!0)],2)])),_:1}),i(y,{effect:"dark",content:"横向",placement:"top","show-after":200},{default:o((()=>[d("div",{class:c(["layout-item layout-transverse",{"is-active":"transverse"==m(w)}]),onClick:l[2]||(l[2]=e=>P("transverse"))},[l[25]||(l[25]=d("div",{class:"layout-dark"},null,-1)),l[26]||(l[26]=d("div",{class:"layout-content"},null,-1)),"transverse"==m(w)?(_(),s(t,{key:0},{default:o((()=>[i(f)])),_:1})):p("",!0)],2)])),_:1}),i(y,{effect:"dark",content:"分栏",placement:"top","show-after":200},{default:o((()=>[d("div",{class:c(["layout-item layout-columns",{"is-active":"columns"==m(w)}]),onClick:l[3]||(l[3]=e=>P("columns"))},[l[27]||(l[27]=d("div",{class:"layout-dark"},null,-1)),l[28]||(l[28]=d("div",{class:"layout-light"},null,-1)),l[29]||(l[29]=d("div",{class:"layout-content"},null,-1)),"columns"==m(w)?(_(),s(t,{key:0},{default:o((()=>[i(f)])),_:1})):p("",!0)],2)])),_:1})]),d("div",ce,[d("span",null,[l[30]||(l[30]=r(" 侧边栏反转色 ")),i(y,{effect:"dark",content:"侧边栏颜色变为深色模式",placement:"top"},{default:o((()=>[i(t,null,{default:o((()=>[i(D)])),_:1})])),_:1})]),i(B,{modelValue:m(L),"onUpdate:modelValue":l[4]||(l[4]=e=>v(L)?L.value=e:null),onChange:m(g)},null,8,["modelValue","onChange"])]),d("div",me,[d("span",null,[l[31]||(l[31]=r(" 头部反转色 ")),i(y,{effect:"dark",content:"头部颜色变为深色模式",placement:"top"},{default:o((()=>[i(t,null,{default:o((()=>[i(D)])),_:1})])),_:1})]),i(B,{modelValue:m(E),"onUpdate:modelValue":l[5]||(l[5]=e=>v(E)?E.value=e:null),onChange:m(k)},null,8,["modelValue","onChange"])]),i(n,{class:"divider","content-position":"center"},{default:o((()=>[i(t,null,{default:o((()=>[i(G)])),_:1}),l[32]||(l[32]=r(" 全局主题 "))])),_:1}),d("div",pe,[l[33]||(l[33]=d("span",null,"主题颜色",-1)),i(O,{modelValue:m(V),"onUpdate:modelValue":l[6]||(l[6]=e=>v(V)?V.value=e:null),predefine:z,onChange:m(h)},null,8,["modelValue","onChange"])]),d("div",ve,[l[34]||(l[34]=d("span",null,"暗黑模式",-1)),i(X)]),d("div",fe,[l[35]||(l[35]=d("span",null,"灰色模式",-1)),i(B,{modelValue:m(x),"onUpdate:modelValue":l[7]||(l[7]=e=>v(x)?x.value=e:null),onChange:l[8]||(l[8]=e=>m(b)("grey",!!e))},null,8,["modelValue"])]),d("div",_e,[l[36]||(l[36]=d("span",null,"色弱模式",-1)),i(B,{modelValue:m(C),"onUpdate:modelValue":l[9]||(l[9]=e=>v(C)?C.value=e:null),onChange:l[10]||(l[10]=e=>m(b)("weak",!!e))},null,8,["modelValue"])]),i(n,{class:"divider","content-position":"center"},{default:o((()=>[i(t,null,{default:o((()=>[i(R)])),_:1}),l[37]||(l[37]=r(" 界面设置 "))])),_:1}),d("div",he,[l[38]||(l[38]=d("span",null,"菜单折叠",-1)),i(B,{modelValue:m(S),"onUpdate:modelValue":l[11]||(l[11]=e=>v(S)?S.value=e:null)},null,8,["modelValue"])]),d("div",be,[l[39]||(l[39]=d("span",null,"菜单手风琴",-1)),i(B,{modelValue:m(U),"onUpdate:modelValue":l[12]||(l[12]=e=>v(U)?U.value=e:null)},null,8,["modelValue"])]),d("div",ge,[l[40]||(l[40]=d("span",null,"水印",-1)),i(B,{modelValue:m(F),"onUpdate:modelValue":l[13]||(l[13]=e=>v(F)?F.value=e:null)},null,8,["modelValue"])]),d("div",ke,[l[41]||(l[41]=d("span",null,"面包屑",-1)),i(B,{modelValue:m(I),"onUpdate:modelValue":l[14]||(l[14]=e=>v(I)?I.value=e:null)},null,8,["modelValue"])]),d("div",ye,[l[42]||(l[42]=d("span",null,"面包屑图标",-1)),i(B,{modelValue:m(M),"onUpdate:modelValue":l[15]||(l[15]=e=>v(M)?M.value=e:null)},null,8,["modelValue"])]),d("div",we,[l[43]||(l[43]=d("span",null,"标签栏",-1)),i(B,{modelValue:m(T),"onUpdate:modelValue":l[16]||(l[16]=e=>v(T)?T.value=e:null)},null,8,["modelValue"])]),d("div",Ve,[l[44]||(l[44]=d("span",null,"标签栏图标",-1)),i(B,{modelValue:m(A),"onUpdate:modelValue":l[17]||(l[17]=e=>v(A)?A.value=e:null)},null,8,["modelValue"])]),d("div",xe,[l[45]||(l[45]=d("span",null,"页脚",-1)),i(B,{modelValue:m(q),"onUpdate:modelValue":l[18]||(l[18]=e=>v(q)?q.value=e:null)},null,8,["modelValue"])])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-5dbb5f0f"]]),Le="/assets/png/30f32b13-BwBphHdk.png";function Ee(e){return"function"==typeof e?e():m(e)}"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const Se=()=>{};function Ue(e,l=200,a={}){return function(e,l){return function(...a){return new Promise(((t,n)=>{Promise.resolve(e((()=>l.apply(this,a)),{fn:l,thisArg:this,args:a})).then(t).catch(n)}))}}(function(e,l={}){let a,t,n=Se;const s=e=>{clearTimeout(e),n(),n=Se};return u=>{const o=Ee(e),i=Ee(l.maxWait);return a&&s(a),o<=0||void 0!==i&&i<=0?(t&&(s(t),t=null),Promise.resolve(u())):new Promise(((e,d)=>{n=l.rejectOnCancel?d:e,i&&!t&&(t=setTimeout((()=>{a&&s(a),t=null,e(u())}),i)),a=setTimeout((()=>{t&&s(t),t=null,e(u())}),o)}))}}(l,a),e)}const Fe=Y(e({__name:"Maximize",setup(e){const l=a(),t=()=>{l.setGlobalState("maximize",!1)};return(e,l)=>(_(),h("div",{class:"maximize",onClick:t},l[0]||(l[0]=[d("i",{class:c("iconfont icon-tuichu")},null,-1)])))}}),[["__scopeId","data-v-a8cb88c8"]]),Ie=Y(e({__name:"MoreButton",setup(e){const l=g(),t=k(),n=Z(),p=a(),v=ee(),f=y("refresh"),h=()=>{setTimeout((()=>{l.meta.isKeepAlive&&v.removeKeepAliveName(l.fullPath),f(!1),w((()=>{l.meta.isKeepAlive&&v.addKeepAliveName(l.fullPath),f(!0)}))}),0)},x=()=>{p.setGlobalState("maximize",!0)},C=()=>{l.meta.isAffix||n.removeTabs(l.fullPath)},L=()=>{n.closeMultipleTab(),t.push(V)};return(e,a)=>{const t=u("Refresh"),p=u("el-icon"),v=u("el-dropdown-item"),f=u("FullScreen"),g=u("Remove"),k=u("DArrowLeft"),y=u("DArrowRight"),w=u("CircleClose"),V=u("FolderDelete"),E=u("el-dropdown-menu"),S=u("el-dropdown");return _(),s(S,{trigger:"click",teleported:!1},{dropdown:o((()=>[i(E,null,{default:o((()=>[i(v,{onClick:h},{default:o((()=>[i(p,null,{default:o((()=>[i(t)])),_:1}),r(b(e.$t("tabs.refresh")),1)])),_:1}),i(v,{onClick:x},{default:o((()=>[i(p,null,{default:o((()=>[i(f)])),_:1}),r(b(e.$t("tabs.maximize")),1)])),_:1}),i(v,{divided:"",onClick:C},{default:o((()=>[i(p,null,{default:o((()=>[i(g)])),_:1}),r(b(e.$t("tabs.closeCurrent")),1)])),_:1}),i(v,{onClick:a[0]||(a[0]=e=>m(n).closeTabsOnSide(m(l).fullPath,"left"))},{default:o((()=>[i(p,null,{default:o((()=>[i(k)])),_:1}),r(b(e.$t("tabs.closeLeft")),1)])),_:1}),i(v,{onClick:a[1]||(a[1]=e=>m(n).closeTabsOnSide(m(l).fullPath,"right"))},{default:o((()=>[i(p,null,{default:o((()=>[i(y)])),_:1}),r(b(e.$t("tabs.closeRight")),1)])),_:1}),i(v,{divided:"",onClick:a[2]||(a[2]=e=>m(n).closeMultipleTab(m(l).fullPath))},{default:o((()=>[i(p,null,{default:o((()=>[i(w)])),_:1}),r(b(e.$t("tabs.closeOther")),1)])),_:1}),i(v,{onClick:L},{default:o((()=>[i(p,null,{default:o((()=>[i(V)])),_:1}),r(b(e.$t("tabs.closeAll")),1)])),_:1})])),_:1})])),default:o((()=>[a[3]||(a[3]=d("div",{class:"more-button"},[d("i",{class:c("iconfont icon-xiala")})],-1))])),_:1})}}}),[["__scopeId","data-v-556a0c97"]]),Me={class:"tabs-box"},Te={class:"tabs-menu"},Ae=Y(e({__name:"index",setup(e){const l=g(),t=k(),c=Z(),m=x(),v=a(),f=n(l.fullPath),y=C((()=>c.tabsMenuList)),w=C((()=>v.tabsIcon));L((()=>{I(),V()})),E((()=>l.fullPath),(()=>{if(l.meta.isFull)return;f.value=l.fullPath;const e={icon:l.meta.icon,title:l.meta.title,path:l.fullPath,name:l.name,close:!l.meta.isAffix,isKeepAlive:l.meta.isKeepAlive};c.addTabs(e)}),{immediate:!0});const V=()=>{m.flatMenuListGet.forEach((e=>{if(e.meta.isAffix&&!e.meta.isHide&&!e.meta.isFull){const l={icon:e.meta.icon,title:e.meta.title,path:e.path,name:e.name,close:!e.meta.isAffix,isKeepAlive:e.meta.isKeepAlive};c.addTabs(l)}}))},I=()=>{le.create(document.querySelector(".el-tabs__nav"),{draggable:".el-tabs__item",animation:300,onEnd({newIndex:e,oldIndex:l}){const a=[...c.tabsMenuList],t=a.splice(l,1)[0];a.splice(e,0,t),c.setTabs(a)}})},M=e=>{const l=e.props.name;t.push(l)},T=e=>{c.removeTabs(e,e==l.fullPath),0==y.value.length&&t.push("/")};return(e,l)=>{const a=u("el-icon"),t=u("el-tab-pane"),n=u("el-tabs");return _(),h("div",Me,[d("div",Te,[y.value.length>0?(_(),s(n,{key:0,modelValue:f.value,"onUpdate:modelValue":l[0]||(l[0]=e=>f.value=e),type:"card",onTabClick:M,onTabRemove:T},{default:o((()=>[(_(!0),h(S,null,U(y.value,(e=>(_(),s(t,{key:e.path,label:e.title,name:e.path,closable:e.close},{label:o((()=>[e.icon&&w.value?(_(),s(a,{key:0,class:"tabs-icon"},{default:o((()=>[(_(),s(F(e.icon)))])),_:2},1024)):p("",!0),r(" "+b(e.title),1)])),_:2},1032,["label","name","closable"])))),128))])),_:1},8,["modelValue"])):p("",!0),i(Ie)])])}}}),[["__scopeId","data-v-e9dd168b"]]),qe=Y(e({__name:"index",setup(e){const l=a(),{maximize:d,isCollapse:r,layout:c,tabs:v}=t(l),f=ee(),{keepAliveName:b}=t(f),g=n(!0);P("refresh",(e=>g.value=e));const k=new Map;function y(e,l){if(!e)return;const a=l.fullPath;let t=k.get(a);return t||(t={name:a,render:()=>z(e)},k.set(a,t)),z(t)}E((()=>d.value),(()=>{const e=document.getElementById("app");d.value?e.classList.add("main-maximize"):e.classList.remove("main-maximize")}),{immediate:!0}),E((()=>c.value),(()=>{const e=document.body,l=e.classList.contains("menu-collapsed")?"menu-collapsed":"";e.setAttribute("class",`${c.value} ${l}`.trim())}),{immediate:!0}),E((()=>r.value),(()=>{const e=document.body;r.value?e.classList.add("menu-collapsed"):e.classList.remove("menu-collapsed")}),{immediate:!0});const w=n(0),V=Ue((()=>{w.value=document.body.clientWidth,!r.value&&w.value<1200&&l.setGlobalState("isCollapse",!0),r.value&&w.value>1200&&l.setGlobalState("isCollapse",!1)}),100);return window.addEventListener("resize",V,!1),I((()=>{window.removeEventListener("resize",V)})),(e,l)=>{const a=u("router-view"),t=u("el-main");return _(),h(S,null,[M(i(Fe,null,null,512),[[T,m(d)]]),M(i(Ae,null,null,512),[[T,m(v)]]),i(t,null,{default:o((()=>[i(a,null,{default:o((({Component:e,route:l})=>[i(A,{appear:"",name:"fade-transform",mode:"out-in"},{default:o((()=>[(_(),s(q,{include:m(b)},[g.value?(_(),s(F(y(e,l)),{key:l.fullPath})):p("",!0)],1032,["include"]))])),_:2},1024)])),_:1})])),_:1})],64)}}}),[["__scopeId","data-v-55558ba2"]]),ze=Y(e({__name:"CollapseIcon",setup(e){const l=a(),t=()=>l.setGlobalState("isCollapse",!l.isCollapse);return(e,a)=>{const n=u("el-icon");return _(),s(n,{class:"collapse-icon",onClick:t},{default:o((()=>[(_(),s(F(m(l).isCollapse?"expand":"fold")))])),_:1})}}}),[["__scopeId","data-v-6ee6d1b9"]]),Pe={class:"breadcrumb-title"},je=Y(e({__name:"Breadcrumb",setup(e){const l=g();k();const t=x(),n=a(),r=C((()=>{let e=t.breadcrumbListGet[l.matched[l.matched.length-1].path]??[];return e[0].path!==V&&(e=[{path:V,meta:{icon:"HomeFilled",title:"首页"}},...e]),e}));return(e,l)=>{const a=u("el-icon"),t=u("el-breadcrumb-item"),v=u("el-breadcrumb");return _(),h("div",{class:c(["breadcrumb-box mask-image",!m(n).breadcrumbIcon&&"no-icon"])},[i(v,{"separator-icon":m(j)},{default:o((()=>[i(D,{name:"breadcrumb"},{default:o((()=>[(_(!0),h(S,null,U(r.value,((e,l)=>(_(),s(t,{key:e.path},{default:o((()=>[d("div",{class:c(["el-breadcrumb__inner is-link",{"item-no-icon":!e.meta.icon}])},[e.meta.icon&&m(n).breadcrumbIcon?(_(),s(a,{key:0,class:"breadcrumb-icon"},{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024)):p("",!0),d("span",Pe,b(e.meta.title),1)],2)])),_:2},1024)))),128))])),_:1})])),_:1},8,["separator-icon"])],2)}}}),[["__scopeId","data-v-e6dfe999"]]),De={class:"tool-bar-lf"},Be=Y(e({__name:"ToolBarLeft",setup(e){const l=a();return(e,a)=>(_(),h("div",De,[i(ze,{id:"collapseIcon"}),M(i(je,{id:"breadcrumb"},null,512),[[T,m(l).breadcrumb]])]))}}),[["__scopeId","data-v-b541c38e"]]),Ge={class:"search-menu"},Oe=["onMouseenter"],Re={class:"menu-lf"},$e={class:"menu-title"},Ne=Y(e({__name:"SearchMenu",setup(e){const l=k(),a=x(),t=C((()=>a.flatMenuListGet.filter((e=>!e.meta.isHide)))),r=n(""),p=n(null),v=n(!1),f=n("");E(v,(e=>{e?document.addEventListener("keydown",M):document.removeEventListener("keydown",M)}));const g=()=>{v.value=!0,w((()=>{setTimeout((()=>{var e;null==(e=p.value)||e.focus()}))}))},y=n([]),V=Ue((()=>{y.value=f.value?t.value.filter((e=>{var l;return(e.path.toLowerCase().includes(f.value.toLowerCase())||e.meta.title.toLowerCase().includes(f.value.toLowerCase()))&&!(null==(l=e.meta)?void 0:l.isHide)})):[],r.value=y.value.length?y.value[0].path:""}),300);E(f,V);const L=n(null),I=e=>{const l=y.value.length;if(0===l)return;const a=(y.value.findIndex((e=>e.path===r.value))+e+l)%l;r.value=y.value[a].path,w((()=>{var e;if(!(null==(e=L.value)?void 0:e.firstElementChild))return;const l=L.value.firstElementChild.clientHeight+12||0;L.value.scrollTop=a*l}))},M=e=>{"ArrowUp"===e.key?(e.preventDefault(),I(-1)):"ArrowDown"===e.key?(e.preventDefault(),I(1)):"Enter"===e.key&&(e.preventDefault(),T())},T=()=>{var e;const a=y.value.find((e=>e.path===r.value));a&&((null==(e=a.meta)?void 0:e.isLink)?window.open(a.meta.isLink,"_blank"):l.push(a.path),f.value="",v.value=!1)};return(e,l)=>{const a=u("el-input"),t=u("el-icon"),n=u("el-empty"),k=u("el-dialog");return _(),h("div",Ge,[d("i",{class:c(["iconfont icon-sousuo","toolBar-icon"]),onClick:g}),i(k,{class:"search-dialog",modelValue:v.value,"onUpdate:modelValue":l[2]||(l[2]=e=>v.value=e),width:600,"show-close":!1,top:"10vh"},{default:o((()=>[i(a,{modelValue:f.value,"onUpdate:modelValue":l[0]||(l[0]=e=>f.value=e),ref_key:"menuInputRef",ref:p,placeholder:"菜单搜索：支持菜单名称、路径",size:"large",clearable:"","prefix-icon":m(B)},null,8,["modelValue","prefix-icon"]),y.value.length?(_(),h("div",{key:0,class:"menu-list",ref_key:"menuListRef",ref:L},[(_(!0),h(S,null,U(y.value,(e=>(_(),h("div",{key:e.path,class:c(["menu-item",{"menu-active":e.path===r.value}]),onMouseenter:l=>{return a=e,void(r.value=a.path);var a},onClick:l[1]||(l[1]=e=>T())},[d("div",Re,[i(t,{class:"menu-icon"},{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024),d("span",$e,b(e.meta.title),1)]),d("i",{class:c(["iconfont icon-huiche","menu-enter"]),onClick:g})],42,Oe)))),128))],512)):(_(),s(n,{key:1,class:"mt20 mb20","image-size":100,description:"暂无菜单"}))])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-c3424415"]]),He={class:"theme-setting"},Ke=e({__name:"ThemeSetting",setup(e){const l=()=>{ie.emit("openThemeDrawer")};return(e,a)=>(_(),h("div",He,[d("i",{class:c(["iconfont icon-zhuti","toolBar-icon"]),onClick:l})]))}}),Je={class:"publish-bulletin"},We={class:"dialog-footer"},Qe=Y(e({__name:"PublishBulletin",setup(e){const l=ae(),a=n(!1),t=n(),s=n(!1),c=n([]),m=[{label:"公告通知",value:1},{label:"功能上新",value:2},{label:"数据记录",value:3}],p=n({name:"",bulletin_type:void 0,modality:1,visible:"0",content:""}),v=n({name:[{required:!0,message:"请输入公告名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],bulletin_type:[{required:!0,message:"请选择公告类型",trigger:"change"}],modality:[{required:!0,message:"请选择公告形式",trigger:"change"}],content:[{required:!0,message:"请输入公告描述",trigger:"blur"},{min:1,max:1200,message:"长度在 1 到 1200 个字符",trigger:"blur"}]}),f=()=>{a.value=!0,g()},b=()=>{a.value=!1,g()},g=()=>{p.value={name:"",bulletin_type:void 0,modality:1,visible:"0",content:""},c.value=[],t.value&&t.value.resetFields()},k=async()=>{if(t.value)try{await t.value.validate(),s.value=!0,c.value.length>0?p.value.visible=c.value.join(","):p.value.visible="0",await te(p.value),G.success("发布成功"),a.value=!1;try{const e=await ne({page:1,page_size:5,is_read:1});l.updateUnreadMessage(e.data.count),l.fillMessageList(e.data.data,1)}catch(e){}}catch(e){G.error("发布失败")}finally{s.value=!1}};return(e,l)=>{const n=u("el-tooltip"),g=u("el-input"),y=u("el-form-item"),w=u("el-option"),V=u("el-select"),x=u("el-radio"),C=u("el-radio-group"),L=u("el-checkbox"),E=u("el-checkbox-group"),F=u("el-form"),I=u("el-button"),M=u("el-dialog");return _(),h("div",Je,[i(n,{effect:"dark",content:"发布公告",placement:"bottom"},{default:o((()=>[d("i",{class:"iconfont icon-fabugonggao",onClick:f})])),_:1}),i(M,{modelValue:a.value,"onUpdate:modelValue":l[5]||(l[5]=e=>a.value=e),title:"发布公告",width:"600px","close-on-click-modal":!1,"destroy-on-close":!0},{footer:o((()=>[d("span",We,[i(I,{onClick:b},{default:o((()=>l[10]||(l[10]=[r("取消")]))),_:1}),i(I,{type:"primary",onClick:k,loading:s.value},{default:o((()=>l[11]||(l[11]=[r("确定")]))),_:1},8,["loading"])])])),default:o((()=>[i(F,{ref_key:"formRef",ref:t,model:p.value,rules:v.value,"label-width":"100px"},{default:o((()=>[i(y,{label:"公告名称",prop:"name"},{default:o((()=>[i(g,{modelValue:p.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>p.value.name=e),placeholder:"请输入公告名称",maxlength:"50","show-word-limit":""},null,8,["modelValue"])])),_:1}),i(y,{label:"公告类型",prop:"bulletin_type"},{default:o((()=>[i(V,{modelValue:p.value.bulletin_type,"onUpdate:modelValue":l[1]||(l[1]=e=>p.value.bulletin_type=e),placeholder:"请选择公告类型",style:{width:"100%"}},{default:o((()=>[(_(),h(S,null,U(m,(e=>i(w,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),i(y,{label:"公告形式",prop:"modality"},{default:o((()=>[i(C,{modelValue:p.value.modality,"onUpdate:modelValue":l[2]||(l[2]=e=>p.value.modality=e)},{default:o((()=>[i(x,{label:1},{default:o((()=>l[6]||(l[6]=[r("普通公告")]))),_:1}),i(x,{label:2},{default:o((()=>l[7]||(l[7]=[r("重要公告")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),i(y,{label:"谁不可见",prop:"visible"},{default:o((()=>[i(E,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=e=>c.value=e)},{default:o((()=>[i(L,{label:"1"},{default:o((()=>l[8]||(l[8]=[r("中台普通权限")]))),_:1}),i(L,{label:"2"},{default:o((()=>l[9]||(l[9]=[r("销售")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),i(y,{label:"公告描述",prop:"content"},{default:o((()=>[i(g,{modelValue:p.value.content,"onUpdate:modelValue":l[4]||(l[4]=e=>p.value.content=e),type:"textarea",rows:6,placeholder:"请输入公告描述",maxlength:"1200","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-0b16cbf6"]]),Xe={class:"message"},Ye={class:"message-list"},Ze=["onClick"],el={class:"message-content"},ll={class:"message-title"},al={class:"message-date"},tl=e({__name:"Message",setup(e){const l=ae(),a=O(),t=k(),s=n("first"),p=n(!1),v=n(!1),f=n(!1),g=n({name:"",modality:"",bulletin_type:"",content:"",created_name:"",created_at:""}),y={1:"公告通知",2:"功能上新",3:"数据记录",4:"任务消息"},w=async e=>{try{const a=await ne({page:1,page_size:5,is_read:1});l.updateUnreadMessage(a.data.count),V(a.data.data,1),p.value=e}catch(a){}},V=(e,a)=>{l.fillMessageList(e,a)},x=n(document.hasFocus()),C=n([]),F=()=>{v.value=!1,t.push("/message")},M=()=>{l.unreadMessage?v.value=!v.value:t.push("/message")},T=e=>{const l={id:Date.now(),instance:null,timeout:null};l.instance=R({message:z("span",{style:{color:"#b07cf7",cursor:"pointer"}},e.data.data.content),type:"info",duration:0,onClick:()=>{(e=>{const l=e.data.data.json_data.type||JSON.parse(e.data.data.json_data).type;1==l||2==l?t.push({path:1==l?"/business/task/informationAuthor":"/business/task/examineAuthor",query:{page_type:"add",id:e.data.data.json_data.task_id||JSON.parse(e.data.data.json_data).task_id}}):3==l?t.push({path:"/business/task",query:{task_id:e.data.data.json_data.task_id||JSON.parse(e.data.data.json_data).task_id}}):4==l&&t.push({path:"/business/task",query:{key:"6",order_id:e.data.data.json_data.order_id||JSON.parse(e.data.data.json_data).order_id}}),ue({id:e.data.data.id}).then((()=>{w(!1)}))})(e)}}),C.value.push(l),x.value&&A(l,5e3)},A=(e,l)=>{e.timeout&&clearTimeout(e.timeout),e.timeout=setTimeout((()=>{e.instance&&(e.instance.close(),q(e.id))}),l)},q=e=>{C.value=C.value.filter((l=>l.id!==e))},P=()=>{x.value=!0,C.value.forEach((e=>{e.timeout||A(e,5e3)}))},j=()=>{x.value=!1,C.value.forEach((e=>{e.timeout&&(clearTimeout(e.timeout),e.timeout=null)}))};L((()=>{window.addEventListener("focus",P),window.addEventListener("blur",j)})),I((()=>{window.removeEventListener("focus",P),window.removeEventListener("blur",j)})),E(a.messages,(e=>{let l=e[e.length-1];"receive_message"===l.event&&(T(l),w(!1))}),{deep:!0}),L((()=>{w(!1)})),L((()=>{document.addEventListener("click",(e=>{e.target.closest(".message")||(v.value=!1)}))})),I((()=>{document.removeEventListener("click",(()=>{}))}));const D=()=>{f.value=!1,w(!1)};return(e,a)=>{const n=u("el-badge"),p=u("el-tab-pane"),k=u("el-tabs"),V=u("el-popover"),x=u("el-form-item"),C=u("el-input"),L=u("el-form"),E=u("el-button"),I=u("el-drawer");return _(),h("div",Xe,[i(V,{visible:v.value,"onUpdate:visible":a[1]||(a[1]=e=>v.value=e),placement:"bottom",width:310,trigger:"manual"},{reference:o((()=>[d("div",{onClick:M},[i(n,{value:m(l).unreadMessage,class:"item"},{default:o((()=>[d("i",{class:c(["iconfont icon-xiaoxi","toolBar-icon"])})])),_:1},8,["value"])])])),default:o((()=>[d("span",{onClick:F,style:{position:"absolute",top:"22px",right:"20px","font-size":"14px","z-index":"2",color:"#1860ce",cursor:"pointer"}},"全部消息"),i(k,{modelValue:s.value,"onUpdate:modelValue":a[0]||(a[0]=e=>s.value=e)},{default:o((()=>[i(p,{label:"未读消息",name:"first"},{default:o((()=>[d("div",Ye,[(_(!0),h(S,null,U(m(l).messageList,((e,l)=>(_(),h("div",{class:"message-item",onClick:l=>(e=>{if(4==e.type){let l=e.json_data.type||JSON.parse(e.json_data).type;1==l||2==l?t.push({path:1==l?"/business/task/informationAuthor":"/business/task/examineAuthor",query:{page_type:"add",id:e.json_data.task_id||JSON.parse(e.json_data).task_id}}):3==l?t.push({path:"/business/task"}):4==l&&t.push({path:"/business/task",query:{order_id:e.json_data.order_id||JSON.parse(e.json_data).order_id}})}else f.value=!0,g.value={name:e.name,modality:1===e.modality?"普通公告":"重要公告",bulletin_type:y[e.type],content:e.content,created_name:e.created_name,created_at:e.created_at},v.value=!1;ue({id:e.id}).then((()=>{w(!1)}))})(e),key:l},[a[6]||(a[6]=d("img",{src:se,alt:"",class:"message-icon"},null,-1)),d("div",el,[d("span",ll,b(e.name),1),d("span",al,b(e.content),1)])],8,Ze)))),128))])])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["visible"]),i(I,{modelValue:f.value,"onUpdate:modelValue":a[5]||(a[5]=e=>f.value=e),title:"公告详情",direction:"rtl",onClose:D},{footer:o((()=>[i(E,{onClick:D},{default:o((()=>a[7]||(a[7]=[r("返回")]))),_:1})])),default:o((()=>[i(L,{model:g.value,"label-width":"100px"},{default:o((()=>[i(x,{label:"公告名称"},{default:o((()=>[r(b(g.value.name),1)])),_:1}),i(x,{label:"公告类型"},{default:o((()=>[r(b(g.value.bulletin_type),1)])),_:1}),i(x,{label:"公告形式"},{default:o((()=>[r(b(g.value.modality),1)])),_:1}),i(x,{label:"公告描述"},{default:o((()=>[i(C,{modelValue:g.value.content,"onUpdate:modelValue":a[2]||(a[2]=e=>g.value.content=e),type:"textarea",rows:10,disabled:"",maxlength:"1200","show-word-limit":""},null,8,["modelValue"])])),_:1}),i(x,{label:"发布人"},{default:o((()=>[i(C,{modelValue:g.value.created_name,"onUpdate:modelValue":a[3]||(a[3]=e=>g.value.created_name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(x,{label:"发布时间"},{default:o((()=>[i(C,{modelValue:g.value.created_at,"onUpdate:modelValue":a[4]||(a[4]=e=>g.value.created_at=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}),nl=Y(tl,[["__scopeId","data-v-21816f48"]]),sl=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],ul=(()=>{if("undefined"==typeof document)return!1;const e=sl[0],l={};for(const a of sl){if((null==a?void 0:a[1])in document){for(const[t,n]of a.entries())l[e[t]]=n;return l}}return!1})(),ol={change:ul.fullscreenchange,error:ul.fullscreenerror};let il={request:(e=document.documentElement,l)=>new Promise(((a,t)=>{const n=()=>{il.off("change",n),a()};il.on("change",n);const s=e[ul.requestFullscreen](l);s instanceof Promise&&s.then(n).catch(t)})),exit:()=>new Promise(((e,l)=>{if(!il.isFullscreen)return void e();const a=()=>{il.off("change",a),e()};il.on("change",a);const t=document[ul.exitFullscreen]();t instanceof Promise&&t.then(a).catch(l)})),toggle:(e,l)=>il.isFullscreen?il.exit():il.request(e,l),onchange(e){il.on("change",e)},onerror(e){il.on("error",e)},on(e,l){const a=ol[e];a&&document.addEventListener(a,l,!1)},off(e,l){const a=ol[e];a&&document.removeEventListener(a,l,!1)},raw:ul};Object.defineProperties(il,{isFullscreen:{get:()=>Boolean(document[ul.fullscreenElement])},element:{enumerable:!0,get:()=>document[ul.fullscreenElement]??void 0},isEnabled:{enumerable:!0,get:()=>Boolean(document[ul.fullscreenEnabled])}}),ul||(il={isEnabled:!1});const dl={class:"fullscreen"},rl=e({__name:"Fullscreen",setup(e){const l=n(il.isFullscreen);L((()=>{il.on("change",(()=>{il.isFullscreen?l.value=!0:l.value=!1}))}));const a=()=>{il.isEnabled||G.warning("当前您的浏览器不支持全屏 ❌"),il.toggle()};return(e,t)=>(_(),h("div",dl,[d("i",{class:c([["iconfont",l.value?"icon-suoxiao":"icon-fangda"],"toolBar-icon"]),onClick:a},null,2)]))}}),cl={class:"dialog-footer"},ml=e({__name:"InfoDialog",setup(e,{expose:l}){const a=k(),t=n({old_password:"",new_password:"",username:JSON.parse(window.localStorage.getItem("geeker-user")).userInfo.name}),c=n(!1),m=()=>{oe(t.value).then((e=>{delete t.value.username,990===(null==e?void 0:e.code)&&(G({message:"修改成功",type:"success"}),localStorage.clear(),a.push("/login"),c.value=!1)}))};return l({openDialog:()=>{c.value=!0}}),(e,l)=>{const a=u("el-input"),n=u("el-form-item"),p=u("el-form"),v=u("el-button"),f=u("el-dialog");return _(),s(f,{modelValue:c.value,"onUpdate:modelValue":l[4]||(l[4]=e=>c.value=e),title:"个人信息",width:"500px",draggable:""},{footer:o((()=>[d("span",cl,[i(v,{onClick:l[3]||(l[3]=e=>c.value=!1)},{default:o((()=>l[5]||(l[5]=[r("取消")]))),_:1}),i(v,{type:"primary",onClick:m},{default:o((()=>l[6]||(l[6]=[r("确认")]))),_:1})])])),default:o((()=>[i(p,{model:t.value,"label-width":"auto",style:{"max-width":"600px"}},{default:o((()=>[i(n,{label:"用户名："},{default:o((()=>[i(a,{modelValue:t.value.username,"onUpdate:modelValue":l[0]||(l[0]=e=>t.value.username=e),disabled:!0},null,8,["modelValue"])])),_:1}),i(n,{label:"旧密码："},{default:o((()=>[i(a,{modelValue:t.value.old_password,"onUpdate:modelValue":l[1]||(l[1]=e=>t.value.old_password=e)},null,8,["modelValue"])])),_:1}),i(n,{label:"新密码："},{default:o((()=>[i(a,{modelValue:t.value.new_password,"onUpdate:modelValue":l[2]||(l[2]=e=>t.value.new_password=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),pl={class:"dialog-footer"},vl=e({__name:"PasswordDialog",setup(e,{expose:l}){const a=n(!1);return l({openDialog:()=>{a.value=!0}}),(e,l)=>{const t=u("el-button"),n=u("el-dialog");return _(),s(n,{modelValue:a.value,"onUpdate:modelValue":l[2]||(l[2]=e=>a.value=e),title:"修改密码",width:"500px",draggable:""},{footer:o((()=>[d("span",pl,[i(t,{onClick:l[0]||(l[0]=e=>a.value=!1)},{default:o((()=>l[3]||(l[3]=[r("取消")]))),_:1}),i(t,{type:"primary",onClick:l[1]||(l[1]=e=>a.value=!1)},{default:o((()=>l[4]||(l[4]=[r("确认")]))),_:1})])])),default:o((()=>[l[5]||(l[5]=d("span",null,"This is Password",-1))])),_:1},8,["modelValue"])}}}),fl=Y(e({__name:"Avatar",setup(e){const l=k(),a=$(),t=()=>{N.confirm("您是否确认退出登录?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{await H(),a.setToken(""),l.replace(K),G.success("退出登录成功！")}))},s=n(null),c=n(null);return(e,l)=>{const a=u("User"),n=u("el-icon"),m=u("el-dropdown-item"),p=u("SwitchButton"),v=u("el-dropdown-menu"),f=u("el-dropdown");return _(),h(S,null,[i(f,{trigger:"click"},{dropdown:o((()=>[i(v,null,{default:o((()=>[i(m,{onClick:l[0]||(l[0]=e=>{var l;null==(l=s.value)||l.openDialog()})},{default:o((()=>[i(n,null,{default:o((()=>[i(a)])),_:1}),r(b(e.$t("header.personalData")),1)])),_:1}),i(m,{divided:"",onClick:t},{default:o((()=>[i(n,null,{default:o((()=>[i(p)])),_:1}),r(b(e.$t("header.logout")),1)])),_:1})])),_:1})])),default:o((()=>[l[1]||(l[1]=d("div",{class:"avatar"},[d("img",{src:"/assets/gif/avatar-Dcbh69co.gif",alt:"avatar"})],-1))])),_:1}),i(ml,{ref_key:"infoRef",ref:s},null,512),i(vl,{ref_key:"passwordRef",ref:c},null,512)],64)}}}),[["__scopeId","data-v-1e432f17"]]),_l={class:"tool-bar-ri"},hl={class:"header-icon"},bl={class:"username"},gl=Y(e({__name:"ToolBarRight",setup(e){const l=$(),a=C((()=>l.userInfo.name));return(e,l)=>{const t=J("permission");return _(),h("div",_l,[d("div",hl,[i(Ne,{id:"searchMenu"}),i(Ke,{id:"themeSetting"}),M(i(Qe,{id:"publishBulletin"},null,512),[[t,"system:home:gonggao"]]),i(nl,{id:"message"}),i(rl,{id:"fullscreen"})]),d("span",bl,b(a.value),1),i(fl)])}}}),[["__scopeId","data-v-7b9dea48"]]),kl=Y(e({__name:"SubMenu",props:{menuList:{}},setup(e){const l=k(),a=e=>e.some((e=>{var l;return 1===e.meta.is_menu||!!(null==(l=e.children)?void 0:l.length)&&a(e.children)}));return(e,t)=>{const n=u("el-icon"),r=u("SubMenu",!0),c=u("el-sub-menu"),m=u("el-menu-item");return _(!0),h(S,null,U(e.menuList,(e=>{var t,u,v,f;return _(),h(S,{key:e.path},[(null==(t=e.children)?void 0:t.length)&&1===e.meta.is_menu&&a(e.children)?(_(),s(c,{key:0,index:e.path,disabled:null==(u=e.meta)?void 0:u.disabled},{title:o((()=>[i(n,null,{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024),d("span",null,b(e.meta.title),1)])),default:o((()=>[i(r,{menuList:e.children},null,8,["menuList"])])),_:2},1032,["index","disabled"])):p("",!0),(null==(v=e.children)?void 0:v.length)&&a(e.children)||1!==e.meta.is_menu?p("",!0):(_(),s(m,{key:1,index:e.path,disabled:null==(f=e.meta)?void 0:f.disabled,onClick:a=>(e=>{if(e.meta.isLink)return window.open(e.meta.isLink,"_blank");l.push(e.path)})(e)},{title:o((()=>[d("span",null,b(e.meta.title),1)])),default:o((()=>[i(n,null,{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024)])),_:2},1032,["index","disabled","onClick"]))],64)})),128)}}}),[["__scopeId","data-v-f063e101"]]),yl={class:"logo flx-center"},wl=e({name:"layoutVertical"}),Vl=Y(e({...wl,setup(e){const l=g(),t=x(),n=a(),r=C((()=>n.accordion)),c=C((()=>n.isCollapse)),p=C((()=>t.showMenuListGet)),v=C((()=>l.meta.activeMenu?l.meta.activeMenu:l.path));return(e,l)=>{const a=u("el-menu"),t=u("el-scrollbar"),n=u("el-aside"),f=u("el-header"),h=u("el-container");return _(),s(h,{class:"layout"},{default:o((()=>[i(n,null,{default:o((()=>[d("div",{class:"aside-box",style:W({width:c.value?"65px":"210px"})},[d("div",yl,[l[0]||(l[0]=d("img",{class:"logo-img",src:Le,alt:"logo"},null,-1)),M(d("span",{class:"logo-text"},b(m(undefined)),513),[[T,!c.value]])]),i(t,null,{default:o((()=>[i(a,{router:!1,"default-active":v.value,collapse:c.value,"unique-opened":r.value,"collapse-transition":!1},{default:o((()=>[i(kl,{"menu-list":p.value},null,8,["menu-list"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})],4)])),_:1}),i(h,null,{default:o((()=>[i(f,null,{default:o((()=>[i(Be),i(gl)])),_:1}),i(qe)])),_:1})])),_:1})}}}),[["__scopeId","data-v-6e54db0e"]]),xl={class:"header-lf mask-image"},Cl={class:"logo flx-center"},Ll={class:"logo-text"},El={class:"header-ri"},Sl=e({name:"layoutClassic"}),Ul=Y(e({...Sl,setup(e){const l=g(),t=x(),n=a(),r=C((()=>n.accordion)),c=C((()=>n.isCollapse)),p=C((()=>t.showMenuListGet)),v=C((()=>l.meta.activeMenu?l.meta.activeMenu:l.path));return(e,l)=>{const a=u("el-header"),t=u("el-menu"),n=u("el-scrollbar"),f=u("el-aside"),h=u("el-container");return _(),s(h,{class:"layout"},{default:o((()=>[i(a,null,{default:o((()=>[d("div",xl,[d("div",Cl,[l[0]||(l[0]=d("img",{class:"logo-img",src:Le,style:{width:"90px"},alt:"logo"},null,-1)),d("span",Ll,b(m(undefined)),1)]),i(Be)]),d("div",El,[i(gl)])])),_:1}),i(h,{class:"classic-content"},{default:o((()=>[i(f,null,{default:o((()=>[d("div",{class:"aside-box",style:W({width:c.value?"65px":"210px"})},[i(n,null,{default:o((()=>[i(t,{router:!1,"default-active":v.value,collapse:c.value,"unique-opened":r.value,"collapse-transition":!1},{default:o((()=>[i(kl,{"menu-list":p.value},null,8,["menu-list"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})],4)])),_:1}),i(h,{class:"classic-main"},{default:o((()=>[i(qe)])),_:1})])),_:1})])),_:1})}}}),[["__scopeId","data-v-483404bb"]]),Fl={class:"logo flx-center"},Il={class:"logo-text"},Ml=e({name:"layoutTransverse"}),Tl=Y(e({...Ml,setup(e){const l=g(),a=k(),t=x(),n=C((()=>t.showMenuListGet)),r=C((()=>l.meta.activeMenu?l.meta.activeMenu:l.path));return(e,l)=>{const t=u("el-icon"),c=u("el-sub-menu"),p=u("el-menu-item"),v=u("el-menu"),f=u("el-header"),g=u("el-container");return _(),s(g,{class:"layout"},{default:o((()=>[i(f,null,{default:o((()=>[d("div",Fl,[l[0]||(l[0]=d("img",{class:"logo-img",src:Le,alt:"logo"},null,-1)),d("span",Il,b(m(undefined)),1)]),i(v,{mode:"horizontal",router:!1,"default-active":r.value},{default:o((()=>[(_(!0),h(S,null,U(n.value,(e=>{var l;return _(),h(S,{key:e.path},[(null==(l=e.children)?void 0:l.length)?(_(),s(c,{key:e.path,index:e.path+"el-sub-menu"},{title:o((()=>[i(t,null,{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024),d("span",null,b(e.meta.title),1)])),default:o((()=>[i(kl,{"menu-list":e.children},null,8,["menu-list"])])),_:2},1032,["index"])):(_(),s(p,{key:e.path+"el-menu-item",index:e.path,onClick:l=>(e=>{if(e.meta.isLink)return window.open(e.meta.isLink,"_blank");a.push(e.path)})(e)},{title:o((()=>[d("span",null,b(e.meta.title),1)])),default:o((()=>[i(t,null,{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024)])),_:2},1032,["index","onClick"]))],64)})),128))])),_:1},8,["default-active"]),i(gl)])),_:1}),i(qe)])),_:1})}}}),[["__scopeId","data-v-73159b76"]]),Al={class:"aside-split"},ql={class:"split-list"},zl=["onClick"],Pl={class:"title"},jl={class:"logo flx-center"},Dl=e({name:"layoutColumns"}),Bl=Y(e({...Dl,setup(e){const l=g(),t=k(),r=x(),p=a(),v=C((()=>p.accordion)),f=C((()=>p.isCollapse)),y=C((()=>r.showMenuListGet)),w=C((()=>l.meta.activeMenu?l.meta.activeMenu:l.path)),V=n([]),L=n("");E((()=>[y,l]),(()=>{var e;if(!y.value.length)return;L.value=l.path;const a=y.value.filter((e=>l.path===e.path||`/${l.path.split("/")[1]}`===e.path));if(null==(e=a[0].children)?void 0:e.length)return V.value=a[0].children;V.value=[]}),{deep:!0,immediate:!0});return(e,l)=>{const a=u("el-icon"),n=u("el-scrollbar"),r=u("el-menu"),p=u("el-aside"),g=u("el-header"),k=u("el-container");return _(),s(k,{class:"layout"},{default:o((()=>[d("div",Al,[l[0]||(l[0]=d("div",{class:"logo flx-center"},[d("img",{class:"logo-img",src:Le,style:{width:"100%"},alt:"logo"})],-1)),i(n,null,{default:o((()=>[d("div",ql,[(_(!0),h(S,null,U(y.value,(e=>(_(),h("div",{key:e.path,class:c(["split-item",{"split-active":L.value===e.path||`/${L.value.split("/")[1]}`===e.path}]),onClick:l=>(e=>{var l;if(L.value=e.path,null==(l=e.children)?void 0:l.length)return V.value=e.children;V.value=[],t.push(e.path)})(e)},[i(a,null,{default:o((()=>[(_(),s(F(e.meta.icon)))])),_:2},1024),d("span",Pl,b(e.meta.title),1)],10,zl)))),128))])])),_:1})]),i(p,{class:c({"not-aside":!V.value.length}),style:W({width:f.value?"65px":"210px"})},{default:o((()=>[d("div",jl,[M(d("span",{class:"logo-text"},b(f.value?"G":m(undefined)),513),[[T,V.value.length]])]),i(n,null,{default:o((()=>[i(r,{router:!1,"default-active":w.value,collapse:f.value,"unique-opened":v.value,"collapse-transition":!1},{default:o((()=>[i(kl,{"menu-list":V.value},null,8,["menu-list"])])),_:1},8,["default-active","collapse","unique-opened"])])),_:1})])),_:1},8,["class","style"]),i(k,null,{default:o((()=>[i(g,null,{default:o((()=>[i(Be),i(gl)])),_:1}),i(qe)])),_:1})])),_:1})}}}),[["__scopeId","data-v-d7ddaf61"]]),Gl=e({name:"layout"}),Ol=Y(e({...Gl,setup(e){const l={vertical:Vl,classic:Ul,transverse:Tl,columns:Bl},t=a(),n=C((()=>t.isDark)),d=C((()=>t.layout)),r=C((()=>t.watermark)),c=Q({color:"rgba(0, 0, 0, .15)"});return E(n,(()=>c.color=n.value?"rgba(255, 255, 255, .15)":"rgba(0, 0, 0, .15)"),{immediate:!0}),(e,a)=>{const t=u("el-watermark");return _(),s(t,{id:"watermark",font:c,content:r.value?["Geeker Admin","Happy Working"]:""},{default:o((()=>[(_(),s(F(l[d.value]))),i(Ce)])),_:1},8,["font","content"])}}}),[["__scopeId","data-v-fc528996"]]);export{Ol as default};
