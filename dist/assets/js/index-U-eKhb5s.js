import{s as e,H as l,r as a,a6 as o,a as r,c as t,o as i,b as s,w as p,y as n,aj as c,d as u,h as _,Z as b,ai as m,E as d}from"./index-C2bfFjZ1.js";import{_ as f}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-BSD3Vs5K.js";import v from"./detail-6rIprm1h.js";import{m as y,a as k,b as x,c as g}from"./kol-BVgENpvM.js";import"./index-BMv7HewC.js";import"./_plugin-vue_export-helper-BXFjo1rG.js";import"./sortable.esm-DeWNWKFU.js";const w={class:"table-box"},h={style:{"text-align":"right",margin:"0"}},C=e({...e({name:"useProTable"}),setup(e){l();const C=a();o({type:1});const j=o([{prop:"mcn_attribute",label:"属性",search:{el:"input"}},{prop:"mcn_short_name",label:"机构简称",search:{el:"input"}},{prop:"type",label:"类型"},{prop:"external_contracting_parties",label:"对外签约主体"},{prop:"office_location",label:"办公地点"},{prop:"internal_contact_person",label:"集采对接人"},{prop:"supplier_contact_person",label:"供应商联系人"},{prop:"history_collaboration_reference",label:"引力历史合作参考"},{prop:"service_team_members",label:"服务团队人数"},{prop:"outsourced_execution_service",label:"外包执行服务类目/明细"},{prop:"outsourced_execution_price",label:"执行外包参考报价"},{prop:"outsourced_execution_balance",label:"执行外包结算账期"},{prop:"supplement_policy",label:"补充说明"},{prop:"updated_at",label:"信息更新时间"},{prop:"operation",label:"操作",fixed:"right",width:200}]),T=a(null),L=(e,l={})=>{var a,o;const r={title:e,isView:"查看"===e,row:{...l},api:"编辑"===e?k:x,getTableList:null==(a=C.value)?void 0:a.getTableList};null==(o=T.value)||o.acceptParams(r)};return(e,l)=>{const a=r("el-button"),o=r("el-popover");return i(),t("div",w,[s(f,{ref_key:"proTable",ref:C,columns:j,"request-api":n(y)},{tableHeader:p((e=>[s(a,{type:"primary",icon:n(m),plain:"",onClick:l=>L("新增",e.row)},{default:p((()=>l[0]||(l[0]=[u("新增机构")]))),_:2},1032,["icon","onClick"])])),operation:p((e=>[s(a,{type:"primary",link:"",icon:n(c),onClick:l=>L("编辑",e.row)},{default:p((()=>l[1]||(l[1]=[u("编辑")]))),_:2},1032,["icon","onClick"]),s(o,{visible:1==e.row.visible,placement:"top"},{reference:p((()=>[s(a,{type:"primary",link:"",icon:n(b),onClick:l=>e.row.visible=!0},{default:p((()=>l[4]||(l[4]=[u("删除")]))),_:2},1032,["icon","onClick"])])),default:p((()=>[l[5]||(l[5]=_("p",null,"请确认是否删除该机构",-1)),_("div",h,[s(a,{size:"small",text:"",onClick:l=>e.row.visible=!1},{default:p((()=>l[2]||(l[2]=[u("否")]))),_:2},1032,["onClick"]),s(a,{size:"small",type:"primary",onClick:l=>(e=>{const l=e.$index;tableData.value.list[l].visible=!1,g({id:e.row.id}).then((e=>{var l;d.success(e.msg),null==(l=C.value)||l.getTableList()}))})(e)},{default:p((()=>l[3]||(l[3]=[u("是")]))),_:2},1032,["onClick"])])])),_:2},1032,["visible"])])),_:1},8,["columns","request-api"]),s(v,{ref_key:"drawerRef",ref:T},null,512)])}}});export{C as default};
