import{k as t,a as e,c as a,o as _,h as o,b as s,w as i,d as r,t as n}from"./index-C2bfFjZ1.js";import{d}from"./index-WDAssdXv.js";import{_ as l}from"./_plugin-vue_export-helper-BXFjo1rG.js";const c=e=>t.post("/dandelionCossDomainExportNew",e);d().tabs;const m=[{label:"跨域项目名称",search:!0,prop:"third_project_name",width:200},{label:"操作",prop:"daren",slot:!0,width:200},{label:"跨域项目ID",search:!0,width:280,prop:"third_project_id"},{label:"笔记数",width:200,prop:"note_id_count"},{label:"任务开始日期",prop:"brief_start_time"},{label:"任务结束日期",prop:"brief_end_time"},{label:"京东合作笔记阅读人数",prop:"jd_read_num_sum"},{label:"京东合作笔记点赞人数",prop:"jd_fav_num_sum"},{label:"京东合作笔记评论人数",prop:"jd_like_num_sum"},{label:"京东合作笔记评论人数",prop:"jd_cmt_num_sum"},{label:"京东合作笔记分享人数",prop:"jd_share_num_sum"},{label:"抽样比例",prop:"third_ads_income_sum",slot:!0},{label:"蒲公英实际支付金额",prop:"third_bcoo_income_sum",slot:!0},{label:"总金额",prop:"third_total_income_sum",slot:!0},{label:"站外转化行为uv（归因周期：30）",prop:"jd_active_num_sum",slot:!0},{label:"站外转化率（归因周期：30）",prop:"jd_active_ratio_sum",slot:!0},{label:"站外转化成本（归因周期：30）",prop:"jd_active_cost_sum",slot:!0},{label:"站外转化行为uv（归因周期：15）",prop:"jd_active_num_15d_sum",slot:!0},{label:"站外转化率（归因周期：15）",prop:"jd_active_ratio_15d_sum",slot:!0},{label:"站外转化成本（归因周期：15）",prop:"jd_active_cost_15d_sum",slot:!0},{label:"开始日期",hide:!0,search:!0,type:"daterange",align:"center",searchRange:!0,format:"YYYY-MM-DD",span:24,valueFormat:"YYYY-MM-DD",startPlaceholder:"日期开始",endPlaceholder:"日期结束",prop:"created_at"}],u={class:"card content-box"},p={style:{height:"90%"}},h=["onClick"],g={key:1};const v=l({data:()=>({page:{currentPage:1,pageSize:10,total:0},form:{},dandelionTaskNoteExport:c,params:{},loading:!1,data:[],formFourthInlineNew:{note_source:"",note_title:"",note_id:"",spu_name:"",blogger_nickname:"",content_tags:"",report_brand:"",customer_name:"",cooperation_name:"",order_id:"",task_group_id:"",star_task_main_task_id:"",cross_domain_project_id:"",created_at:{start_time:"",end_time:""}},activeName:"1",option:{index:!1,gridBtn:!1,addBtn:!1,searchLabelWidth:100,align:"center",headerAlign:"center",border:!0,menu:!1,searchEnter:!0,dialogDrag:!0,searchSpan:6,stripe:!0,menuType:"text",searchMenuPosition:"right",searchIcon:!0,column:m},contentTagObj:{0:"单品",1:"合集-默认",2:"合集-OOTD",3:"合集-好物分享",4:"合集-PLOG/VLOG",5:"合集-开箱",6:"合集-测评",7:"合集-教程"},originalObj:{0:"定制合作",1:"共创合作",2:"招募合作",3:"新芽合作",4:"明星合作"},noteTypeObj:{0:"图文",1:"视频"}}),methods:{checkNote(t,e){this.$emit("checkNote",{third_project_id:t.third_project_id},"third_project_id")},getList(){this.loading=!0;const e=Object.assign({page:this.page.currentPage,page_size:this.page.pageSize,cooperate_type:"",note_title:"",note_id:"",spu_name:"",kol_nick_name:"",brand_user_id:"",order_id:"",star_event_group_id:"",brief_id:"",star_event_group_id:"",note_publish_time_start:"",note_publish_time_end:"",brand_user_name:"",third_project_id:""},this.params);this.data=[],(e=>t.post("/kolDandelionCossDomainNew",e))(e).then((t=>{const e=t.data;this.loading=!1,this.page.total=e.count;const a=e.lists;this.data=a,this.kolDandelionCossDomainListTotalFunNew()}))},kolDandelionCossDomainListTotalFunNew(){(e=>t.post("/kolDandelionCossDomainTotalNew",e))(this.formFourthInlineNew).then((t=>{let e=t.data.data;this.data.unshift({third_project_name:"--",third_project_id:"--",brief_start_time:"--",brief_end_time:"--",note_id_count:e.total_note_id_count,third_ads_income_sum:e.total_ads_income_sum,jd_active_cost_15d_sum:e.total_jd_active_cost_15d_sum,third_bcoo_income_sum:e.total_third_bcoo_income_sum,third_total_income_sum:e.total_third_total_income_sum,jd_active_num_sum:e.total_jd_active_num_sum,jd_active_ratio_sum:e.total_jd_active_ratio_sum,jdA_active_cost_sum:e.total_jdA_active_cost_sum,jd_read_num_sum:e.total_jd_read_num_sum,jd_like_num_sum:e.total_jd_like_num_sum,jd_fav_num_sum:e.total_jd_fav_num_sum,jd_cmt_num_sum:e.total_jd_cmt_num_sum,jd_share_num_sum:e.total_jd_share_num_sum,jd_active_num_15d_sum:e.total_jd_active_num_15d_sum,jd_active_ratio_15d_sum:e.total_jd_active_ratio_15d_sum,jd_active_cost_15d_sum:e.total_jd_active_cost_15d_sum})}))},rowSave(t,e){this.loading=!0,e(t).then((t=>{this.loading=!1,990==t.code&&this.$message.success("导出成功")}))},rowUpdate(e,a,_,o){var s;(s=Object.assign({updateUser:this.userInfo.name},e),t.post("/sys/departmentsUpdate",s)).then((()=>{this.$message.success("修改成功"),_(),this.getList()})).catch((()=>{o()}))},rowDel(e){this.$confirm("此操作将永久删除, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{return a=e.id,t.post("/sys/departmentsUpdate",a);var a})).then((()=>{this.$message.success("删除成功"),this.getList()}))},searchChange(t,e){e&&e(),t.created_at&&t.created_at.length>0&&(t.created_at={end_time:t.created_at[1],start_time:t.created_at[0]}),this.params=t,this.page.currentPage=1,this.getList()},refreshChange(){this.getList(),this.$message.success("刷新成功")}}},[["render",function(t,d,l,c,m,v){const j=e("el-button"),b=e("avue-crud");return _(),a("div",u,[o("div",p,[s(b,{ref:"crud",option:m.option,page:m.page,"onUpdate:page":d[1]||(d[1]=t=>m.page=t),"table-loading":m.loading,onOnLoad:v.getList,onRowUpdate:v.rowUpdate,onRowDel:v.rowDel,onRefreshChange:v.refreshChange,onSearchReset:v.searchChange,onSearchChange:v.searchChange,modelValue:m.form,"onUpdate:modelValue":d[2]||(d[2]=t=>m.form=t),data:m.data},{daren:i((t=>[0!==(null==t?void 0:t.index)?(_(),a("span",{key:0,class:"text-blue-500 cursor-pointer",onClick:e=>v.checkNote(t.row,"order_id")}," 查看笔记 ",8,h)):(_(),a("span",g,"合计"))])),third_ads_income_sum:i((t=>{var e,a;return[r(n((null==(e=t.row)?void 0:e.third_ads_income_sum)?"￥"+(null==(a=t.row)?void 0:a.third_ads_income_sum):"--"),1)]})),third_total_income_sum:i((t=>{var e,a;return[r(n((null==(e=t.row)?void 0:e.third_total_income_sum)?"￥"+(null==(a=t.row)?void 0:a.third_total_income_sum):"--"),1)]})),jd_active_ratio_sum:i((t=>{var e,a;return[r(n((null==(e=t.row)?void 0:e.jd_active_ratio_sum)?"%"+(null==(a=t.row)?void 0:a.jd_active_ratio_sum):"--"),1)]})),jd_active_ratio_15d_sum:i((t=>{var e,a;return[r(n((null==(e=t.row)?void 0:e.jd_active_ratio_15d_sum)?"￥"+(null==(a=t.row)?void 0:a.jd_active_ratio_15d_sum):"--"),1)]})),jd_active_cost_15d_sum:i((t=>{var e,a;return[r(n((null==(e=t.row)?void 0:e.jd_active_cost_15d_sum)?"￥"+(null==(a=t.row)?void 0:a.jd_active_cost_15d_sum):"--"),1)]})),"menu-left":i((()=>[s(j,{type:"primary",icon:"Download",onClick:d[0]||(d[0]=t=>v.rowSave(m.params,m.dandelionTaskNoteExport))},{default:i((()=>d[3]||(d[3]=[r("导出")]))),_:1})])),_:1},8,["option","page","table-loading","onOnLoad","onRowUpdate","onRowDel","onRefreshChange","onSearchReset","onSearchChange","modelValue","data"])])])}],["__scopeId","data-v-21fca980"]]);export{v as default};
