import{s as e,u as t,H as a,r,e as n,a as i,c as l,o,h as s,z as c,b as d,w as u,y as v,N as g,g as _,aN as p,a0 as m,n as y,d as f,t as w,E as C,q as k,aM as S,K as h}from"./index-BE6Fh1xm.js";import{u as b}from"./tabs-CGQc3OAd.js";import N from"./planInformation-D1u2DP6m.js";import x from"./unit-DqdQtMTP.js";import O from"./target-CXco6UTx.js";import L from"./create-D-BXUTEd.js";import J from"./confirm-C8mS-9YT.js";import{d as T}from"./temp-api-B36KNeYM.js";import{_ as j}from"./_plugin-vue_export-helper-GSmkUi5K.js";import"./time-ChyUUY53.js";import"./AnchorNavigation-DOB1pEjt.js";import"./setupRequest-C4Opp8Oc.js";import"./lodash-BOiQUzk8.js";import"./DetailDrawer-bj4Na5-S.js";import"./planInformation-Bw2KfoAK.js";import"./unit-CQ4ZjND0.js";import"./create-9Fyc66dN.js";const A={class:"page-container"},D={class:"sidebar"},B={class:"level-icon"},F={class:"main-content"},U={class:"content-wrapper"},q={class:"navigation-buttons"},I=j(e({__name:"index",setup(e,{expose:j}){const I=t(),R=b(),K=a(),M=r(0),P=r(null),E=r("plan");r([]);const z=r([]),H=r({noteList:[],targetCards:[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}],bidForm:{bid:.1,smartBid:!1,minBid:.1,maxBid:100}}),$=r([]),V=r([{plan:{advertiser_id:null,marketing_target:null,campaign_name:null,placement:1,promotion_target:1,time_type:0,start_time:null,expire_time:null,enable:0,time_period:{mon:"",tues:"",wed:"",thur:"",fri:"",sat:"",sun:""},bidding_strategies:[{bidding_strategy:null,limit_day_budget:0,campaign_day_budget:0,time_period_type:null,optimize_target:null,constraint_type:null,intelligent_expansion:null,smart_switch:null,pacing_mode:null}]},unit:{advertiser_id:null,unit_name:"",event_bid:100,note_ids:[],promotion_target:1,target_type:1,target_config:{target_gender:"all",target_age:"all",target_city:"",target_device:"all",industry_interest_target:{content_interests:[],shopping_interests:[]},crowd_target:{crowd_pkg:[],dmp_permission:!0},keywords:[],interest_keywords:[],intelligent_expansion:null},keyword_target_period:15,keyword_target_action:[1,2,3]},create:{advertiser_id:null,creativity_name:"",note_id:"",click_urls:[],expo_urls:[],conversion_type:0,jump_url:"",landing_page_type:1,bar_content:"",conversion_component_types:[0],custom_mask:1,custom_title:0,title_fills:[],mask_gen:1,title_gen:1}}]),G=[],Q=e=>{if("unit"===E.value)V.value[0].unit={...V.value[0].unit,...e.unit};else{if(e.plan){const t=void 0!==e.plan.time_type?"string"==typeof e.plan.time_type?parseInt(e.plan.time_type):e.plan.time_type:V.value[0].plan.time_type;V.value[0].plan={...V.value[0].plan,...e.plan,time_type:t,start_time:0===t?"":e.plan.start_time||V.value[0].plan.start_time,expire_time:0===t?"":e.plan.expire_time||V.value[0].plan.expire_time}}if(e.unit&&(V.value[0].unit={...V.value[0].unit,...e.unit}),e.create&&e.create&&"object"==typeof e.create){const t=e.create.creativeList&&Array.isArray(e.create.creativeList);V.value[0].create={advertiser_id:e.create.advertiser_id||0,creativity_name:e.create.creativity_name||"",note_id:e.create.note_id||"",click_urls:[],expo_urls:[],conversion_type:0,jump_url:"",landing_page_type:1,bar_content:"立即参与",conversion_component_types:[0],custom_mask:1,custom_title:0,title_fills:[],mask_gen:1,title_gen:1,creativeList:t?e.create.creativeList:[]},t&&($.value=e.create.creativeList)}}},W=e=>{if((e.noteList||e.notes)&&(H.value.noteList=[...e.notes||e.noteList||[]]),e.targetCards||e.target_cards){const t=(e.targetCards||e.target_cards||[]).map((e=>{const t={...e};return t.targetPackage=t.targetPackage||"",t.targetType=Number(t.targetType)||1,t.gender=t.gender||["all"],t.location=t.location||["all"],t.ageRanges=t.ageRanges||["all"],t.platform=t.platform||["all"],t.locationMode=t.locationMode||"country",t.selectedLocations=t.selectedLocations||[],t.selectedCities=t.selectedCities||{},t.locationFilter=t.locationFilter||"",t.crowds=t.crowds||[],t.selectedDistricts=t.selectedDistricts||{},t.selectedNodes=t.selectedNodes||[],t.selectedCrowds=t.selectedCrowds||[],t.selectedKeywords=t.selectedKeywords||[],t.crowd_target?t.crowd_target.crowd_pkg||(t.crowd_target.crowd_pkg=[]):t.crowd_target={crowd_pkg:[]},t}));H.value.targetCards=t}e.bid_form&&(H.value.bidForm={...e.bid_form});const t=V.value[0].unit,a={...t,advertiser_id:e.advertiser_id||t.advertiser_id,unit_name:e.unit_name||t.unit_name,event_bid:e.event_bid||t.event_bid,note_ids:e.note_ids||t.note_ids,promotion_target:e.promotion_target||t.promotion_target,target_type:e.target_type||t.target_type,keyword_target_period:e.keyword_target_period||t.keyword_target_period,keyword_target_action:e.keyword_target_action||t.keyword_target_action,targetCards:(e.targetCards||e.target_cards||t.targetCards||[]).map((e=>({...e}))),target_config:(()=>{var a,r,n,i,l;const o=(e.targetCards||e.target_cards||[])[0];return o?{...t.target_config,target_gender:(null==(a=o.gender)?void 0:a[0])||"all",target_age:(null==(r=o.ageRanges)?void 0:r[0])||"all",target_city:(null==(n=o.location)?void 0:n[0])||"all",target_device:(null==(i=o.platform)?void 0:i[0])||"all",industry_interest_target:{content_interests:t.target_config.industry_interest_target.content_interests,shopping_interests:t.target_config.industry_interest_target.shopping_interests},...o.crowds&&o.crowds.length>0?{crowd_target:{crowd_pkg:o.crowds,dmp_permission:(null==(l=t.target_config.crowd_target)?void 0:l.dmp_permission)||!0}}:{},keywords:o.selectedKeywords||[],interest_keywords:t.target_config.interest_keywords,intelligent_expansion:t.target_config.intelligent_expansion}:t.target_config})()};V.value[0].unit=a},X=()=>{H.value={noteList:[],targetCards:[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}],bidForm:{bid:.1,smartBid:!1,minBid:.1,maxBid:100}},$.value=[]};j({scrollToSection:e=>{var t,a;null==(a=null==(t=P.value)?void 0:t.scrollToSection)||a.call(t,e)},validate:()=>{var e,t;return null==(t=null==(e=P.value)?void 0:e.validate)?void 0:t.call(e)}});const Y=[{name:"营销诉求",path:"/marketing",component:N},{name:"计划基本信息",path:"/planInfo",component:N},{name:"目标和出价方式",path:"/target",component:O}],Z=n((()=>{switch(E.value){case"plan":default:return Y[0].component;case"unit":return x;case"creative":return L;case"confirm":return J}})),ee=r({}),te=n((()=>{switch(E.value){case"unit":return{unitData:V.value[0].unit,savedState:H.value,campaignData:{plan:{advertiser_id:V.value[0].plan.advertiser_id}}};case"creative":return{campaignData:{unit:{notes:H.value.noteList,note_ids:H.value.noteList.map((e=>e.noteId)),targetCards:H.value.targetCards,bidForm:H.value.bidForm},plan:{bidding_strategies:V.value[0].plan.bidding_strategies,advertiser_id:String(V.value[0].plan.advertiser_id)}},savedState:$.value,creativeSteps:G,"onUpdate:campaign-data":Q,"onUpdate:creative-stats":ue,"onUpdate:creative-steps":e=>G.splice(0,G.length,...e)};case"confirm":return{campaignData:{...V.value[0],unit:{...V.value[0].unit,notes:H.value.noteList,targetCards:H.value.targetCards,bidForm:H.value.bidForm},create:{...V.value[0].create,creativeList:$.value}},creativeData:ee.value,"onUpdate:transformedData":ve};default:return{campaignData:V.value[0]}}})),ae=n((()=>"plan"!==E.value&&"confirm"===E.value)),re=n((()=>"plan"!==E.value)),ne=async(e,t)=>!e&&t?(C.error(t),!1):e,ie=n((()=>"unit"===E.value||"plan"===E.value&&M.value===Y.length-1)),le=n((()=>"creative"===E.value)),oe=n((()=>"confirm"===E.value||"creative"===E.value)),se=e=>{const t=(new Date).toISOString().split("T")[0],a=localStorage.getItem(`skip_${e}_confirmation`);if(a){const{skipUntil:e}=JSON.parse(a);return e===t}return!1},ce=e=>{const t=(new Date).toISOString().split("T")[0];localStorage.setItem(`skip_${e}_confirmation`,JSON.stringify({skipUntil:t}))};r({plan:0,unit:0,creative:0,confirm:0});const de=r({priceCount:0,targetCount:0,noteCount:0,totalCount:0}),ue=e=>{ee.value={...e,plan:V.value[0].plan},de.value=e.stats},ve=e=>{let t=e.map((e=>{var t,a,r,n;return 0!=(null==(a=null==(t=e.unit.target_config)?void 0:t.crowd_target)?void 0:a.crowd_pkg.length)&&null!=(null==(n=null==(r=e.unit.target_config)?void 0:r.crowd_target)?void 0:n.crowd_pkg)||delete e.unit.target_config.crowd_target,{...e}}));z.value=t},ge=r(!1),_e=(e,t)=>{var a,r;if(e.targetType!==t.targetType)return!1;if(Array.isArray(e.crowds)&&Array.isArray(t.crowds)){if(e.crowds.length!==t.crowds.length)return!1;const a=[...e.crowds].sort(),r=[...t.crowds].sort();if(JSON.stringify(a)!==JSON.stringify(r))return!1}if((null==(a=e.crowd_target)?void 0:a.crowd_pkg)&&(null==(r=t.crowd_target)?void 0:r.crowd_pkg)){if(e.crowd_target.crowd_pkg.length!==t.crowd_target.crowd_pkg.length)return!1;const a=e.crowd_target.crowd_pkg.map((e=>e.value)).sort(),r=t.crowd_target.crowd_pkg.map((e=>e.value)).sort();if(JSON.stringify(a)!==JSON.stringify(r))return!1}const n=JSON.stringify(e.gender.sort())===JSON.stringify(t.gender.sort())&&JSON.stringify(e.ageRanges.sort())===JSON.stringify(t.ageRanges.sort())&&JSON.stringify(e.platform.sort())===JSON.stringify(t.platform.sort())&&JSON.stringify(e.location.sort())===JSON.stringify(t.location.sort())&&JSON.stringify(e.selectedLocations)===JSON.stringify(t.selectedLocations)&&JSON.stringify(e.selectedKeywords)===JSON.stringify(t.selectedKeywords)&&JSON.stringify(e.selectedCrowds)===JSON.stringify(t.selectedCrowds),i=JSON.stringify(e.selectedCities)===JSON.stringify(t.selectedCities)&&JSON.stringify(e.selectedDistricts)===JSON.stringify(t.selectedDistricts);return n&&i},pe=async()=>{var e,t,a;try{if(null==(e=P.value)?void 0:e.validate){if(!(await P.value.validate()))return}if("unit"===E.value&&H.value.targetCards.length>1&&(e=>{for(let t=0;t<e.length;t++)for(let a=t+1;a<e.length;a++)if(_e(e[t],e[a]))return!0;return!1})(H.value.targetCards))return void C.error("存在完全相同的定向卡片，请修改后再继续");if("unit"===E.value&&"confirm"===ye(E.value)&&H.value.targetCards.length>0&&(H.value.targetCards.forEach((e=>{const t=e;t.crowd_target?t.crowd_target.crowd_pkg||(t.crowd_target.crowd_pkg=[]):t.crowd_target={crowd_pkg:[]}})),V.value[0].unit.targetCards=H.value.targetCards.map((e=>({...e})))),"unit"===E.value&&"creative"===ye(E.value)&&($.value=[],ee.value={},de.value={priceCount:0,targetCount:0,noteCount:0,totalCount:0},(null==(t=V.value[0])?void 0:t.create)&&(V.value[0].create={advertiser_id:V.value[0].plan.advertiser_id,creativity_name:"",note_id:"",click_urls:[],expo_urls:[],conversion_type:0,jump_url:"",landing_page_type:1,bar_content:"立即参与",conversion_component_types:[0],custom_mask:1,custom_title:0,title_fills:[],mask_gen:1,title_gen:1,creativeList:[]})),"creative"===E.value&&"confirm"===ye(E.value)&&(null==(a=P.value)?void 0:a.getCreativeData)){const e=await P.value.getCreativeData();e.creativeList&&e.creativeList.length,Array.isArray(e.creativeList)&&($.value=e.creativeList.map((e=>{const t=Array.isArray(e.conversion_component_types)?e.conversion_component_types:Array.isArray(e.conversionComponentTypes)?e.conversionComponentTypes:[0];return{...e,conversion_type:e.conversion_type??e.conversionType??0,conversion_component_types:t,qual_info:e.qual_info||{apply_id:e.industry||null,product_qual_id_list:Array.isArray(e.products)?e.products.map((e=>"string"==typeof e?Number(e):e)).filter(Boolean):[],brand_qual_id_list:Array.isArray(e.brands)?e.brands.map((e=>"string"==typeof e?Number(e):e)).filter(Boolean):[]}}})),$.value.length)}if(ae.value){ge.value=!0;try{let e=await T(z.value);990===e.code?(C.success(e.msg),K.push("/juguang/tijiao"),R.removeTabs(I.fullPath),K.push("/juguang/tijiao")):C.error(e.msg)}catch(r){C.error("提交失败，请重试")}finally{ge.value=!1}}else{const e=ye(E.value);e&&("confirm"===E.value&&X(),E.value=e,k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)})))}}catch(r){C.error("操作失败，请重试"),ae.value&&(ge.value=!1)}},me=()=>{var e,t,a;const r=fe(E.value);if(r)if("confirm"===E.value){if(se("confirm_to_creative")){const a=[...H.value.noteList],n=JSON.parse(JSON.stringify(H.value.targetCards)),i={...H.value.bidForm},l=[...$.value];return X(),H.value.noteList=a,H.value.targetCards=n,H.value.bidForm=i,$.value=l,(null==(e=V.value[0])?void 0:e.unit)&&(V.value[0].unit.targetCards=JSON.parse(JSON.stringify(H.value.targetCards))),(null==(t=V.value[0])?void 0:t.create)&&(V.value[0].create.creativeList=$.value),E.value=r,void k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)}))}const a=S("div",null,[S("p",null,'回到上一步会导致"信息确认"中所有编辑丢失，是否继续？'),S("div",{style:"margin-top: 10px;"},[S("label",{style:"display: flex; align-items: center;"},[S("input",{type:"checkbox",style:"margin-right: 5px;",id:"dontShowAgainToday"}),S("span",null,"今天不再提示")])])]);h.confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",showClose:!0,distinguishCancelAndClose:!0,showCancelButton:!0,dangerouslyUseHTMLString:!0}).then((()=>{var e,t;const a=document.getElementById("dontShowAgainToday");a&&a.checked&&ce("confirm_to_creative");const n=[...H.value.noteList],i=JSON.parse(JSON.stringify(H.value.targetCards)),l={...H.value.bidForm},o=[...$.value];X(),H.value.noteList=n,H.value.targetCards=i,H.value.bidForm=l,$.value=o,(null==(e=V.value[0])?void 0:e.unit)&&(V.value[0].unit.targetCards=JSON.parse(JSON.stringify(H.value.targetCards))),(null==(t=V.value[0])?void 0:t.create)&&(V.value[0].create.creativeList=$.value),E.value=r,k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)}))})).catch((()=>{}))}else if("creative"===E.value){if(se("creative_to_unit"))return $.value=[],ee.value={},de.value={priceCount:0,targetCount:0,noteCount:0,totalCount:0},(null==(a=V.value[0])?void 0:a.create)&&(V.value[0].create={advertiser_id:V.value[0].plan.advertiser_id,creativity_name:"",note_id:"",click_urls:[],expo_urls:[],conversion_type:0,jump_url:"",landing_page_type:1,bar_content:"立即参与",conversion_component_types:[0],custom_mask:1,custom_title:0,title_fills:[],mask_gen:1,title_gen:1,creativeList:[]}),E.value=r,void k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)}));const e=S("div",null,[S("p",null,"回到上一步会清除所有创意数据，再次进入创意层级时将基于最新的计划和单元数据重新生成。是否继续？"),S("div",{style:"margin-top: 10px;"},[S("label",{style:"display: flex; align-items: center;"},[S("input",{type:"checkbox",style:"margin-right: 5px;",id:"dontShowAgainTodayCreative"}),S("span",null,"今天不再提示")])])]);h.confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",showClose:!0,distinguishCancelAndClose:!0,showCancelButton:!0,dangerouslyUseHTMLString:!0}).then((()=>{var e;const t=document.getElementById("dontShowAgainTodayCreative");t&&t.checked&&ce("creative_to_unit"),$.value=[],ee.value={},de.value={priceCount:0,targetCount:0,noteCount:0,totalCount:0},(null==(e=V.value[0])?void 0:e.create)&&(V.value[0].create={advertiser_id:V.value[0].plan.advertiser_id,creativity_name:"",note_id:"",click_urls:[],expo_urls:[],conversion_type:0,jump_url:"",landing_page_type:1,bar_content:"立即参与",conversion_component_types:[0],custom_mask:1,custom_title:0,title_fills:[],mask_gen:1,title_gen:1,creativeList:[]}),E.value=r,k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)}))})).catch((()=>{}))}else E.value=r,k((()=>{const e=document.querySelector(".main-content");e&&(e.scrollTop=0)}))},ye=e=>{const t=["plan","unit","creative","confirm"],a=t.indexOf(e);return a<t.length-1?t[a+1]:e},fe=e=>{const t=["plan","unit","creative","confirm"],a=t.indexOf(e);return a>0?t[a-1]:e};return(e,t)=>{const a=i("el-icon"),r=i("el-button");return o(),l("div",A,[s("aside",D,[s("div",{class:c(["level-item",{"level-active":"plan"===E.value}])},[s("div",B,[d(a,null,{default:u((()=>[d(v(g))])),_:1})]),t[1]||(t[1]=s("span",{class:"level-text"},"计划层级",-1))],2),s("div",{class:c(["level-item",[{"level-active":"unit"===E.value},{disabled:!ie.value}]])},t[2]||(t[2]=[s("div",{class:"level-number"},"2",-1),s("span",{class:"level-text"},"单元层级",-1)]),2),s("div",{class:c(["level-item",[{"level-active":"creative"===E.value},{disabled:!le.value}]])},t[3]||(t[3]=[s("div",{class:"level-number"},"3",-1),s("span",{class:"level-text"},"创意层级",-1)]),2),s("div",{class:c(["level-item",[{"level-active":"confirm"===E.value},{disabled:!oe.value}]])},t[4]||(t[4]=[s("div",{class:"level-number"},"4",-1),s("span",{class:"level-text"},"信息确认",-1)]),2)]),s("main",F,[s("div",U,[(o(),_(m(Z.value),p({ref_key:"contentRef",ref:P},te.value,{"onUpdate:campaignData":Q,"onUpdate:unitData":W,onValidate:ne,"onUpdate:creativeStats":ue,"onUpdate:creativeSteps":t[0]||(t[0]=e=>G=e),"onUpdate:transformedData":ve}),null,16)),s("div",q,[re.value?(o(),_(r,{key:0,onClick:me},{default:u((()=>t[5]||(t[5]=[f(" 上一步 ")]))),_:1})):y("",!0),d(r,{type:"primary",loading:ge.value,onClick:pe},{default:u((()=>[f(w(ae.value?"完成":"下一步"),1)])),_:1},8,["loading"])])])])])}}}),[["__scopeId","data-v-60498570"]]);export{I as default};
