import{H as e,u as a,r as t,f as r,p as l,a as o,Q as u,c as n,o as d,h as s,b as i,w as p,d as c,m as f,g as m,t as _,n as v,y as b}from"./index-C2bfFjZ1.js";import{u as y}from"./tabs-B2gAxMzC.js";import{s as h}from"./setupRequest-CaDpMqvE.js";import{_ as w}from"./_plugin-vue_export-helper-BXFjo1rG.js";const g={class:"dashboard-details"},I={class:"back-button-wrapper"},N={class:"card-header"},k={class:"filter-info"},D={key:0},x={class:"pagination-container"},q=w({__name:"index",setup(w){const q=e(),E=a(),S=t(E.query.platform||1),A=t(E.query.timeRange||""),C=t(E.query.startDate||""),T=t(E.query.endDate||""),G=t(E.query.cardId||1),M=t(E.query.platformType||"1"),j=t(E.query.kol_attributes),z=t([]),R=t(!0),L=t(1),O=t(10),P=t(0),H=e=>1==e?"星图":2==e?"蒲公英":"全部",F=y(),Q=()=>{F.removeTabs(E.fullPath),q.push("/DataDashboard/index")},B=async()=>{R.value=!0;try{const e={start_date:C.value,end_date:T.value,page:L.value,page_size:O.value,card_id:G.value,platform_type:0==S.value?1:S.value,kol_attributes:j.value},a=await(e=>h({url:"/xingtu/order_detail",method:"get",params:e}))(e);a&&a.data&&(z.value=(a.data.list||[]).map((e=>{return{platform:H(e.platform_type||1),orderId:e.order_id||"--",influencerName:e.kol_name||"--",influencerId:e.platform_uid||"--",taskId:e.task_id||"--",influencerType:(t=e.kol_attributes,1===t?"野生达人":2===t?"机构达人":"--"),agencyName:e.mcn_short_name||"--",clientName:e.company||"--",account:e.star_name||"--",orderAmount:e.actual_amount_price||0,rebateRate:e.rebate_ratio||0,rebateAmount:e.rebate_amount||0,businessMediator:e.business_media||"--",connectionMediator:e.alliance_personnel||"--",orderStatus:(a=e.order_status||"PENDING",{FINISHED:"已完成",ONGOING:"进行中",PENDING:"待确认",CANCELED:"已取消"}[a]||a),createTime:e.order_create_time||"--"};var a,t})),P.value=a.data.count||0)}catch(e){}finally{R.value=!1}},J=e=>{L.value=e,B()},K=e=>{O.value=e,L.value=1,B()};return r([C,T,G,M],(()=>{L.value=1,B()})),l((()=>{B()})),(e,a)=>{const t=o("el-button"),r=o("el-tag"),l=o("el-table-column"),y=o("el-table"),h=o("el-pagination"),w=o("el-card"),q=u("loading");return d(),n("div",g,[s("div",I,[a[1]||(a[1]=s("span",{class:"title"},"数据详情",-1)),i(t,{onClick:Q,type:"primary",icon:"el-icon-arrow-left"},{default:p((()=>a[0]||(a[0]=[c("返回仪表盘")]))),_:1})]),i(w,{class:"table-card"},{header:p((()=>[s("div",N,[s("div",k,[a[2]||(a[2]=s("span",null,"数据类型：",-1)),i(r,{type:"primary"},{default:p((()=>{var e,a;return[c(_(null==(a=null==(e=b(E))?void 0:e.query)?void 0:a.type),1)]})),_:1}),a[3]||(a[3]=s("span",null,"平台：",-1)),i(r,null,{default:p((()=>[c(_(H(S.value)),1)])),_:1}),a[4]||(a[4]=s("span",null,"时间范围：",-1)),i(r,null,{default:p((()=>{return[c(_((e=A.value,{week:"近一周",month:"近一个月",three_months:"近三个月",custom:"自定义时间"}[e]||e)),1)];var e})),_:1}),C.value&&T.value?(d(),n("span",D,"日期：")):v("",!0),C.value&&T.value?(d(),m(r,{key:1,type:"success"},{default:p((()=>[c(_(C.value)+" 至 "+_(T.value),1)])),_:1})):v("",!0)])])])),default:p((()=>[f((d(),m(y,{data:z.value,style:{width:"100%"},border:"",stripe:"",height:"calc(100vh - 380px)"},{default:p((()=>[i(l,{prop:"platform",label:"平台",width:"100",fixed:"left"}),i(l,{prop:"orderId",label:"订单ID",width:"120",fixed:"left"}),i(l,{prop:"influencerName",label:"达人昵称",width:"120",fixed:"left"}),i(l,{prop:"influencerId",label:"达人ID",width:"120"}),i(l,{prop:"taskId",label:"任务ID",width:"120"},{default:p((e=>[c(_(e.row.taskId||"--"),1)])),_:1}),i(l,{prop:"influencerType",label:"达人属性",width:"120"},{default:p((e=>["机构达人"===e.row.influencerType?(d(),m(r,{key:0,type:"success"},{default:p((()=>a[5]||(a[5]=[c("机构达人")]))),_:1})):"野生达人"===e.row.influencerType?(d(),m(r,{key:1,type:"warning"},{default:p((()=>a[6]||(a[6]=[c("野生达人")]))),_:1})):(d(),m(r,{key:2,type:"info"},{default:p((()=>a[7]||(a[7]=[c("--")]))),_:1}))])),_:1}),i(l,{prop:"agencyName",label:"机构简称",width:"120"},{default:p((e=>[c(_(e.row.agencyName||"--"),1)])),_:1}),i(l,{prop:"clientName",label:"客户/品牌名称",width:"150"}),i(l,{prop:"account",label:"账户信息",width:"120"}),i(l,{prop:"orderAmount",label:"订单金额",width:"120"},{default:p((e=>[c(" ¥"+_((e.row.orderAmount||0).toLocaleString()),1)])),_:1}),i(l,{prop:"rebateRate",label:"返点比例",width:"100"},{default:p((e=>[c(_(e.row.rebateRate||0)+"% ",1)])),_:1}),i(l,{prop:"rebateAmount",label:"返点金额",width:"120"},{default:p((e=>[c(" ¥"+_((e.row.rebateAmount||0).toLocaleString()),1)])),_:1}),i(l,{prop:"businessMediator",label:"商务媒介",width:"120"},{default:p((e=>[c(_(e.row.businessMediator||"--"),1)])),_:1}),i(l,{prop:"connectionMediator",label:"建联媒介",width:"120"},{default:p((e=>[c(_(e.row.connectionMediator||"--"),1)])),_:1}),i(l,{prop:"orderStatus",label:"订单状态",width:"100"},{default:p((e=>{return[i(r,{type:(a=e.row.orderStatus,{FINISHED:"success",ONGOING:"primary",PENDING:"warning",CANCELED:"info"}[a]||"info")},{default:p((()=>[c(_(e.row.orderStatus),1)])),_:2},1032,["type"])];var a})),_:1}),i(l,{prop:"createTime",label:"订单创建时间",width:"180"})])),_:1},8,["data"])),[[q,R.value]]),s("div",x,[i(h,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:P.value,"page-size":O.value,"current-page":L.value,onSizeChange:K,onCurrentChange:J},null,8,["total","page-size","current-page"])])])),_:1})])}}},[["__scopeId","data-v-6ff00876"]]);export{q as default};
