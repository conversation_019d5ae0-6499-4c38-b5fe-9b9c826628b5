import{_ as e}from"./default-face-CI2vwNfZ.js";import{o as l}from"./kolOrder-E7NJTBcc.js";import{h as o,q as t,a,x as n}from"./order-AmV_cyoN.js";import{_ as i}from"./_plugin-vue_export-helper-BXFjo1rG.js";import{r as d,f as r,a as s,c as u,o as c,h as p,b as m,n as _,w as v,g as f,d as k,t as h,F as y,i as g,K as x,E as w}from"./index-C2bfFjZ1.js";import"./lodash-Y6-DaIz7.js";const b={key:0,id:"selectkol",class:"module-primary"},V={class:"module-primary-legend",style:{"padding-bottom":"0"}},C={style:{display:"flex","align-items":"center"}},z={class:"ml-2"},T={id:"selectkol",class:"module-primary"},K={key:0,class:"module-secondary-group",style:{"overflow-x":"auto"}},U={class:"search-div-photo flex items-center"},I=["src"],E={key:1,src:e,width:"45",height:"45",alt:""},j={class:"ml-4 font-semibold"},S={class:"is-required"},F={style:{display:"flex","flex-direction":"row"}},q={key:0,class:"cart-component"},A={class:"cart-count"},B={style:{display:"flex","justify-content":"center","align-items":"center"}},O={class:"shopping-cart-container"},R={class:"add-product-form",style:{"margin-bottom":"15px",display:"flex",gap:"10px",width:"100%"}},D={class:"validation-hints",style:{"margin-bottom":"15px"}},N={key:0,class:"validation-warning",style:{color:"#F56C6C","font-size":"12px","margin-bottom":"5px"}},W={key:0,class:"product-list"},$={style:{width:"40px"}},G={style:{flex:"1",overflow:"hidden","text-overflow":"ellipsis"}},H={style:{flex:"2",overflow:"hidden","text-overflow":"ellipsis"}},J={style:{width:"80px"}},L={key:1,class:"empty-cart",style:{color:"#999","text-align":"center",padding:"15px","background-color":"#f8f8f8","border-radius":"4px"}},M=i({__name:"settlementInfo",props:["task_info","star_id"],setup(e){const i=e,M=d(),P=l(),Q=d([]),X=d([]),Y=d(!1),Z=d("1"),ee=d(!1),le=d(null),oe=d({title:"",product_link:""}),te=()=>{if(!le.value)return;if(le.value.ecom_cart||(le.value.ecom_cart=[]),le.value.ecom_cart.length>=7)return void w.warning("购物车商品最多支持添加7个");if(!oe.value.title||!oe.value.product_link)return void w.warning("请填写完整商品信息");le.value.ecom_cart.some((e=>e.product_link===oe.value.product_link))?w.warning("商品链接不可重复"):(le.value.ecom_cart.push({title:oe.value.title,product_link:oe.value.product_link}),P.updateTaskKolEcomCart(le.value.id,le.value.ecom_cart),oe.value={title:"",product_link:""},w.success("商品添加成功"))};r((()=>{var e,l;return null==(l=null==(e=null==i?void 0:i.task_info)?void 0:e.task_kol)?void 0:l.length}),((e,l)=>{var o;if(e>1){let e=null==(o=null==i?void 0:i.task_info)?void 0:o.task_kol.map((e=>{const l={...e,component_info:{link_component_ids:[],industry_component_id:"",live_attract_component_id:"",search_word:""},ecom_cart:e.ecom_cart||[]};return"3"===Z.value&&(l.component_info.ecom_cart=l.ecom_cart),l}));P.setKolInfo(e),Y.value=!0,M.value.batch_status=Y.value}else Y.value=!1,M.value.batch_status=Y.value}));const ae={1:"1-20S视频",2:"21-60S视频",71:"60S以上视频"},ne=async(e,l)=>{var o;if(!(null==(o=M.value)?void 0:o.star_id))return void w({message:"请先选择星图账号",type:"warning"});const t=w({message:"组件信息更新中...",type:"info",duration:0});await n({advertiser_id:M.value.star_id,type:1==Z.value?2:3}).then((e=>{990==e.code?(t.close(),se(),w({message:"组件信息更新成功",type:"success"})):t.close()}))};M.value=P.kolOrder;const ie=e=>{var l;Y.value=e,M.value.batch_status=Y.value,e||null==(l=null==i?void 0:i.task_info)||l.task_kol.map((e=>{e.kol_id==M.value.kol_id&&(e.component_info="",e.xingtu_task_name="",e.component_id="",e.search_word="",e.cocreate_douyin_id="")}))},de=e=>{var l;const o=Z.value;if("3"===o&&"3"!==e){if(null==(l=null==i?void 0:i.task_info)?void 0:l.task_kol.some((e=>e.ecom_cart&&e.ecom_cart.length>0)))return void x.confirm("切换组件类型将清空所有已添加的购物车商品，确定要继续吗？","确认切换",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{re(e,o)})).catch((()=>{Z.value="3"}))}re(e,o)},re=(e,l)=>{var o,t;Z.value=e,"1"!==e&&"2"!==e||se("",e);let a=!1;"3"===l&&"3"!==e&&(null==(o=null==i?void 0:i.task_info)||o.task_kol.forEach((e=>{var l;if(e.ecom_cart&&e.ecom_cart.length>0){a=!0,e.ecom_cart=[],e.component_info&&"string"!=typeof e.component_info||(e.component_info={}),e.component_info.ecom_cart&&delete e.component_info.ecom_cart,P.updateTaskKolEcomCart(e.id,[]);const o=[...null==(l=null==i?void 0:i.task_info)?void 0:l.task_kol];P.setKolInfo(o)}}))),setTimeout((()=>{var e,l;(null==(e=null==i?void 0:i.task_info)?void 0:e.task_kol.some((e=>e.ecom_cart&&e.ecom_cart.length>0)))&&(null==(l=null==i?void 0:i.task_info)||l.task_kol.forEach((e=>{e.ecom_cart&&e.ecom_cart.length>0&&(e.ecom_cart=[],P.updateTaskKolEcomCart(e.id,[]))})))}),0),null==(t=null==i?void 0:i.task_info)||t.task_kol.forEach((l=>{"3"!==e&&(P.updateTaskKolComponentInfo(l.id,[]),l.kol_id==M.value.kol_id&&(l.component_info="",l.component_id="")),"3"===e&&(l.ecom_cart||(l.ecom_cart=[]),l.component_info&&"string"!=typeof l.component_info||(l.component_info={}),l.component_info.ecom_cart=l.ecom_cart,l.component_info.link_component_ids=[],l.component_info.industry_component_id="",l.component_info.live_attract_component_id="",l.component_id&&(l.component_id=""))})),a&&w({message:"已切换组件类型，购物车商品已清空",type:"info"})},se=async(e,l=Z.value)=>{let o=e;const n={star_id:i.star_id,link_name:o,page:1,page_size:10};1==l?await t(n).then((e=>{const l=e.data.list;Q.value=l})):2==l&&await a(n).then((e=>{const l=e.data.list.map((e=>({link_name:e.anchor_name,component_id:e.industry_anchor_id})));Q.value=l}))},ue=e=>{e&&se()};return o().then((e=>{X.value=e.data})),r((()=>null==i?void 0:i.star_id),(()=>{var e;null==(e=null==i?void 0:i.task_info)||e.task_kol.map((e=>{e.kol_id==M.value.kol_id&&(e.component_info="",e.component_id="")}))}),{deep:!0}),(e,l)=>{var o,t,a,n;const d=s("el-switch"),r=s("el-table-column"),x=s("el-input"),re=s("el-option"),ce=s("el-select"),pe=s("RefreshRight"),me=s("el-icon"),_e=s("el-tooltip"),ve=s("el-button"),fe=s("InfoFilled"),ke=s("el-table"),he=s("el-dialog");return c(),u(y,null,[p("div",null,[(null==(t=null==(o=null==i?void 0:i.task_info)?void 0:o.task_kol)?void 0:t.length)>1?(c(),u("div",b,[p("div",V,[p("div",C,[l[5]||(l[5]=p("div",null,"个性化信息设置",-1)),p("div",z,[m(d,{modelValue:Y.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Y.value=e),onChange:ie},null,8,["modelValue"])]),l[6]||(l[6]=p("div",{class:"ml-2"},[p("span",{style:{"font-size":"12px",color:"#704314"}},"选择个性化信息后，需要针对每个达人单独设置具体信息，其他非个性化信息则所有下单达人保持一致")],-1))])])])):_("",!0),p("div",T,[l[8]||(l[8]=p("div",{class:"module-primary-legend",style:{"border-bottom":"1px solid #e8e8e8"}},"达人信息",-1)),(null==(a=null==i?void 0:i.task_info)?void 0:a.task_kol)?(c(),u("div",K,[m(ke,{data:null==(n=null==i?void 0:i.task_info)?void 0:n.task_kol,size:"small","header-cell-style":{textAlign:"center",backgroundColor:"#ffffff",color:"#606266"},"cell-style":{textAlign:"center"}},{default:v((()=>{var e,o,t,a,n,d,s,w,b,V,C,z;return[m(r,{prop:"kol_name",label:"达人信息",width:"200",fixed:""},{default:v((e=>{var l,o,t;return[p("div",U,[(null==(l=null==e?void 0:e.row)?void 0:l.kol_photo)?(c(),u("img",{key:0,src:e.row.kol_photo,width:"45",height:"45",alt:""},null,8,I)):(c(),u("img",E)),p("span",j,h(null==(o=null==e?void 0:e.row)?void 0:o.kol_name),1)]),k(" ID:"+h(null==(t=null==e?void 0:e.row)?void 0:t.platform_uid),1)]})),_:1}),(null==(o=null==(e=null==i?void 0:i.task_info)?void 0:e.task_kol)?void 0:o.length)>1&&Y.value?(c(),f(r,{key:0,width:"200",prop:"xingtu_task_name",label:"任务名称"},{default:v((e=>[p("div",S,[m(x,{style:{width:"180px"},maxlength:"180",modelValue:e.row.xingtu_task_name,"onUpdate:modelValue":l=>e.row.xingtu_task_name=l,placeholder:"请输入任务名称",size:"small",clearable:"",onChange:l=>{return o=e.row,void P.updateTaskKolTaskName(o.id,o.xingtu_task_name);var o}},null,8,["modelValue","onUpdate:modelValue","onChange"])])])),_:1})):_("",!0),(null==(a=null==(t=null==i?void 0:i.task_info)?void 0:t.task_kol)?void 0:a.length)>1&&Y.value?(c(),f(r,{key:1,prop:"component_info",width:"200",label:"组件信息"},{header:v((()=>[p("div",F,[m(ce,{modelValue:Z.value,"onUpdate:modelValue":l[1]||(l[1]=e=>Z.value=e),placeholder:"选择组件类型",size:"small",style:{width:"120px"},onChange:de},{default:v((()=>[m(re,{label:"常规组件",value:"1"}),m(re,{label:"行业组件",value:"2"}),m(re,{label:"购物车组件",value:"3"})])),_:1},8,["modelValue"]),p("span",null,[m(_e,{class:"box-item",style:{"font-size":"12px"},effect:"dark",content:"获取最新组件信息（星图创建组件未同步至星推时使用）",placement:"top"},{default:v((()=>[p("div",{style:{display:"flex",color:"rgb(179, 98, 205)","align-items":"center","margin-left":"10px"},onClick:ne},[l[7]||(l[7]=p("span",{style:{"font-size":"12px",cursor:"pointer"}},"同步",-1)),m(me,{style:{"font-size":"12px","font-weight":"bold",cursor:"pointer"}},{default:v((()=>[m(pe)])),_:1})])])),_:1})])])])),default:v((e=>{var l;return["3"===Z.value?(c(),u("div",q,[p("span",A,"商品数量: "+h((null==(l=e.row.ecom_cart)?void 0:l.length)||0),1),m(ve,{type:"primary",link:"",size:"small",onClick:l=>{return(o=e.row).ecom_cart||(o.ecom_cart=[]),o.component_info&&"string"!=typeof o.component_info||(o.component_info={}),o.component_info.ecom_cart=o.ecom_cart,le.value=o,ee.value=!0,void(oe.value={title:"",product_link:""});var o}},{default:v((()=>{var l;return[k(h((null==(l=e.row.ecom_cart)?void 0:l.length)>0?"查看/编辑":"添加商品"),1)]})),_:2},1032,["onClick"])])):(c(),f(ce,{key:1,style:{width:"180px"},size:"small",disabled:!i.star_id,modelValue:e.row.component_id,"onUpdate:modelValue":l=>e.row.component_id=l,filterable:"",clearable:"",remote:"","remote-method":se,onVisibleChange:ue,onChange:l=>((e,l)=>{var o,t;if(1==Z.value){if(!e)return void P.updateTaskKolComponentInfo(l.id,[]);let a=Q.value.filter((l=>l.component_id==e)),n=7!=a[0].link_type?{link_component_ids:[null==(o=a[0])?void 0:o.component_id]}:{live_attract_component_id:null==(t=a[0])?void 0:t.component_id};M.value.task_kol.map((e=>{l.kol_id==e.kol_id&&(l.component_info=n,P.updateTaskKolComponentInfo(l.id,n.link_component_ids))}))}else if(2==Z.value){if(!e)return void P.updateTaskKolIndustryComponent(l.id,"");let o=Q.value.filter((l=>l.component_id==e));l.component_info={industry_component_id:o[0].component_id},P.updateTaskKolIndustryComponent(l.id,o[0].component_id)}})(l,e.row),placeholder:"请选择组件"},{default:v((()=>[(c(!0),u(y,null,g(Q.value,(e=>(c(),f(re,{key:e.component_id,label:e.link_name+"("+e.component_id+")",value:e.component_id},null,8,["label","value"])))),128))])),_:2},1032,["disabled","modelValue","onUpdate:modelValue","onChange"]))]})),_:1})):_("",!0),(null==(d=null==(n=null==i?void 0:i.task_info)?void 0:n.task_kol)?void 0:d.length)>1&&Y.value?(c(),f(r,{key:2,prop:"component_info",width:"200",label:"挂载搜索词"},{default:v((e=>[m(x,{style:{width:"180px"},modelValue:e.row.search_word,"onUpdate:modelValue":l=>e.row.search_word=l,placeholder:"请输入挂载搜索词",size:"small",clearable:"",onChange:l=>{return o=e.row,void P.updateTaskKolSearchWord(o.id,o.search_word);var o}},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1})):_("",!0),(null==(w=null==(s=null==i?void 0:i.task_info)?void 0:s.task_kol)?void 0:w.length)>1&&Y.value?(c(),f(r,{key:3,prop:"component_info",width:"200",label:"品牌官方抖音号"},{default:v((e=>[p("div",B,[m(_e,{class:"box-item",effect:"dark",content:"请先选择组件",placement:"top",disabled:!!e.row.component_id},{default:v((()=>[m(x,{placeholder:"请输入品牌官方抖音号",style:{width:"190px"},size:"small",disabled:!e.row.component_id,modelValue:e.row.cocreate_douyin_id,"onUpdate:modelValue":l=>e.row.cocreate_douyin_id=l},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:2},1032,["disabled"]),m(_e,{class:"box-item",effect:"dark",content:"需为蓝V企业号，抖音主页可复制抖音号",placement:"top"},{default:v((()=>[m(me,{style:{cursor:"pointer","padding-left":"2px"}},{default:v((()=>[m(fe)])),_:1})])),_:1})])])),_:1})):_("",!0),m(r,{prop:"name",width:(null==(V=null==(b=null==i?void 0:i.task_info)?void 0:b.task_kol)?void 0:V.length)>1&&Y.value?"200":null,label:"执行人"},{default:v((e=>[m(ce,{style:{width:"180px"},modelValue:e.row.executor_id,"onUpdate:modelValue":l=>e.row.executor_id=l,size:"small",filterable:"",onChange:l=>(e=>{const l=X.value.find((l=>l.id===e.executor_id));l&&P.updateTaskKolExecutor(e.id,l.id,l.name)})(e.row),placeholder:"请选择执行人员"},{default:v((()=>[(c(!0),u(y,null,g(X.value,(e=>(c(),f(re,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])),_:1},8,["width"]),m(r,{prop:"name",label:"本次返点"},{default:v((e=>{var l;return[k(h(null==(l=e.row)?void 0:l.this_rebate_ratio)+"% ",1)]})),_:1}),m(r,{prop:"name",label:"平台刊例价",width:(null==(z=null==(C=null==i?void 0:i.task_info)?void 0:C.task_kol)?void 0:z.length)>1&&Y.value?"100":null},{default:v((e=>{var l,o;return[p("p",null,h(ae[null==(l=null==e?void 0:e.row)?void 0:l.cooperation_type]+" "),1),p("p",null,h("¥"+(null==(o=e.row)?void 0:o.kol_price)),1)]})),_:1},8,["width"])]})),_:1},8,["data"])])):_("",!0)])]),m(he,{modelValue:ee.value,"onUpdate:modelValue":l[4]||(l[4]=e=>ee.value=e),title:le.value?`${le.value.kol_name} 的购物车商品`:"购物车商品",width:"650px"},{default:v((()=>{var e,o;return[p("div",O,[l[13]||(l[13]=p("div",{class:"dialog-instructions",style:{"margin-bottom":"15px",color:"#606266","font-size":"14px","background-color":"#f5f7fa",padding:"10px","border-radius":"4px"}},[p("p",null,"您可以为该达人添加最多7个购物车商品，添加后将在该达人视频中显示购物车组件。")],-1)),p("div",R,[m(x,{modelValue:oe.value.title,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.value.title=e),placeholder:"商品名称",style:{width:"200px"},maxlength:"50","show-word-limit":""},null,8,["modelValue"]),m(x,{modelValue:oe.value.product_link,"onUpdate:modelValue":l[3]||(l[3]=e=>oe.value.product_link=e),placeholder:"商品链接",style:{width:"300px"}},null,8,["modelValue"]),m(ve,{type:"primary",onClick:te,disabled:!oe.value.title||!oe.value.product_link||le.value&&(null==(e=le.value.ecom_cart)?void 0:e.length)>=7},{default:v((()=>l[9]||(l[9]=[k(" 添加 ")]))),_:1},8,["disabled"])]),p("div",D,[le.value&&(null==(o=le.value.ecom_cart)?void 0:o.length)>=7?(c(),u("div",N," ⚠️ 购物车商品最多支持添加7个 ")):_("",!0),l[10]||(l[10]=p("div",{class:"validation-info",style:{color:"#98734a","font-size":"12px"}}," * 仅支持推广设有佣金的精选联盟、淘宝联盟商品 ",-1))]),le.value&&le.value.ecom_cart&&le.value.ecom_cart.length>0?(c(),u("div",W,[l[12]||(l[12]=p("div",{class:"product-list-header",style:{"font-weight":"bold","margin-bottom":"8px",display:"flex","border-bottom":"1px solid #eee","padding-bottom":"8px"}},[p("div",{style:{width:"40px"}},"序号"),p("div",{style:{flex:"1"}},"商品标题"),p("div",{style:{flex:"2"}},"商品链接"),p("div",{style:{width:"80px"}},"操作")],-1)),(c(!0),u(y,null,g(le.value.ecom_cart,((e,o)=>(c(),u("div",{key:o,class:"product-item",style:{display:"flex","margin-bottom":"8px","align-items":"center"}},[p("div",$,h(o+1),1),p("div",G,h(e.title),1),p("div",H,h(e.product_link),1),p("div",J,[m(ve,{type:"danger",onClick:e=>(e=>{le.value&&le.value.ecom_cart&&(le.value.ecom_cart.splice(e,1),P.updateTaskKolEcomCart(le.value.id,le.value.ecom_cart),w.success("商品已删除"))})(o),size:"small",style:{padding:"4px 8px"}},{default:v((()=>l[11]||(l[11]=[k(" 删除 ")]))),_:2},1032,["onClick"])])])))),128))])):(c(),u("div",L," 请添加商品 "))])]})),_:1},8,["modelValue","title"])],64)}}},[["__scopeId","data-v-0f8dd7cc"]]);export{M as default};
