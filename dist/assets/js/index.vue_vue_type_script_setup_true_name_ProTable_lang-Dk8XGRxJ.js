import{a6 as e,e as a,x as t,r as l,s as r,a as n,g as o,o as s,w as i,h as u,b as d,bi as c,aa as p,bo as g,bp as h,aN as m,aT as b,F as v,bq as f,br as y,bs as S,bt as w,ba as C,p as A,f as P,c as k,m as z,n as j,v as B,y as V,ar as _,aH as N,i as x,a0 as T,a1 as I,a5 as E,ab as U,bu as L,an as R,bh as F}from"./index-BE6Fh1xm.js";import{S as X}from"./index-C9PKVjyH.js";import{_ as H}from"./_plugin-vue_export-helper-GSmkUi5K.js";import{S as Z}from"./sortable.esm-DeWNWKFU.js";const K="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAzCAMAAAA3r39rAAABEVBMVEUAAADb29va2trY2Njj4+PX19fZ2dng4ODf39/X19fj4+Py8vLk5OTt7e3V1dX09PT29vbZ2dn39/fY2Nj29vbb29v29vb29vb19fX29vbV1dX39/f29vb29vb19fXc3NzZ2dnd3d3e3t7h4eH19fX19fXZ2dn19fX39/fb29vb29vW1tb19fXT09P29vbf39/29vb19fXU1NTn5+fc3NzR0dH29vb19fX19fX29vbT09P39/f19fX39/ff39/39/fX19f19fXr6+vo6OjZ2dnn5+fb29vj4+Pd3d319fXm5ubm5ubj4+PY2Nja2tr39/fk5OT6+vr09PTm5ub39/f8/Pzx8fHi4uLp6enr6+vt7e37h5dsAAAAUXRSTlMAQpJ0ChanzshV+g7jBMAS47ONcNMd++jew4heMx7LwJ5HI/j387y3rKx3XFNPSeSabWswKyjt2b2ikYWBc108mZZsaT339evZsKycgX5QGL+U+rOeAAAC3UlEQVRYw+WV13qiQBiGiaIbJaAgFgTsvZds7DG9l3UEBnP/F7IIDyYmgDB7uO8R4Mc7/8z8I5gdhabfgfMC5pHfvnrOZ0uu7vvt0TfN9vNHtuSvqLQnYzxNneNOAfy8ko679xEv2QHpHCGvqRfCrQ8fZF/xg6FGZYC79DUpN9MJvFJNd8a3is9VUxR8lZCb3BGdc7mBhRydP5waBulTzCWn9frw4KhB+ghzzQ0dLBxowAs6hHngjc7FERraHtK5IyKx7BWBeYK4zjYitg14TW9/9Gj001e4XfkVXwDzTGBKN0nL+kIU5W+3Qyb5uMNa53exdjtGUSHcaqSLu+7xr0+Og7Zdmw/uBbt3FwErYbD4AZQdQIH1U7t+hspXPopBa2EYyKsdsqx0G9bCxokif02CsLPQzEnw2Vr4vNYD7oUm8Im07IYndbWHa6FasVzEYXYjowmV7rR9c/SNm/a0CxAqNIzFh+MfPBSVFYLQVP5EKw9ZaDaH81NnIQK2wlwRrNCExZyVkEjfowrv04SFEI+doApPYriNUP7PhAN04cBKGPGjCy2+bYTQz4ZRdzn8py8Qe7bOrMaNbteowvXtiKvNOqZT5BmuDCSwRheq2utljuHF7V4se1Gg8Q9CRRdqRHtLHGtx2pUhhIqMJoSa0JBwLWycMHQS+EAUyhuo6AKNxBjT52s4obqSNfSUcfU5gH4jf8N4Iq0hkCRgEMUuk6ZQ2kBVAZ5RVLjRfEaNyUtsyET1SW9vVQjXnoFwY7oTUWaonRC+miztRvMuVLcLuKWUrPL6icHFDsMm9EkDFCT9vQTLdETzSJPikp/X2JKESImtzfmlSO7/NwitBTOpZlJl4IFyKlOdMIuWEMGsIN+FVn/Wq1W5DJuKJs/KpcRnU5kkSuWzZDTFZrhqrTfrt4R3EnOAxAkiIgodfjFnepPHcSbDjlJb+VaSGrFsZvw4uWTmC74jiBGCwL/b/gIpvWL/TS00iAAAAABJRU5ErkJggg==",M=r({name:"Pagination"}),O=r({...M,props:{pageable:{},handleSizeChange:{type:Function},handleCurrentChange:{type:Function}},setup:e=>(e,a)=>{const t=n("el-pagination");return s(),o(t,{background:!0,"current-page":e.pageable.pageNum,"page-size":e.pageable.pageSize,"page-sizes":[10,25,50,100],total:e.pageable.total|e.pageable.import_count,layout:"total, sizes, prev, pager, jumper",onSizeChange:e.handleSizeChange,onCurrentChange:e.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])}}),D={class:"table-main"},q=r({name:"ColSetting"}),Q=H(r({...q,props:{colSetting:{}},setup(e,{expose:a}){const t=l(!1);return a({openColSetting:()=>{t.value=!0}}),(e,a)=>{const l=n("el-table-column"),r=n("el-switch"),c=n("el-table"),p=n("el-drawer");return s(),o(p,{modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=e=>t.value=e),title:"列设置",size:"450px"},{default:i((()=>[u("div",D,[d(c,{data:e.colSetting,border:!0,"row-key":"prop","default-expand-all":"","tree-props":{children:"_children"}},{empty:i((()=>a[1]||(a[1]=[u("div",{class:"table-empty"},[u("img",{src:K,alt:"notData"}),u("div",null,"暂无可配置列")],-1)]))),default:i((()=>[d(l,{prop:"label",align:"center",label:"列名"}),d(l,{prop:"isShow",align:"center",label:"显示"},{default:i((e=>[d(r,{modelValue:e.row.isShow,"onUpdate:modelValue":a=>e.row.isShow=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),d(l,{prop:"sortable",align:"center",label:"排序"},{default:i((e=>[d(r,{modelValue:e.row.sortable,"onUpdate:modelValue":a=>e.row.sortable=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1})])),_:1},8,["data"])])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-0cba8348"]]);const Y=r({...r({name:"TableColumn"}),props:{column:{}},setup(e){const a=c(),t=p("enumMap",l(new Map)),r=(e,a)=>t.value.get(e.prop)&&e.isFilterEnum?f(S(a.row,e.prop),t.value.get(e.prop),e.fieldNames):y(S(a.row,e.prop)),i=(e,a)=>f(S(a.row,e.prop),t.value.get(e.prop),e.fieldNames,"tag")||"primary",u=e=>d(v,null,[e.isShow&&d(n("el-table-column"),m(e,{align:e.align??"center",showOverflowTooltip:e.showOverflowTooltip??"operation"!==e.prop}),{default:t=>{let l;return e._children?e._children.map((e=>u(e))):e.render?e.render(t):e.prop&&a[b(e.prop)]?a[b(e.prop)](t):e.tag?d(n("el-tag"),{type:i(e,t)},"function"==typeof(o=l=r(e,t))||"[object Object]"===Object.prototype.toString.call(o)&&!w(o)?l:{default:()=>[l]}):r(e,t);var o},header:t=>e.headerRender?e.headerRender(t):e.prop&&a[`${b(e.prop)}Header`]?a[`${b(e.prop)}Header`](t):e.label})]);return(e,a)=>(s(),o(u,g(h(e.column)),null,16))}}),W={class:"card table-main"},G={class:"table-header"},J={class:"header-button-lf"},$={key:0,class:"header-button-ri"},ee={class:"table-empty"},ae=r({name:"ProTable"}),te=r({...ae,props:{columns:{default:()=>[]},data:{},requestApi:{},requestAuto:{type:Boolean,default:!0},requestError:{},dataCallback:{},title:{},pagination:{type:Boolean,default:!0},initParam:{default:{}},border:{type:Boolean,default:!0},toolButton:{type:[Array,Boolean],default:!0},rowKey:{default:"id"},searchCol:{default:()=>({xs:1,sm:2,md:2,lg:3,xl:4})}},emits:["search","reset","dragSort"],setup(r,{expose:c,emit:p}){var g;const h=r,f=l(),y=l("id-"+C()),S=["selection","radio","index","expand","sort"],w=l(!0),H=e=>Array.isArray(h.toolButton)?h.toolButton.includes(e):h.toolButton,M=l(""),{selectionChange:D,selectedList:q,selectedListIds:ae,isSelected:te}=((e="id")=>{const t=l(!1),r=l([]),n=a((()=>{let a=[];return r.value.forEach((t=>a.push(t[e]))),a}));return{isSelected:t,selectedList:r,selectedListIds:n,selectionChange:e=>{e.length?t.value=!0:t.value=!1,r.value=e}}})(h.rowKey),{tableData:le,pageable:re,searchParam:ne,searchInitParam:oe,getTableList:se,search:ie,reset:ue,handleSizeChange:de,handleCurrentChange:ce}=((l,r={},n=!0,o,s)=>{const i=e({tableData:[],pageable:{page:1,page_size:10,total:0},searchParam:{},searchInitParam:{},totalParam:{}}),u=a({get:()=>({page:i.pageable.page,page_size:i.pageable.page_size}),set:e=>{}}),d=async()=>{if(l)try{Object.assign(i.totalParam,r,n?u.value:{});let{data:e}=await l({...i.searchInitParam,...i.totalParam});o&&(e=o(e)),i.tableData=n?(null==e?void 0:e.data)?null==e?void 0:e.data:e.list?e.list:e.plugins:e,n&&(i.pageable.total=e.count?e.count:e.total?e.total:e.import_count?e.import_count:e.plugins_count)}catch(e){s&&s(e)}},c=()=>{i.totalParam={};let e={};for(let a in i.searchParam)(i.searchParam[a]||!1===i.searchParam[a]||0===i.searchParam[a])&&(e[a]=i.searchParam[a]);Object.assign(i.totalParam,e)};return{...t(i),getTableList:d,search:()=>{i.pageable.page=1,c(),d()},reset:()=>{i.pageable.page=1,i.searchParam={...i.searchInitParam},c(),d()},handleSizeChange:e=>{i.pageable.page=1,i.pageable.page_size=e,d()},handleCurrentChange:e=>{i.pageable.page=e,d()},updatedTotalParam:c}})(h.requestApi,h.initParam,h.pagination,h.dataCallback,h.requestError);A((()=>{Pe(),h.requestAuto&&se(),h.data&&(re.value.total=h.data.length)}));const pe=a((()=>h.data?h.pagination?h.data.slice((re.value.page-1)*re.value.page_size,re.value.page_size*re.value.page):h.data:le.value));P((()=>h.initParam),se,{deep:!0});const ge=e(h.columns),he=a((()=>be(ge))),me=l(new Map);F("enumMap",me);const be=(e,a=[])=>(e.forEach((async e=>{var t;(null==(t=e._children)?void 0:t.length)&&a.push(...be(e._children)),a.push(e),e.isShow=e.isShow??!0,e.isSetting=e.isSetting??!0,e.isFilterEnum=e.isFilterEnum??!0,await(async({prop:e,enum:a})=>{if(!a)return;if(me.value.has(e)&&("function"==typeof a||me.value.get(e)===a))return;if("function"!=typeof a)return me.value.set(e,V(a));me.value.set(e,[]);const{data:t}=await a();me.value.set(e,t)})(e)})),a.filter((e=>{var a;return!(null==(a=e._children)?void 0:a.length)}))),ve=a((()=>{var e;return null==(e=he.value)?void 0:e.filter((e=>{var a,t;return(null==(a=e.search)?void 0:a.el)||(null==(t=e.search)?void 0:t.render)})).sort(((e,a)=>e.search.order-a.search.order))}));null==(g=ve.value)||g.forEach(((e,a)=>{var t,l,r;e.search.order=(null==(t=e.search)?void 0:t.order)??a+2;const n=(null==(l=e.search)?void 0:l.key)??b(e.prop),o=null==(r=e.search)?void 0:r.defaultValue;null!=o&&(ne.value[n]=o,oe.value[n]=o)}));const fe=l(),ye=ge.filter((e=>{const{type:a,prop:t,isSetting:l}=e;return!S.includes(a)&&"operation"!==t&&l})),Se=()=>fe.value.openColSetting(),we=p,Ce=()=>{ie(),we("search")},Ae=()=>{ue(),we("reset")},Pe=()=>{const e=document.querySelector(`#${y.value} tbody`);Z.create(e,{handle:".move",animation:300,onEnd({newIndex:e,oldIndex:a}){const[t]=pe.value.splice(a,1);pe.value.splice(e,0,t),we("dragSort",{newIndex:e,oldIndex:a})}})};return c({element:f,tableData:pe,radio:M,pageable:re,searchParam:ne,searchInitParam:oe,isSelected:te,selectedList:q,selectedListIds:ae,getTableList:se,search:ie,reset:ue,handleSizeChange:de,handleCurrentChange:ce,clearSelection:()=>f.value.clearSelection(),enumMap:me}),(e,a)=>{const t=n("el-button"),l=n("el-radio"),r=n("DCaret"),c=n("el-icon"),p=n("el-tag"),g=n("el-table-column");return s(),k(v,null,[z(d(X,{onSearch:Ce,onReset:Ae,columns:ve.value,"search-param":V(ne),"search-col":e.searchCol},null,8,["columns","search-param","search-col"]),[[B,w.value]]),u("div",W,[u("div",G,[u("div",J,[_(e.$slots,"tableHeader",{selectedList:V(q),selectedListIds:V(ae),isSelected:V(te)})]),e.toolButton?(s(),k("div",$,[_(e.$slots,"toolButton",{},(()=>{var l;return[H("refresh")?(s(),o(t,{key:0,icon:V(U),circle:"",onClick:V(se)},null,8,["icon","onClick"])):j("",!0),H("setting")&&e.columns.length?(s(),o(t,{key:1,icon:V(L),circle:"",onClick:Se},null,8,["icon"])):j("",!0),H("search")&&(null==(l=ve.value)?void 0:l.length)?(s(),o(t,{key:2,icon:V(R),circle:"",onClick:a[0]||(a[0]=e=>w.value=!w.value)},null,8,["icon"])):j("",!0)]}))])):j("",!0)]),d(V(N),m({ref_key:"tableRef",ref:f},e.$attrs,{id:y.value,data:pe.value,border:e.border,"row-key":e.rowKey,onSelectionChange:V(D)}),{append:i((()=>[_(e.$slots,"append")])),empty:i((()=>[u("div",ee,[_(e.$slots,"empty",{},(()=>[a[4]||(a[4]=u("img",{src:K,alt:"notData"},null,-1)),a[5]||(a[5]=u("div",null,"暂无数据",-1))]))])])),default:i((()=>[_(e.$slots,"default"),(s(!0),k(v,null,x(ge,(t=>(s(),k(v,{key:t},[t.type&&S.includes(t.type)?(s(),o(g,m({key:0,ref_for:!0},t,{align:t.align??"center","reserve-selection":"selection"==t.type}),{default:i((n=>["expand"==t.type?(s(),k(v,{key:0},[t.render?(s(),o(T(t.render),m({key:0,ref_for:!0},n),null,16)):_(e.$slots,t.type,m({key:1,ref_for:!0},n))],64)):j("",!0),"radio"==t.type?(s(),o(l,{key:1,modelValue:M.value,"onUpdate:modelValue":a[1]||(a[1]=e=>M.value=e),label:n.row[e.rowKey]},{default:i((()=>a[3]||(a[3]=[u("i",null,null,-1)]))),_:2},1032,["modelValue","label"])):j("",!0),"sort"==t.type?(s(),o(p,{key:2,class:"move"},{default:i((()=>[d(c,null,{default:i((()=>[d(r)])),_:1})])),_:1})):j("",!0)])),_:2},1040,["align","reserve-selection"])):(s(),o(Y,{key:1,column:t},I({_:2},[x(Object.keys(e.$slots),(a=>({name:a,fn:i((t=>[_(e.$slots,a,m({ref_for:!0},t))]))})))]),1032,["column"]))],64)))),128))])),_:3},16,["id","data","border","row-key","onSelectionChange"]),_(e.$slots,"pagination",{},(()=>[e.pagination?(s(),o(O,{key:0,pageable:V(re),"handle-size-change":V(de),"handle-current-change":V(ce)},null,8,["pageable","handle-size-change","handle-current-change"])):j("",!0)]))]),e.toolButton?(s(),o(Q,{key:0,ref_key:"colRef",ref:fe,"col-setting":V(ye),"onUpdate:colSetting":a[2]||(a[2]=e=>E(ye)?ye.value=e:null)},null,8,["col-setting"])):j("",!0)],64)}}});export{te as _};
