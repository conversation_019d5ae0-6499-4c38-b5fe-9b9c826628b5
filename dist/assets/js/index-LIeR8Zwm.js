import{g as e,s as a,h as l}from"./index-BxStTrC5.js";import t from"./ChatDialog-Ikz2Qtyu.js";import{r as o,a6 as u,p as s,j as n,R as r,a as d,c,o as i,h as m,n as p,b,w as v,d as _,y as f,an as g,g as y,t as h,ao as k,E as x}from"./index-BE6Fh1xm.js";import{_ as V}from"./_plugin-vue_export-helper-GSmkUi5K.js";const w={border:!0,index:!1,align:"center",menuAlign:"center",addBtn:!1,menu:!1,column:[{label:"任务名称",prop:"subscribe_name",width:180},{label:"任务状态",prop:"subscribe_status",slot:!0},{label:"发送成功数",prop:"success_count"},{label:"发送失败数",prop:"fail_count"},{label:"创建时间",prop:"start_time"}]},C={border:!0,index:!1,menuType:"text",align:"center",menuAlign:"center",addBtn:!1,menu:!1,column:[{label:"达人信息",prop:"kol_info",slot:!0,width:260},{label:"合作意向",prop:"dm_analyze",slot:!0},{label:"建联状态",prop:"contact_status",slot:!0},{label:"建联原文",prop:"reply_dm",slot:!0},{label:"联系方式",prop:"phone",slot:!0},{label:"任务名称",prop:"subscribe_name"},{label:"建联时间",prop:"start_time"}]},z={class:"content"},T={class:"overflow-auto right-h w-full"},Y={key:0},S={class:"request_box"},D={class:"flex justify-between"},P={class:"form-content"},I={class:"form-buttons"},j={class:"content_box"},U={key:1},M={class:"request_box"},A={class:"flex justify-between"},q={class:"form-content"},B={class:"form-buttons"},E={class:"content_box"},F={class:"flex flex-col"},L={class:"flex"},O=["src"],R={style:{display:"flex","flex-direction":"column","justify-content":"center","margin-left":"20px","font-weight":"bold"}},G={key:0},H=["onClick"],J={key:1},K={key:0},N={key:1},Q=V({__name:"index",setup(V){const Q=o("first"),W=o(!1),X=o({data:[],count:0}),Z=o({data:[],count:0}),$=o(!1),ee=o({}),ae=u({currentPage:1,pageSize:10,total:0}),le=u({currentPage:1,pageSize:10,total:0}),te=o({xingtui_id:localStorage.getItem("userId"),subscribe_name:"",createTime:"",subscribe_status:""}),oe=o({xingtui_id:localStorage.getItem("userId"),subscribe_name:"",createTime:"",subscribe_success_count:"",dm_analyze:"",kol_id:""}),ue=o(null);s((()=>{ie()}));const se=e=>{e.props.name!==Q.value&&(ue.value&&ue.value.abort(),Q.value=e.props.name,ne())},ne=()=>{"first"===Q.value?(te.value={xingtui_id:localStorage.getItem("userId"),subscribe_name:"",createTime:"",subscribe_status:""},ie()):(oe.value={xingtui_id:localStorage.getItem("userId"),subscribe_name:"",createTime:"",subscribe_success_count:"",dm_analyze:"",kol_id:""},me())},re=e=>{const a=document.createElement("textarea");a.value=e,document.body.appendChild(a),a.select();try{document.execCommand("copy"),x.success("复制成功")}catch(l){x.warning("该浏览器不支持自动复制")}document.body.removeChild(a)},de=()=>{ae.currentPage=1,te.value.createTime&&(te.value.start_time=te.value.createTime[0],te.value.end_time=te.value.createTime[1]),ie()},ce=()=>{le.currentPage=1,oe.value.createTime&&(oe.value.start_time=oe.value.createTime[0],oe.value.end_time=oe.value.createTime[1]),me()},ie=async(a={})=>{W.value=!0;try{ue.value&&ue.value.abort(),ue.value=new AbortController,a.currentPage&&(ae.currentPage=a.currentPage),a.pageSize&&(ae.pageSize=a.pageSize);const l={...te.value,page:ae.currentPage,page_size:ae.pageSize},t=await e(l,ue.value.signal);990===t.code&&(X.value.data=t.data.data,X.value.count=t.data.count,ae.total=t.data.count)}catch(l){n.isCancel(l)}finally{W.value=!1}},me=async(e={})=>{W.value=!0;try{ue.value&&ue.value.abort(),ue.value=new AbortController,e.currentPage&&(le.currentPage=e.currentPage),e.pageSize&&(le.pageSize=e.pageSize);const a={...oe.value,page:le.currentPage,page_size:le.pageSize},t=await l(a,ue.value.signal);990===t.code&&(Z.value.data=t.data.data,Z.value.count=t.data.count,le.total=t.data.count)}catch(a){n.isCancel(a)}finally{W.value=!1}};return r((()=>{ue.value&&ue.value.abort()})),(e,l)=>{const o=d("el-tab-pane"),u=d("el-tabs"),s=d("el-input"),n=d("el-form-item"),r=d("el-option"),V=d("el-select"),ue=d("el-date-picker"),pe=d("el-icon"),be=d("el-button"),ve=d("el-form"),_e=d("el-tag"),fe=d("avue-crud"),ge=d("el-tooltip"),ye=d("el-dialog");return i(),c("div",z,[m("div",T,[m("div",null,[b(u,{modelValue:Q.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Q.value=e),class:"demo-tabs",onTabClick:se},{default:v((()=>[b(o,{label:"建联任务",name:"first"}),b(o,{label:"建联达人",name:"second"})])),_:1},8,["modelValue"])]),"first"===Q.value?(i(),c("div",Y,[m("div",S,[b(ve,{inline:!0,model:te.value,class:"demo-form-inline",style:{width:"100%"}},{default:v((()=>[m("div",D,[m("div",P,[b(n,{label:"任务名称"},{default:v((()=>[b(s,{modelValue:te.value.subscribe_name,"onUpdate:modelValue":l[1]||(l[1]=e=>te.value.subscribe_name=e),placeholder:"请输入任务名称",clearable:""},null,8,["modelValue"])])),_:1}),b(n,{label:"任务状态"},{default:v((()=>[b(V,{modelValue:te.value.subscribe_status,"onUpdate:modelValue":l[2]||(l[2]=e=>te.value.subscribe_status=e),placeholder:"全部",clearable:""},{default:v((()=>[b(r,{label:"待发送",value:2}),b(r,{label:"进行中",value:0}),b(r,{label:"已完成",value:1})])),_:1},8,["modelValue"])])),_:1}),b(n,{label:"创建时间",class:"date-item"},{default:v((()=>[b(ue,{modelValue:te.value.createTime,"onUpdate:modelValue":l[3]||(l[3]=e=>te.value.createTime=e),type:"daterange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})]),m("div",I,[b(be,{type:"primary",onClick:de},{default:v((()=>[b(pe,null,{default:v((()=>[b(f(g))])),_:1}),l[10]||(l[10]=_("搜索 "))])),_:1}),b(be,{icon:"Delete",onClick:ne},{default:v((()=>l[11]||(l[11]=[_("清空")]))),_:1})])])])),_:1},8,["model"])]),m("div",j,[b(fe,{ref:"taskCrud",data:X.value.data,option:f(w),page:ae,loading:W.value,onOnLoad:ie},{subscribe_status:v((({row:e})=>{return[b(_e,{type:(a=e.subscribe_status,{"待发送":"info","进行中":"warning","已完成":"success"}[a]||"info")},{default:v((()=>[_(h(e.subscribe_status),1)])),_:2},1032,["type"])];var a})),menu:v((({row:e})=>[e.fail_count>0?(i(),y(be,{key:0,text:"",style:{color:"rgb(235, 118, 118)"},onClick:l=>(async e=>{try{const l=await a({subscribe_id:e.subscribe_id});990===(null==l?void 0:l.code)?x.success("重连成功,请稍后查看建联状态"):x.error("重连失败")}catch(l){x.error("重连失败")}})(e)},{default:v((()=>l[12]||(l[12]=[_(" 发送失败重连 ")]))),_:2},1032,["onClick"])):p("",!0),b(be,{text:"",style:{color:"rgb(149, 118, 235)"},onClick:a=>(e=>{Q.value="second",oe.value={...oe.value,subscribe_id:e.subscribe_id},me()})(e)},{default:v((()=>l[13]||(l[13]=[_("查看建联达人")]))),_:2},1032,["onClick"])])),_:1},8,["data","option","page","loading"])])])):p("",!0),"second"===Q.value?(i(),c("div",U,[m("div",M,[b(ve,{inline:!0,model:oe.value,class:"demo-form-inline",style:{width:"100%"}},{default:v((()=>[m("div",A,[m("div",q,[b(n,{label:"达人信息"},{default:v((()=>[b(s,{modelValue:oe.value.kol_id,"onUpdate:modelValue":l[4]||(l[4]=e=>oe.value.kol_id=e),placeholder:"请输入达人昵称或ID",clearable:""},null,8,["modelValue"])])),_:1}),b(n,{label:"建联状态"},{default:v((()=>[b(V,{modelValue:oe.value.subscribe_success_count,"onUpdate:modelValue":l[5]||(l[5]=e=>oe.value.subscribe_success_count=e),placeholder:"全部",clearable:""},{default:v((()=>[b(r,{label:"未建联",value:0}),b(r,{label:"已建联",value:1})])),_:1},8,["modelValue"])])),_:1}),b(n,{label:"合作意向"},{default:v((()=>[b(V,{modelValue:oe.value.dm_analyze,"onUpdate:modelValue":l[6]||(l[6]=e=>oe.value.dm_analyze=e),placeholder:"全部",clearable:""},{default:v((()=>[b(r,{label:"未回复",value:0}),b(r,{label:"有意向",value:1}),b(r,{label:"无意向",value:2})])),_:1},8,["modelValue"])])),_:1}),b(n,{label:"任务名称"},{default:v((()=>[b(s,{modelValue:oe.value.subscribe_name,"onUpdate:modelValue":l[7]||(l[7]=e=>oe.value.subscribe_name=e),placeholder:"请输入任务名称",clearable:""},null,8,["modelValue"])])),_:1}),b(n,{label:"建联时间",class:"date-item"},{default:v((()=>[b(ue,{modelValue:oe.value.createTime,"onUpdate:modelValue":l[8]||(l[8]=e=>oe.value.createTime=e),type:"daterange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})]),m("div",B,[b(be,{type:"primary",onClick:ce},{default:v((()=>[b(pe,null,{default:v((()=>[b(f(g))])),_:1}),l[14]||(l[14]=_("搜索 "))])),_:1}),b(be,{icon:"Delete",onClick:ne},{default:v((()=>l[15]||(l[15]=[_("清空")]))),_:1})])])])),_:1},8,["model"])]),m("div",E,[b(fe,{ref:"kolCrud",data:Z.value.data,option:f(C),page:le,loading:W.value,onOnLoad:me},{kol_info:v((({row:e})=>[m("div",F,[m("div",L,[m("img",{src:e.kol_avatar,alt:"",style:{"border-radius":"50%",width:"50px",height:"50px","margin-right":"5px"}},null,8,O),m("div",R,h(e.kol_name),1)]),m("p",null,"ID："+h(e.kol_id),1)])])),dm_analyze:v((({row:e})=>{return[b(_e,{type:(a=e.dm_analyze,{"未回复":"info","有意向":"success","无意向":"danger"}[a]||"info")},{default:v((()=>[_(h(e.dm_analyze),1)])),_:2},1032,["type"])];var a})),contact_status:v((({row:e})=>[b(_e,{type:"未建联"===e.contact_status?"danger":"success"},{default:v((()=>[_(h(e.contact_status),1)])),_:2},1032,["type"])])),reply_dm:v((({row:e})=>[e.reply_dm?(i(),c("div",G,[b(ge,{effect:"dark",content:"点击查看建联原文",placement:"top"},{default:v((()=>[m("a",{style:{"text-decoration":"underline"},href:"javascript:;",onClick:a=>(e=>{ee.value=e,$.value=!0})(e)},h(e.reply_dm),9,H)])),_:2},1024),b(pe,{class:"ml-1 cursor-pointer",onClick:a=>re(e.reply_dm)},{default:v((()=>[b(f(k))])),_:2},1032,["onClick"])])):(i(),c("div",J,"-"))])),phone:v((({row:e})=>[e.phone?(i(),c("div",K,[m("span",null,h(e.phone),1),b(pe,{class:"ml-1 cursor-pointer",onClick:a=>re(e.phone)},{default:v((()=>[b(f(k))])),_:2},1032,["onClick"])])):(i(),c("div",N,"-"))])),_:1},8,["data","option","page","loading"])]),b(ye,{modelValue:$.value,"onUpdate:modelValue":l[9]||(l[9]=e=>$.value=e),title:"建联原文",width:"800","align-center":""},{default:v((()=>[b(t,{originTextForm:ee.value},null,8,["originTextForm"])])),_:1},8,["modelValue"])])):p("",!0)])])}}},[["__scopeId","data-v-c3b7eb1d"]]);export{Q as default};
