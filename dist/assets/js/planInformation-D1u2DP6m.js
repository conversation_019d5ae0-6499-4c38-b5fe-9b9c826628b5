import{s as a,r as e,f as t,p as i,a6 as l,e as n,a as d,c as r,o as s,b as o,h as p,w as m,y as c,aO as u,F as g,i as v,g as _,t as f,d as y,n as b,m as D,z as h,$ as V,v as w,E as T}from"./index-BE6Fh1xm.js";import k from"./time-ChyUUY53.js";import x from"./target-CXco6UTx.js";import{g as I}from"./temp-api-B36KNeYM.js";import S from"./AnchorNavigation-DOB1pEjt.js";import{_ as j}from"./_plugin-vue_export-helper-GSmkUi5K.js";import"./setupRequest-C4Opp8Oc.js";const U={class:"plan-information"},A={id:"marketing",class:"content-section",ref:"marketingRef"},R={class:"section-content"},B={class:"info-box"},E={id:"basic-info",class:"content-section",ref:"planInfoRef"},Y={class:"section-content",style:{width:"50%"}},$={class:"advertiser-option"},C={class:"advertiser-name"},F={class:"advertiser-id"},O={style:{display:"flex","align-items":"center"}},M={class:"setting-content"},q={id:"bidding-strategy",class:"content-section",ref:"targetRef"},z={class:"section-content"},H=j(a({__name:"planInformation",props:{campaignData:{}},emits:["update:campaign-data"],setup(a,{expose:j,emit:H}){const N=a,P=H,G=e([]),J=e(!1),K=async(a="")=>{J.value=!0;try{const e={key_word:a,is_owner:!0},t=await I(e);t&&t.data&&t.data.list?G.value=t.data.list:G.value=[]}catch(e){T.error("获取广告主账号失败"),G.value=[]}finally{J.value=!1}},L=a=>{K(a||"")};t([()=>G.value.length,()=>N.campaignData.plan.advertiser_id],(([a,e])=>{if(a>0&&e){const a=e.toString();G.value.find((e=>e.advertiser_id===a));Q.advertiser_id=a}}),{immediate:!0}),i((async()=>{await K()}));const Q=l({advertiser_id:N.campaignData.plan.advertiser_id,campaign_name:N.campaignData.plan.campaign_name,enable:N.campaignData.plan.enable??1,adType:N.campaignData.plan.promotion_target.toString(),dateType:void 0===N.campaignData.plan.time_type?"0":N.campaignData.plan.time_type.toString(),date:1===N.campaignData.plan.time_type?[N.campaignData.plan.start_time,N.campaignData.plan.expire_time]:[]}),W=l({plan:{...N.campaignData.plan}});t(Q,(a=>{var e,t;const i=parseInt(a.dateType);Object.assign(W.plan,{advertiser_id:a.advertiser_id,campaign_name:a.campaign_name,promotion_target:parseInt(a.adType),enable:a.enable,time_type:i,start_time:0===i?"":(null==(e=a.date)?void 0:e[0])||"",expire_time:0===i?"":(null==(t=a.date)?void 0:t[1])||""})}),{deep:!0}),t((()=>N.campaignData.plan.time_type),(a=>{void 0!==a&&a.toString()!==Q.dateType&&(Q.dateType=a.toString())}),{immediate:!0}),t((()=>N.campaignData.plan.enable),(a=>{a!==Q.enable&&(Q.enable=a,W.plan.enable=a)}),{immediate:!0}),t((()=>Q.advertiser_id),(a=>{if(a){const e=G.value.find((e=>e.advertiser_id===a.toString()));e&&(W.plan.advertiser_id=a,Q.campaign_name||(Q.campaign_name=`${e.advertiser_name}-新计划`,W.plan.campaign_name=Q.campaign_name))}}));const X=n((()=>({mon:N.campaignData.plan.time_period.mon,tues:N.campaignData.plan.time_period.tues,wed:N.campaignData.plan.time_period.wed,thur:N.campaignData.plan.time_period.thur,fri:N.campaignData.plan.time_period.fri,sat:N.campaignData.plan.time_period.sat,sun:N.campaignData.plan.time_period.sun}))),Z=e(!1),aa=()=>{Z.value=!Z.value},ea=a=>{const e=new Date;return e.setHours(0,0,0,0),a.getTime()<e.getTime()},ta=e(null);j({validate:async()=>{var a;if(!na.value)return!1;try{if(!W.plan.advertiser_id)return T.error("请选择广告主账号"),!1;if(await na.value.validate(),null==(a=ta.value)?void 0:a.validate){if(!(await ta.value.validate()))return!1;if(ta.value.getBiddingStrategies){const a=ta.value.getBiddingStrategies();Array.isArray(a)&&a.forEach(((a,e)=>{a.constraint_type})),W.plan.bidding_strategies=[...a]}}return P("update:campaign-data",{plan:{...W.plan}}),!0}catch(e){return!1}},scrollToSection:a=>{const e=document.getElementById(a);e&&e.scrollIntoView({behavior:"smooth",block:"start"})},getFormData:()=>({...N.campaignData,plan:{...W.plan}})});const ia=a=>{Object.assign(W.plan.time_period,a)},la=a=>{Array.isArray(a)&&(a.forEach(((a,e)=>{a.constraint_type})),W.plan.bidding_strategies=[...a])},na=e(null),da=[{id:"marketing",title:"营销诉求"},{id:"basic-info",title:"基本信息"},{id:"bidding-strategy",title:"出价策略"}];return(a,e)=>{const t=d("el-icon"),i=d("el-option"),l=d("el-select"),n=d("el-form-item"),T=d("el-input"),I=d("el-switch"),j=d("InfoFilled"),H=d("el-tooltip"),P=d("el-button"),K=d("el-radio"),W=d("el-radio-group"),ra=d("el-date-picker"),sa=d("el-form");return s(),r("div",U,[o(S,{anchors:da}),p("div",A,[e[7]||(e[7]=p("div",{class:"section-title"},"营销诉求",-1)),p("div",R,[p("div",B,[o(t,null,{default:m((()=>[o(c(u))])),_:1}),e[6]||(e[6]=p("div",{class:"info-content"},[p("div",{class:"info-title"},"产品种草"),p("div",{class:"info-desc"})],-1))])])],512),p("div",E,[e[13]||(e[13]=p("div",{class:"section-title"},"计划基本信息",-1)),p("div",Y,[o(sa,{model:Q,"label-width":"120px",ref_key:"formRef",ref:na},{default:m((()=>[o(n,{label:"广告主账号信息"},{default:m((()=>[o(l,{modelValue:Q.advertiser_id,"onUpdate:modelValue":e[0]||(e[0]=a=>Q.advertiser_id=a),filterable:"",remote:"","remote-method":L,placeholder:"请输入账户昵称或ID搜索",loading:J.value,style:{width:"100%"},"value-key":"advertiser_id"},{default:m((()=>[(s(!0),r(g,null,v(G.value,(a=>(s(),_(i,{key:a.advertiser_id,label:`${a.advertiser_name} (ID: ${a.advertiser_id})`,value:a.advertiser_id},{default:m((()=>[p("div",$,[p("div",C,f(a.advertiser_name),1),p("div",F,"ID: "+f(a.advertiser_id),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),o(n,{label:"产品名称"},{default:m((()=>[o(T,{modelValue:Q.campaign_name,"onUpdate:modelValue":e[1]||(e[1]=a=>Q.campaign_name=a),placeholder:"请输入产品名称",maxlength:"20","show-word-limit":""},null,8,["modelValue"])])),_:1}),o(n,{label:"默认状态"},{default:m((()=>[p("div",O,[o(I,{modelValue:Q.enable,"onUpdate:modelValue":e[2]||(e[2]=a=>Q.enable=a),"default-value":"1","active-value":1,"inactive-value":0},null,8,["modelValue"]),o(H,{content:"计划创建后的默认关闭/开启状态",placement:"top"},{default:m((()=>[o(t,{style:{"margin-left":"5px",color:"#555"}},{default:m((()=>[o(j)])),_:1})])),_:1})])])),_:1}),o(n,{label:"广告类型"},{default:m((()=>[o(P,{type:"primary"},{default:m((()=>e[8]||(e[8]=[y("信息流推广")]))),_:1})])),_:1}),o(n,{label:"推广标的"},{default:m((()=>[o(W,{modelValue:Q.adType,"onUpdate:modelValue":e[3]||(e[3]=a=>Q.adType=a)},{default:m((()=>[o(K,{label:"1"},{default:m((()=>e[9]||(e[9]=[y("笔记")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),o(n,{label:"投放日期"},{default:m((()=>[o(W,{modelValue:Q.dateType,"onUpdate:modelValue":e[4]||(e[4]=a=>Q.dateType=a)},{default:m((()=>[o(K,{label:"0"},{default:m((()=>e[10]||(e[10]=[y("长期投放")]))),_:1}),o(K,{label:"1"},{default:m((()=>e[11]||(e[11]=[y("自定义")]))),_:1})])),_:1},8,["modelValue"]),"1"===Q.dateType?(s(),_(ra,{key:0,style:{"margin-left":"10px"},modelValue:Q.date,"onUpdate:modelValue":e[5]||(e[5]=a=>Q.date=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD","disabled-date":ea},null,8,["modelValue"])):b("",!0)])),_:1}),o(n,{label:"高级设置"},{default:m((()=>[p("div",{class:"setting-header",onClick:aa},[e[12]||(e[12]=p("span",null,"展示投放时间段等设置",-1)),o(t,{class:h({"is-active":Z.value})},{default:m((()=>[o(c(V))])),_:1},8,["class"])]),D(p("div",M,[o(k,{"time-period":X.value,"onUpdate:timePeriod":ia},null,8,["time-period"])],512),[[w,Z.value]])])),_:1})])),_:1},8,["model"])])],512),p("div",q,[e[14]||(e[14]=p("div",{class:"section-title"},"目标和出价方式",-1)),p("div",z,[o(x,{ref_key:"targetComponentRef",ref:ta,"target-data":N.campaignData.unit,"bidding-strategies":N.campaignData.plan.bidding_strategies,"onUpdate:biddingStrategies":la},null,8,["target-data","bidding-strategies"])])],512)])}}}),[["__scopeId","data-v-f2649dd7"]]);export{H as default};
