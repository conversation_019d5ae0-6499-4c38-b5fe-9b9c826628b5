import{u as a}from"./useHandleData-Bxz7m2iB.js";import{I as e,u as l}from"./index-CGZHvcNq.js";import{g as t,u as i,_ as r}from"./UserDrawer.vue_vue_type_script_setup_true_name_UserDrawer_lang-ZNZztpLf.js";import{_ as s}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-Dk8XGRxJ.js";import{T as o}from"./index-YtmDwW09.js";import{S as n}from"./index-5jOAYFIx.js";import{s as u,r as p,a6 as c,p as d,f as m,a as v,c as b,o as f,b as _,h as y,y as g,w as k,aQ as w,d as C,aj as h,ab as j,Z as x,ai as T,_ as L,ak as R,aR as D,K as I,E as q}from"./index-BE6Fh1xm.js";import{k as P,g as S,j as $,d as N,r as O,e as U,B as A,c as B,f as H}from"./user-BL6MRcTt.js";import"./_plugin-vue_export-helper-GSmkUi5K.js";import"./Imgs-xShYOrto.js";import"./upload-BAU8flRh.js";import"./index-C9PKVjyH.js";import"./sortable.esm-DeWNWKFU.js";const E={class:"main-box"},F={class:"table-box"},K={class:"card mb10 pt0 pb0"},Q=u({name:"useSelectFilter"}),V=u({...Q,setup(u){const Q=p(),V=c([{type:"radio",label:"单选",width:80},{type:"index",label:"#",width:80},{prop:"username",label:"用户姓名",width:120},{prop:"gender",label:"性别",width:120,sortable:!0,enum:t},{prop:"idCard",label:"身份证号"},{prop:"email",label:"邮箱"},{prop:"address",label:"居住地址"},{prop:"status",label:"用户状态",width:120,sortable:!0,tag:!0,enum:i},{prop:"createTime",label:"创建时间",width:180,sortable:!0},{prop:"operation",label:"操作",width:330,fixed:"right"}]),Z=c([{title:"用户状态(单)",key:"userStatus",options:[{label:"全部",value:""},{label:"在职",value:"1",icon:"User"},{label:"待培训",value:"2",icon:"Bell"},{label:"待上岗",value:"3",icon:"Clock"},{label:"已离职",value:"4",icon:"CircleClose"},{label:"已退休",value:"5",icon:"CircleCheck"}]},{title:"用户角色(多)",key:"userRole",multiple:!0,options:[]}]);d((()=>z()));const z=async()=>{const{data:a}=await P();Z[1].options=a},G=p({userStatus:"2",userRole:["1","3"]}),J=a=>{q.success("请注意查看请求参数变化 🤔"),Q.value.pageable.pageNum=1,G.value=a},M=c({departmentId:["11"]}),W=a=>{q.success("请注意查看请求参数变化 🤔"),Q.value.pageable.pageNum=1,M.departmentId=a},X=()=>{var a,e,l,t;Q.value.radio=null==(a=Q.value)?void 0:a.tableData[3].id,null==(t=null==(e=Q.value)?void 0:e.element)||t.setCurrentRow(null==(l=Q.value)?void 0:l.tableData[3])};m((()=>{var a;return null==(a=Q.value)?void 0:a.radio}),(()=>{var a,e;return(null==(a=Q.value)?void 0:a.radio)&&q.success(`选中 id 为【${null==(e=Q.value)?void 0:e.radio}】的数据`)}));const Y=async()=>{I.confirm("确认导出用户数据?","温馨提示",{type:"warning"}).then((()=>{var a;return l(U,"用户列表",null==(a=Q.value)?void 0:a.searchParam)}))},aa=p(null),ea=()=>{var a,e;const l={title:"用户",tempApi:U,importApi:A,getTableList:null==(a=Q.value)?void 0:a.getTableList};null==(e=aa.value)||e.acceptParams(l)},la=p(null),ta=(a,e={})=>{var l,t;const i={title:a,isView:"查看"===a,row:{...e},api:"新增"===a?B:"编辑"===a?H:void 0,getTableList:null==(l=Q.value)?void 0:l.getTableList};null==(t=la.value)||t.acceptParams(i)};return(l,t)=>{const i=v("el-button");return f(),b("div",E,[_(o,{title:"部门列表(多选)",multiple:"",label:"name","request-api":g(S),"default-value":M.departmentId,onChange:W},null,8,["request-api","default-value"]),y("div",F,[y("div",K,[_(n,{data:Z,"default-values":G.value,onChange:J},null,8,["data","default-values"])]),_(s,{ref_key:"proTable",ref:Q,"highlight-current-row":"",columns:V,"request-api":g($),"init-param":Object.assign(M,G.value)},{tableHeader:k((()=>[_(i,{type:"primary",icon:g(T),onClick:t[0]||(t[0]=a=>ta("新增"))},{default:k((()=>t[1]||(t[1]=[C("新增用户")]))),_:1},8,["icon"]),_(i,{type:"primary",icon:g(L),onClick:ea},{default:k((()=>t[2]||(t[2]=[C("批量添加用户")]))),_:1},8,["icon"]),_(i,{type:"primary",icon:g(R),onClick:Y},{default:k((()=>t[3]||(t[3]=[C("导出用户数据")]))),_:1},8,["icon"]),_(i,{type:"primary",icon:g(D),onClick:X},{default:k((()=>t[4]||(t[4]=[C("选中第四行")]))),_:1},8,["icon"])])),operation:k((e=>[_(i,{type:"primary",link:"",icon:g(w),onClick:a=>ta("查看",e.row)},{default:k((()=>t[5]||(t[5]=[C("查看")]))),_:2},1032,["icon","onClick"]),_(i,{type:"primary",link:"",icon:g(h),onClick:a=>ta("编辑",e.row)},{default:k((()=>t[6]||(t[6]=[C("编辑")]))),_:2},1032,["icon","onClick"]),_(i,{type:"primary",link:"",icon:g(j),onClick:l=>(async e=>{var l;await a(O,{id:e.id},`重置【${e.username}】用户密码`),null==(l=Q.value)||l.getTableList()})(e.row)},{default:k((()=>t[7]||(t[7]=[C("重置密码")]))),_:2},1032,["icon","onClick"]),_(i,{type:"primary",link:"",icon:g(x),onClick:l=>(async e=>{var l;await a(N,{id:[e.id]},`删除【${e.username}】用户`),null==(l=Q.value)||l.getTableList()})(e.row)},{default:k((()=>t[8]||(t[8]=[C("删除")]))),_:2},1032,["icon","onClick"])])),_:1},8,["columns","request-api","init-param"]),_(r,{ref_key:"drawerRef",ref:la},null,512),_(e,{ref_key:"dialogRef",ref:aa},null,512)])])}}});export{V as default};
