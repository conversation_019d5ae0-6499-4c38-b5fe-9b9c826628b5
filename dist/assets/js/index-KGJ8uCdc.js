import{s as e,r as a,a as l,c as t,o,b as r,g as s,w as m,F as p,i as d,a0 as i,aD as n,y as u,ar as f}from"./index-C2bfFjZ1.js";import{_ as c}from"./_plugin-vue_export-helper-BXFjo1rG.js";const b={class:"card content-box"},_=e({name:"proForm"}),h=c(e({..._,setup(e){let c=a({});const _=a({form:{inline:!1,labelPosition:"right",labelWidth:"80px",size:"default",disabled:!1,labelSuffix:" :"},columns:[{formItem:{label:"用户名",prop:"username",labelWidth:"80px",required:!0},attrs:{typeName:"input",clearable:!0,placeholder:"请输入用户名",disabled:!0}},{formItem:{label:"密码",prop:"password",class:"data"},attrs:{typeName:"input",clearable:!0,autofocus:!0,placeholder:"请输入密码",type:"password"}},{formItem:{label:"邮箱",prop:"email"},attrs:{typeName:"input",placeholder:"请输入邮箱",clearable:!0,style:"width:500px"}}]});return(e,a)=>{const h=l("el-alert"),y=l("el-form-item");return o(),t("div",b,[r(h,{title:"通过 component :is 组件属性 && v-bind 属性透传，可以将 template 中的 html 代码全部改变为 columns 配置项，具体配置请看代码。",type:"warning",closable:!1}),(o(),s(i("el-form"),n(_.value.form,{ref:"proFormRef",model:u(c)}),{default:m((()=>[(o(!0),t(p,null,d(_.value.columns,(e=>(o(),s(i("el-form-item"),n({key:e.prop,ref_for:!0},e.formItem),{default:m((()=>[(o(),s(i(`el-${e.attrs.typeName}`),n({ref_for:!0},e.attrs,{modelValue:u(c)[e.formItem.prop],"onUpdate:modelValue":a=>u(c)[e.formItem.prop]=a}),null,16,["modelValue","onUpdate:modelValue"]))])),_:2},1040)))),128)),r(y,null,{default:m((()=>[f(e.$slots,"operation",{},void 0,!0)])),_:3})])),_:3},16,["model"]))])}}}),[["__scopeId","data-v-63f08e70"]]);export{h as default};
