import{k as e,r as a,f as l,a as t,Q as o,c as s,o as n,b as r,w as i,m as c,g as u,d as p,y as d,h as m,a5 as h,E as v,K as _}from"./index-C2bfFjZ1.js";import{d as f}from"./index-WDAssdXv.js";import{_ as b}from"./_plugin-vue_export-helper-BXFjo1rG.js";f().tabs;const g=localStorage.getItem("Authorization"),y=JSON.parse(localStorage.getItem("LOCAL_PERMISSION_KEY"));let x=!1;y.forEach((e=>{"system:userMenu:importMCN"==e&&(x=!0)}));const C=[{label:"机构简称",prop:"mcn_short_name",search:!0,formatter:(e,a,l,t)=>l||"-",rules:[{required:!0,message:"请输入机构简称",trigger:"blur"}]},{label:"机构属性",search:!0,prop:"mcn_attribute",type:"select",dicUrl:"https://starapi.yinlimedia.com/admin/mcnCustomizeSelect",dicMethod:"get",dicQuery:{},dicHeaders:{Authorization:g},dicFormatter:e=>{let a=[];return e.data.list.forEach(((e,l)=>{a.push({label:e,value:l})})),a},hide:!0,display:!1},{label:"属性",prop:"mcn_attribute"},{label:"类型",prop:"type",hide:!0},{prop:"external_contracting_parties",label:"对外签约主体",hide:!0},{label:"办公地点",hide:!0,prop:"office_location"},{prop:"internal_contact_person",label:"集采对接人"},{prop:"supplier_contact_person",label:"供应商联系人"},{prop:"history_collaboration_reference",label:"引力历史合作参考"},{prop:"service_team_members",label:"服务团队人数"},{prop:"outsourced_execution_service",label:"外包执行服务类目/明细"},{prop:"outsourced_execution_price",label:"执行外包参考报价"},{prop:"outsourced_execution_balance",label:"执行外包结算账期"},{prop:"supplement_policy",label:"补充说明"},{prop:"updated_at",label:"信息更新时间",addDisplay:!1,editDisplay:!1}],w={class:"card content-box"},z={class:"mediaList"},S={slot:"footer",class:"dialog-footer"},R=b({__name:"index",setup(f){const b=a({}),g=a({}),y=a([]),R=a(!1),E=a({}),M={index:!1,labelWidth:140,addBtn:x,align:"center",searchEnter:!0,headerAlign:"center",border:!0,menu:!0,dialogDrag:!0,searchSpan:8,stripe:!0,menuType:"icon",searchMenuPosition:"right",searchIcon:!0,column:C};let D=[],O=a([]),j=a(!1);const I=()=>{j.value=!1,O.value=[],uploadRef.value.clearFiles()},L=e=>{let a=e.raw;D.push(a)},U=()=>{D.length>0?D.forEach((async a=>{let l=new FormData;var t;l.append("file",a),R.value=!0,(t=l,e.post("/importMcnCustomizeMedia",t)).then((e=>{R.value=!1,990==e.code&&(j.value=!1,v.success("上传成功"),k())}))})):v.warning("请选择文件")},V=()=>{O.value=[],j.value=!0,uploadRef.value.clearFiles()},k=()=>{R.value=!0;const a=Object.assign({page:b.value.currentPage,page_size:b.value.pageSize},E.value);y.value=[],(a=>e.post("/mcnCustomizeList",a))(a).then((e=>{const a=e.data;R.value=!1,b.value.total=a.count;const l=a.list;y.value=l}))},A=(a,l,t)=>{var o;(o=Object.assign(a),e.post("/mcnCustomizeAdd",o)).then((()=>{v.success("新增成功"),l(),k()})).catch((()=>{t()}))},F=(a,l,t,o)=>{var s;(s=Object.assign(a),e.post("/mcnCustomizeEdit",s)).then((()=>{v.success("修改成功"),t(),k()})).catch((()=>{o()}))},N=a=>{_.confirm("此操作将永久删除该用户, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{let l={id:a.id};var t;(t=l,e.post("/mcnCustomizeDel",t)).then((e=>{990==e.code&&(v.success("删除成功"),k())}))}))},P=(e,a)=>{a&&a(),E.value=e,b.value.currentPage=1,k()},B=()=>{k(),v.success("刷新成功")};return l((()=>j.value),((e,a)=>{e||(O.value=[])})),(e,a)=>{const l=t("el-button"),v=t("avue-crud"),_=t("el-form-item"),f=t("UploadFilled"),x=t("el-icon"),C=t("el-upload"),E=t("el-form"),D=t("el-dialog"),K=o("permission");return n(),s("div",w,[r(v,{ref:"crud",option:d(M),page:b.value,"onUpdate:page":a[0]||(a[0]=e=>b.value=e),"table-loading":R.value,onOnLoad:k,onRowUpdate:F,onRowSave:A,onRowDel:N,onRefreshChange:B,onSearchReset:P,onSearchChange:P,modelValue:g.value,"onUpdate:modelValue":a[1]||(a[1]=e=>g.value=e),data:y.value},{"menu-left":i((()=>[c((n(),u(l,{type:"primary",icon:"upload",onClick:V},{default:i((()=>a[4]||(a[4]=[p("导入机构")]))),_:1})),[[K,"system:userMenu:importMCNs"]])])),_:1},8,["option","page","table-loading","modelValue","data"]),r(D,{title:"导入文件",modelValue:d(j),"onUpdate:modelValue":a[3]||(a[3]=e=>h(j)?j.value=e:j=e),"append-to-body":!0,width:"40%"},{default:i((()=>[m("div",null,[r(E,{"label-width":"80px"},{default:i((()=>[r(_,{label:"模板下载"},{default:i((()=>[m("div",z,[r(l,{type:"primary",class:"el-button",onClick:a[2]||(a[2]=e=>{return a="https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/mcn_customize.xlsx",void(window.location.href=a);var a})},{default:i((()=>a[5]||(a[5]=[p("下载机构模板 ")]))),_:1})])])),_:1}),r(_,{label:"导入"},{default:i((()=>[r(C,{class:"upload-demo",ref:"uploadRef",limit:1,"auto-upload":!1,"file-list":d(O),"on-remove":e.handleRemoveKL,"on-change":L,"on-exceed":e.onExceed,accept:".xlsx,.xls",drag:"",action:"#"},{default:i((()=>[r(x,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:i((()=>[r(f)])),_:1}),a[6]||(a[6]=m("div",{class:"el-upload__text"},[p("将文件拖到此处，或"),m("em",null,"点击上传")],-1))])),_:1},8,["file-list","on-remove","on-exceed"])])),_:1})])),_:1})]),m("span",S,[r(l,{onClick:I},{default:i((()=>a[7]||(a[7]=[p("取 消")]))),_:1}),r(l,{type:"primary",onClick:U},{default:i((()=>a[8]||(a[8]=[p("确 定")]))),_:1})])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-1655629f"]]);export{R as default};
