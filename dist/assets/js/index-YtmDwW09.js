import{s as e,r as l,as as a,f as t,a as d,c as u,o as r,n as o,h as i,b as s,t as n,w as c,d as p,at as f,y as h,au as m,ar as v,q as k}from"./index-BE6Fh1xm.js";import{_ as y}from"./_plugin-vue_export-helper-GSmkUi5K.js";const b={class:"card filter"},_={key:0,class:"title sle"},g={class:"search"},x={class:"el-tree-node__label"},w=e({name:"TreeFilter"}),V=y(e({...w,props:{requestApi:{},data:{},title:{},id:{default:"id"},label:{default:"label"},multiple:{type:Boolean,default:!1},defaultValue:{}},emits:["change"],setup(e,{expose:y,emit:w}){const V=e,A={children:"children",label:V.label},C=l(),q=l([]),j=l([]),D=l(),M=()=>{V.multiple?D.value=Array.isArray(V.defaultValue)?V.defaultValue:[V.defaultValue]:D.value="string"==typeof V.defaultValue?V.defaultValue:""};a((async()=>{if(M(),V.requestApi){const{data:e}=await V.requestApi();q.value=e,j.value=[{id:"",[V.label]:"全部"},...e]}})),t((()=>V.defaultValue),(()=>k((()=>M()))),{deep:!0,immediate:!0}),t((()=>V.data),(()=>{var e;(null==(e=V.data)?void 0:e.length)&&(q.value=V.data,j.value=[{id:"",[V.label]:"全部"},...V.data])}),{deep:!0,immediate:!0});const O=l("");t(O,(e=>{C.value.filter(e)}));const R=(e,l,a)=>{if(!e)return!0;let t=a.parent,d=[a.label],u=1;for(;u<a.level;)d=[...d,t.label],t=t.parent,u++;return d.some((l=>-1!==l.indexOf(e)))},T=e=>{var l;let a=null==(l=C.value)?void 0:l.store.nodesMap;if(a)for(const t in a)a.hasOwnProperty(t)&&(a[t].expanded=e)},z=w,B=e=>{V.multiple||z("change",e[V.id])},F=()=>{var e;z("change",null==(e=C.value)?void 0:e.getCheckedKeys())};return y({treeData:q,treeAllData:j,treeRef:C}),(e,l)=>{const a=d("el-input"),t=d("More"),k=d("el-icon"),y=d("el-dropdown-item"),w=d("el-dropdown-menu"),V=d("el-dropdown"),M=d("el-scrollbar");return r(),u("div",b,[e.title?(r(),u("h4",_,n(e.title),1)):o("",!0),i("div",g,[s(a,{modelValue:O.value,"onUpdate:modelValue":l[0]||(l[0]=e=>O.value=e),placeholder:"输入关键字进行过滤",clearable:""},null,8,["modelValue"]),s(V,{trigger:"click"},{dropdown:c((()=>[s(w,null,{default:c((()=>[s(y,{onClick:l[1]||(l[1]=e=>T(!0))},{default:c((()=>l[3]||(l[3]=[p("展开全部")]))),_:1}),s(y,{onClick:l[2]||(l[2]=e=>T(!1))},{default:c((()=>l[4]||(l[4]=[p("折叠全部")]))),_:1})])),_:1})])),default:c((()=>[s(k,{size:"20"},{default:c((()=>[s(t)])),_:1})])),_:1})]),s(M,{style:f({height:e.title?"calc(100% - 95px)":"calc(100% - 56px)"})},{default:c((()=>[s(h(m),{ref_key:"treeRef",ref:C,"default-expand-all":"","node-key":e.id,data:e.multiple?q.value:j.value,"show-checkbox":e.multiple,"check-strictly":!1,"current-node-key":e.multiple?"":D.value,"highlight-current":!e.multiple,"expand-on-click-node":!1,"check-on-click-node":e.multiple,props:A,"filter-node-method":R,"default-checked-keys":e.multiple?D.value:[],onNodeClick:B,onCheck:F},{default:c((l=>[i("span",x,[v(e.$slots,"default",{row:l},(()=>[p(n(l.node.label),1)]),!0)])])),_:3},8,["node-key","data","show-checkbox","current-node-key","highlight-current","check-on-click-node","default-checked-keys"])])),_:3},8,["style"])])}}}),[["__scopeId","data-v-8b10751f"]]);export{V as T};
