import{k as e}from"./index-BE6Fh1xm.js";import{d as a}from"./index-kjei_B5n.js";const l=a=>e.post("/kolDandelionTaskNote",a),o=a=>e.post("/sys/departmentsUpdate",a),r=a=>e.post("/sys/departmentsUpdate",a),p=a=>e.post("/dandelionTaskNoteExport",a);let t=a().tabs;const n=[{label:"订单来源",hide:!0,type:"select",search:!0,dicData:[{label:"定制合作",value:0},{label:"共创合作",value:1},{label:"招募合作",value:2},{label:"新芽合作",value:3},{label:"明星合作",value:4}],prop:"note_source"},{label:"笔记信息",search:!0,hide:!0,prop:"note_title"},{label:"达人信息",prop:"daren",slot:!0,width:280},{label:"博主信息",prop:"bozhuxin<PERSON>",slot:!0,width:260},{label:"笔记来源",prop:"note_source",slot:!0,width:260},{label:"跨域项目信息",prop:"note",slot:!0,width:260},{label:"笔记发布时间",prop:"note_publish_date"},{label:"内容标签",prop:"content_tags",slot:!0,search:!0,type:"select",dicData:[{label:"单品",value:0},{label:"合集-默认",value:1},{label:"合集-OOTD",value:2},{label:"合集-好物分享",value:3},{label:"合集-PLOG/VLOG",value:4},{label:"合集-开箱",value:5},{label:"合集-测评",value:6},{label:"合集-教程",value:7}],width:130},{label:"博主报价",prop:"blogger_quote"},{label:"服务费金额",prop:"service_fee_amount"},{label:"是否为优效模式",prop:"is_optimal_effect_mode",formatter:e=>0==e.is_optimal_effect_mode?"是":"否"},{label:"达人昵称",search:!0,hide:!0,prop:"blogger_nickname"},{label:"SPU名称",search:!0,hide:!0,prop:"spu_name"},{label:"笔记ID",search:!0,prop:"note_id"},{label:"报备品牌",search:!0,hide:!0,prop:"report_brand"},{label:"订单号",search:!0,hide:!0,prop:"order_id"},{label:"任务组ID",search:!0,hide:!0,prop:"task_group_id"},{label:"客户名称",search:!0,hide:!0,prop:"customer_name"},{label:"合作名称",search:!0,hide:!0,with:180,prop:"cooperation_name"},{label:"星任务ID",search:!0,hide:!0,prop:"star_task_main_task_id"},{label:"跨域项目ID",search:!0,hide:!0,prop:"cross_domain_project_id"},{label:"曝光量",prop:"exposure_count"},{label:"阅读量",prop:"read_count"},{label:"关注量",prop:"follower_count"},{label:"视频笔记5s播放率",prop:"play_5s_rate"},{label:"图文笔记3s阅读率",prop:"read_3s_rate"},{label:"平均浏览时长",prop:"average_browsing_time"},{label:"阅读UV",prop:"xiaohongxing_cooperation_data_read_uv"},{label:"互动率",prop:"interaction_rate"},{label:"收藏量",prop:"collection_count"},{label:"评论量",prop:"comment_count"},{label:"点赞量",prop:"like_count"},{label:"分享量",prop:"share_count"},{label:"自然曝光量",prop:"organic_exposure_count"},{label:"自然阅读量",prop:"organic_read_count"},{label:"推广曝光量",prop:"promotion_exposure_count"},{label:"推广阅读量",prop:"promotion_read_count"},{label:"加热曝光量",prop:"heating_read_count"},{label:"阅读单价",prop:"read_unit_price"},{label:"互动单价",prop:"interaction_unit_price"},{label:"笔记发布时间",search:!0,hide:!0,type:"daterange",prop:"created_at",align:"center",searchRange:!0,format:"YYYY-MM-DD",span:24,valueFormat:"YYYY-MM-DD",startPlaceholder:"日期开始",endPlaceholder:"日期结束"}],s=e=>({index:!0,gridBtn:!1,addBtn:!1,align:"center",headerAlign:"center",searchLabelWidth:100,border:!0,menu:!1,dialogDrag:!0,searchSpan:6,stripe:!0,menuType:"text",searchMenuPosition:"right",searchIcon:!0,column:"1"==t?n:""});export{p as a,o as d,l as g,s as o,r as u};
