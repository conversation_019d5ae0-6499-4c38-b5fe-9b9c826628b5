import{u as e}from"./useHandleData-k2swCB_O.js";import{_ as a}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-BSD3Vs5K.js";import{s as l,r,a6 as o,a as t,c as n,o as i,b as s,w as p,h as u,y as d,ab as c,d as m,Z as b,t as w,ai as v,aH as y,E as h}from"./index-C2bfFjZ1.js";import{h as _,a as f,j as g,d as x,r as k}from"./user-fUPUQseu.js";import"./index-BMv7HewC.js";import"./_plugin-vue_export-helper-BXFjo1rG.js";import"./sortable.esm-DeWNWKFU.js";const C={class:"table-box"},j=l({...l({name:"complexProTable"}),setup(l){const j=r(),L=o([{type:"selection",width:80},{type:"index",label:"#",width:80},{type:"expand",label:"Expand",width:100},{prop:"base",label:"基本信息",headerRender:e=>s(t("el-button"),{type:"primary",onClick:()=>h.success("我是通过 tsx 语法渲染的表头")},{default:()=>[e.column.label]}),_children:[{prop:"username",label:"用户姓名",width:110},{prop:"user.detail.age",label:"年龄",width:100},{prop:"gender",label:"性别",width:100,enum:_,fieldNames:{label:"genderLabel",value:"genderValue"}},{prop:"details",label:"详细资料",_children:[{prop:"idCard",label:"身份证号"},{prop:"email",label:"邮箱"},{prop:"address",label:"居住地址"}]}]},{prop:"status",label:"用户状态",tag:!0,enum:f,fieldNames:{label:"userLabel",value:"userStatus"}},{prop:"createTime",label:"创建时间",width:200},{prop:"operation",label:"操作",fixed:"right",width:230}]),T=()=>{var e,a,l,r,o,t;null==(l=null==(e=j.value)?void 0:e.element)||l.setCurrentRow(null==(a=j.value)?void 0:a.tableData[4]),null==(t=null==(r=j.value)?void 0:r.element)||t.toggleRowSelection(null==(o=j.value)?void 0:o.tableData[4],!0)},S=e=>{const{columns:a}=e,l=[];return a.forEach(((e,a)=>{if(0===a)return l[a]="合计";l[a]="N/A"})),l},I=({rowIndex:e,columnIndex:a})=>{if(3===a)return e%2==0?{rowspan:2,colspan:1}:{rowspan:0,colspan:0}},R=({rowIndex:e})=>2===e?"warning-row":6===e?"success-row":"",D=(e,a)=>{"radio"!=a.property&&"operation"!=a.property&&h.success("当前行被点击了！")};return(l,r)=>{const o=t("el-button");return i(),n("div",C,[s(a,{ref_key:"proTable",ref:j,title:"用户列表","highlight-current-row":"",columns:L,"request-api":d(g),"row-class-name":R,"span-method":I,"show-summary":!0,"summary-method":S,onRowClick:D},{tableHeader:p((a=>{var l,t;return[s(o,{type:"primary",icon:d(v),onClick:null==(t=null==(l=j.value)?void 0:l.element)?void 0:t.toggleAllSelection},{default:p((()=>r[0]||(r[0]=[m("全选 / 全不选")]))),_:1},8,["icon","onClick"]),s(o,{type:"primary",icon:d(y),plain:"",onClick:T},{default:p((()=>r[1]||(r[1]=[m("选中第五行")]))),_:1},8,["icon"]),s(o,{type:"danger",icon:d(b),plain:"",disabled:!a.isSelected,onClick:l=>(async a=>{var l,r;await e(x,{id:a},"删除所选用户信息"),null==(l=j.value)||l.clearSelection(),null==(r=j.value)||r.getTableList()})(a.selectedListIds)},{default:p((()=>r[2]||(r[2]=[m(" 批量删除用户 ")]))),_:2},1032,["icon","disabled","onClick"])]})),expand:p((e=>[m(w(e.row),1)])),operation:p((a=>[s(o,{type:"primary",link:"",icon:d(c),onClick:l=>(async a=>{var l;await e(k,{id:a.id},`重置【${a.username}】用户密码`),null==(l=j.value)||l.getTableList()})(a.row)},{default:p((()=>r[3]||(r[3]=[m("重置密码")]))),_:2},1032,["icon","onClick"]),s(o,{type:"primary",link:"",icon:d(b),onClick:l=>(async a=>{var l;await e(x,{id:[a.id]},`删除【${a.username}】用户`),null==(l=j.value)||l.getTableList()})(a.row)},{default:p((()=>r[4]||(r[4]=[m("删除")]))),_:2},1032,["icon","onClick"])])),append:p((()=>r[5]||(r[5]=[u("span",{style:{color:"var(--el-color-primary)"}},"我是插入在表格最后的内容。若表格有合计行，该内容会位于合计行之上。",-1)]))),_:1},8,["columns","request-api"])])}}});export{j as default};
