import{s as e,a6 as l,r as a,a as o,g as t,o as r,w as u,b as n,h as i,c as s,n as d,d as p,m,v as c,E as _}from"./index-BE6Fh1xm.js";import{u as b}from"./kol-DDYIIRhL.js";const f={class:"flex"},w={key:0},v={class:"flex space-between ml2 pl2 pr2 hoverClass"},V=["href"],g=e({name:"mcnList"}),y=e({...g,setup(e,{expose:g}){const y=l({mcn_short_name:[{required:!0,message:"机构简称不可为空"}],mcn_name:[{required:!0,message:"机构全称不可为空"}]});let h={};const x=a(null),U=a(!1),k=a({isView:!1,title:"",row:{}}),j=(e,l)=>{let a=e.file;h=a,a&&(()=>{let e=new FormData;e.append("file",h),loading.value=!0,b(e).then((e=>{loading.value=!1,state.formData.annex_url=e.data,_.success("上传成功"),state.uploadFileList=e.data}))})()},q=a(),M=()=>{q.value.validate((async e=>{if(e)try{await k.value.api(k.value.row),_.success({message:`${k.value.title}成功！`}),k.value.getTableList(),U.value=!1}catch(l){}}))};return g({acceptParams:e=>{k.value=e,U.value=!0}}),(e,l)=>{const a=o("el-input"),_=o("el-form-item"),b=o("el-button"),g=o("el-upload"),h=o("Delete"),C=o("el-icon"),F=o("el-form"),L=o("el-popconfirm"),D=o("el-drawer");return r(),t(D,{modelValue:U.value,"onUpdate:modelValue":l[24]||(l[24]=e=>U.value=e),"destroy-on-close":!0,size:"450px",title:`${k.value.title}机构`},{footer:u((()=>[n(L,{title:"确定删除机构下的所有达人么?","confirm-button-type":"danger","cancel-button-type":"primary",onConfirm:e.deleteMcnKol},{reference:u((()=>[n(b,{slot:"",type:"danger"},{default:u((()=>l[26]||(l[26]=[p("清空达人")]))),_:1})])),_:1},8,["onConfirm"]),n(b,{onClick:l[23]||(l[23]=e=>U.value=!1)},{default:u((()=>l[27]||(l[27]=[p("取消")]))),_:1}),m(n(b,{type:"primary",onClick:M},{default:u((()=>l[28]||(l[28]=[p("确定")]))),_:1},512),[[c,!k.value.isView]])])),default:u((()=>[n(F,{ref_key:"ruleFormRef",ref:q,"label-width":"100px","label-suffix":" :",rules:y,disabled:k.value.isView,model:k.value.row,"hide-required-asterisk":k.value.isView},{default:u((()=>[n(_,{label:"机构简称",prop:"mcn_short_name",class:"required label-right-align"},{default:u((()=>[n(a,{type:"text",modelValue:k.value.row.mcn_short_name,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value.row.mcn_short_name=e)},null,8,["modelValue"])])),_:1}),n(_,{label:"机构全称",prop:"mcn_name",class:"required label-right-align"},{default:u((()=>[n(a,{type:"textarea",modelValue:k.value.row.mcn_name,"onUpdate:modelValue":l[1]||(l[1]=e=>k.value.row.mcn_name=e),rows:"1"},null,8,["modelValue"])])),_:1}),n(_,{label:"返点政策(蒲公英)",prop:"dandelion_policy",class:"label-right-align"},{default:u((()=>[n(a,{min:0,number:"true",modelValue:k.value.row.dandelion_policy,"onUpdate:modelValue":l[2]||(l[2]=e=>k.value.row.dandelion_policy=e),modelModifiers:{number:!0},type:"number",class:"input-num",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"返点政策(花火)",prop:"fireworks_policy",class:"label-right-align"},{default:u((()=>[n(a,{min:0,number:"true",modelValue:k.value.row.fireworks_policy,"onUpdate:modelValue":l[3]||(l[3]=e=>k.value.row.fireworks_policy=e),modelModifiers:{number:!0},type:"number",class:"input-num",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"返点政策(聚星)",prop:"juxing_policy",class:"label-right-align"},{default:u((()=>[n(a,{min:0,number:"true",modelValue:k.value.row.juxing_policy,"onUpdate:modelValue":l[4]||(l[4]=e=>k.value.row.juxing_policy=e),modelModifiers:{number:!0},type:"number",class:"input-num",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"返点政策(星图)",prop:"star_policy",class:"label-right-align"},{default:u((()=>[n(a,{min:0,number:"true",modelValue:k.value.row.star_policy,"onUpdate:modelValue":l[5]||(l[5]=e=>k.value.row.star_policy=e),modelModifiers:{number:!0},type:"number",class:"input-num",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"主营平台",prop:"main_platform",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.main_platform,"onUpdate:modelValue":l[6]||(l[6]=e=>k.value.row.main_platform=e),type:"text",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"属地",prop:"possession",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.possession,"onUpdate:modelValue":l[7]||(l[7]=e=>k.value.row.possession=e),type:"text",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"微信群主",prop:"wechat_leader",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.wechat_leader,"onUpdate:modelValue":l[8]||(l[8]=e=>k.value.row.wechat_leader=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"合作总量",prop:"coopertive_total",class:"label-right-align"},{default:u((()=>[n(a,{min:0,number:"true",modelValue:k.value.row.coopertive_total,"onUpdate:modelValue":l[9]||(l[9]=e=>k.value.row.coopertive_total=e),modelModifiers:{number:!0},type:"number",class:"input-num",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"微信群名",prop:"wechat_name",class:"label-right-align"},{default:u((()=>[n(a,{type:"text",modelValue:k.value.row.wechat_name,"onUpdate:modelValue":l[10]||(l[10]=e=>k.value.row.wechat_name=e)},null,8,["modelValue"])])),_:1}),n(_,{label:"内部对接人",prop:"internal_contact_person",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.internal_contact_person,"onUpdate:modelValue":l[11]||(l[11]=e=>k.value.row.internal_contact_person=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"机构对接人",prop:"mcn_contact_person",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.mcn_contact_person,"onUpdate:modelValue":l[12]||(l[12]=e=>k.value.row.mcn_contact_person=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"合作客户",prop:"cooperative_clients",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.cooperative_clients,"onUpdate:modelValue":l[13]||(l[13]=e=>k.value.row.cooperative_clients=e),type:"text",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"达人类型",prop:"talent_type",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.talent_type,"onUpdate:modelValue":l[14]||(l[14]=e=>k.value.row.talent_type=e),type:"text",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"合作平台",prop:"cooperative_platform",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.cooperative_platform,"onUpdate:modelValue":l[15]||(l[15]=e=>k.value.row.cooperative_platform=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"机构评级",prop:"ext_field1",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.ext_field1,"onUpdate:modelValue":l[16]||(l[16]=e=>k.value.row.ext_field1=e),type:"textarea",rows:"2",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"机构属性",prop:"label_attribute",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.label_attribute,"onUpdate:modelValue":l[17]||(l[17]=e=>k.value.row.label_attribute=e),type:"textarea",rows:"2",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:" 核心优势",prop:"suggest_reason",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.suggest_reason,"onUpdate:modelValue":l[18]||(l[18]=e=>k.value.row.suggest_reason=e),id:"suggestreason",type:"textarea",rows:"2",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"备注2",prop:"ext_field2",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.ext_field2,"onUpdate:modelValue":l[19]||(l[19]=e=>k.value.row.ext_field2=e),type:"textarea",rows:"2",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"KA_政策",prop:"ka_policy",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.ka_policy,"onUpdate:modelValue":l[20]||(l[20]=e=>k.value.row.ka_policy=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"框架政策",prop:"framework_policy",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.framework_policy,"onUpdate:modelValue":l[21]||(l[21]=e=>k.value.row.framework_policy=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"竞对政策",prop:"competition_policy",class:"label-right-align"},{default:u((()=>[n(a,{modelValue:k.value.row.competition_policy,"onUpdate:modelValue":l[22]||(l[22]=e=>k.value.row.competition_policy=e),type:"textarea",rows:"1",clearable:""},null,8,["modelValue"])])),_:1}),n(_,{label:"附件",class:"label-right-align"},{default:u((()=>[i("div",f,[n(g,{class:"upload-demo",ref_key:"uploadRef",ref:x,"show-file-list":!1,"http-request":j,accept:".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.jpg,.jpeg,.png",action:"#"},{default:u((()=>[n(b,{size:"small",type:"primary"},{default:u((()=>l[25]||(l[25]=[p("点击上传")]))),_:1})])),_:1},512),e.uploadFileList?(r(),s("div",w,[i("div",v,[i("a",{href:e.uploadFileList},"附件下载",8,V),n(C,{class:"ml-2"},{default:u((()=>[n(h)])),_:1})])])):d("",!0)])])),_:1})])),_:1},8,["rules","disabled","model","hide-required-asterisk"])])),_:1},8,["modelValue","title"])}}});export{y as default};
