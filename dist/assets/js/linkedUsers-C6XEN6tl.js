import{d as e,e as a,f as t,h as i,i as l,j as s,k as r}from"./resource-B9SEwZSg.js";import{u as o,a4 as n,r as d,f as c,p as v,a as p,c as u,o as h,h as m,b,w as f,F as y,i as g,y as _,g as x,a5 as w,z as k,t as F,d as z,C as A,n as L}from"./index-C2bfFjZ1.js";import{_ as C}from"./_plugin-vue_export-helper-BXFjo1rG.js";const O={class:"author-page-content"},S={class:"flex justify-end"},D={class:"tab-card-group link-card business-card"},B={class:"relative-ratio"},E={class:"ratio-value"},$={class:"link-info"},I={class:"link-label"},T={class:"link-value"},W={class:"relative-ratio"},j={class:"ratio-value"},N={class:"link-info"},P={class:"link-label"},V={class:"link-value"},U={class:"relative-ratio"},q={class:"ratio-value"},M={class:"link-info"},R={class:"link-label"},Z={class:"link-value"},Q={class:"relative-ratio"},Y={class:"ratio-value"},G={class:"link-info"},H={class:"link-label"},J={class:"link-value"},K={class:"relative-ratio"},X={class:"ratio-value"},ee={class:"link-info"},ae={class:"link-label"},te={class:"link-value"},ie={class:"card-panel module-card author-link-struct mt-4"},le={class:"card-panel-body"},se={class:"content-container"},re={class:"right"},oe={class:"dsp-flex"},ne={class:"type-title"},de={style:{position:"relative"},class:"mt-4"},ce={class:"card-panel module-card fans-portrait",style:{}},ve={class:"title-wrapper"},pe={class:"operation"},ue={class:"card-panel-body"},he={class:"charts"},me=C({__name:"linkedUsers",props:["kolDetail"],setup(C){const me=o().query.platform_uid,{proxy:be}=n();var fe=["#38a1ff","#fe346e"];let ye=d("link"),ge=["连接用户数","粉丝数"],_e=[],xe=d(1);d(1);let we=d(""),ke=d([]),Fe=d([]),ze=d({}),Ae=d([]);var Le=d({});let Ce={};d({});const Oe=e=>{let a;return a=100*Number(e)%1==0?(100*Number(e)).toFixed(0):(100*Number(e)).toFixed(2),"NaN"==a?"-":a},Se=(e,a,t)=>{const i=document.getElementById(a),l=be.$echarts.init(i),s={color:"#38a1ff",xAxis:{type:"category",data:t},yAxis:{type:"value",name:"数量"},series:[{data:e,type:"bar"}]};s&&l.setOption(s)},De=e=>{Te()},Be=()=>{e({platform_uid:me}).then((e=>{Ae.value=e.data,Le.value=e.data.filter((e=>e.type==xe.value))[0],Ee()}))},Ee=()=>{if(Le.value.gender){let e=[];Le.value.gender.map((a=>{"female"==a.distribution_key&&e.push({value:a.distribution_value,name:"女性"}),"male"==a.distribution_key&&e.push({value:a.distribution_value,name:"男性"})})),(e=>{const a=document.getElementById("base-chart1"),t=be.$echarts.init(a);var i;(i={color:fe,tooltip:{trigger:"item"},legend:{bottom:"-0%",left:"center"},series:[{type:"pie",radius:["40%","70%"],label:{show:!1,position:"center"},emphasis:{},labelLine:{show:!1},data:e}]})&&t.setOption(i)})(e)}if(Le.value.age){let e=[],a=[];Le.value.age.map((t=>{e.push(t.distribution_key),a.push(t.distribution_value)})),Se(a,"base-chart2",e)}if(Le.value.province){let e=[],a=Le.value.province[0].distribution_value,t=Le.value.province[0].distribution_value;Le.value.province.map((i=>{i.distribution_value>a&&(a=i.distribution_value),i.distribution_value<t&&(t=i.distribution_value),e.push({name:i.distribution_key,value:i.distribution_value})})),((e,a,t)=>{const i=document.getElementById("base-chart3"),l=be.$echarts.init(i),s={color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],backgroundColor:"#ffffff",tooltip:{show:!0,trigger:"item",alwaysShowContent:!1,backgroundColor:"#0C121C",borderColor:"rgba(0, 0, 0, 0.16);",hideDelay:100,triggerOn:"mousemove",enterable:!0,textStyle:{color:"#DADADA",fontSize:"12",width:20,height:30,overflow:"break"},showDelay:100},visualMap:{min:a,max:t,calculable:!0,realtime:!1,inRange:{color:["#DCF3FF","#38A1FF"]}},series:[{type:"map",map:"china",zoom:.8,hover:{shapeStyle:{fill:"#FFA538"},label:{color:"#333333"}},itemStyle:{normal:{borderColor:"#ffffff",borderWidth:1,areaColor:{type:"radial",x:.5,y:.5,r:2,colorStops:[{offset:0,color:"#38A1FF"},{offset:1,color:"#DCF3FF"}],globalCoord:!1}}},data:e,showEffectOn:"render",rippleEffect:{brushType:"stroke"},hoverAnimation:!0,zlevel:1}]};s&&l.setOption(s)})(e,t,a);let i=Le.value.province.sort(((e,a)=>a.distribution_value-e.distribution_value)).slice(0,10),l=[],s=[];i.map((e=>{l.push(e.distribution_value),s.push(e.distribution_key)})),Se(l,"base-chart4",s)}if(Le.value.city_level){let e=[],a=[];Le.value.city_level.map((t=>{a.push(t.distribution_value),e.push(t.distribution_key)})),Se(a,"base-chart5",e)}if(Le.value.interest){let e=[],a=[];Le.value.interest.map((t=>{a.push(t.distribution_value),e.push(t.distribution_key)})),Se(a,"base-chart6",e)}if(Le.value.eight_people){let e=[],a=[];Le.value.eight_people.map((t=>{a.push(t.distribution_value),e.push(t.distribution_key)})),Se(a,"base-chart7",e)}if(Le.value.device_brand){let e=[],a=0;Le.value.device_brand.map((e=>{a+=Number(e.distribution_value)})),Le.value.device_brand.map((t=>{e.push({name:t.distribution_key,value:t.distribution_value/a*100})})),(e=>{const a=document.getElementById("base-chart8"),t=be.$echarts.init(a);var i={colorBy:"data",visualMap:{show:!1,calculable:!0,realtime:!1,min:0,max:100,inRange:{color:"#3c6388",colorLightness:[.4,2]},range:[0,100]},axisLabel:{formatter:function(e){return 100*e+"%"}},series:[{name:"Access From",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#999"},labelLine:{lineStyle:{color:"#38A1FF"},smooth:.2,length:10,length2:20},data:e}]};i&&t.setOption(i)})(e)}},$e=()=>{a({platform_uid:me}).then((e=>{if(Ce=e.data,Ce.length>0){let e=Ce.filter((e=>"个人"==e.video_type))[0].cnt,a=Ce.filter((e=>"星图"==e.video_type))[0].cnt;if(e>0||a>0){((e,a,t)=>{const i=document.getElementById("pie-chart"),l=be.$echarts.init(i);var s;(s={tooltip:{trigger:"item"},legend:{show:!1},series:[{type:"pie",radius:["40%","70%"],color:["#DCF3FF","#38A1FF"],avoidLabelOverlap:!1,innerSize:"80%",encode:{name:"name",value:"value",color:["#DCF3FF","#38A1FF"]},connectLine:{stroke:"#38A1FF"},label:{position:"inside",distance:20,alignTo:"edge",margin:50,offsetY:8,color:"#333333",formatter:e=>e.name+"\n"+(e.value/(a+t)*100).toFixed(2)+"%"},labelLine:{show:!1},startAngle:-90,data:e}]})&&l.setOption(s)})([{name:"个人",value:e},{name:"星图",value:a}],e,a)}}let a=[],t=[],i=[],l=[];e.data.length>0&&(e.data.map((e=>{a.includes(e.level1_label)||(a.push(e.level1_label),t.push(e.level1_label_cnt),l.push(e.level2_label),i.push(e.level2_label_cnt))})),((e,a)=>{const t=document.getElementById("bar-trade1-chat"),i=be.$echarts.init(t);var l;(l={color:"#38a1ff",tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"value"}],yAxis:[{type:"category",data:e,axisTick:{alignWithLabel:!0}}],dataZoom:[{show:!1,type:"inside",orient:"vertical"}],series:[{name:"Direct",type:"bar",barWidth:"60%",data:a}]})&&i.setOption(l)})(a,t),((e,a)=>{const t=document.getElementById("bar-trade2-chat"),i=be.$echarts.init(t);var l;(l={color:"#38a1ff",tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:e,axisTick:{alignWithLabel:!0}}],yAxis:[{type:"value"}],series:[{name:"Direct",type:"bar",barWidth:"60%",data:a}]})&&i.setOption(l)})(l,i))}))},Ie=()=>{t({platform_uid:me}).then((e=>{ze.value=e.data;let a=[],t=[],i=[{type:1,name:"了解"},{type:2,name:"兴趣"},{type:3,name:"喜欢"},{type:4,name:"追随"}];ze.value.map((e=>{5!==e.type&&(t.push({value:e.same_industry_median?e.same_industry_median:0,name:i[e.type-1].name}),a.push({value:e.value?e.value:0,name:i[e.type-1].name}))})),((e,a)=>{const t=document.getElementById("funnel-chart"),i=be.$echarts.init(t);var l;(l={series:[{type:"funnel",color:"#38a1ff",left:"10%",width:"80%",gap:8,orient:"horizontal",label:{position:"leftBottom",formatter:function(e){return`${e.data.name}:${e.percent}%`},color:"#333",align:"left",fontSize:"12"},labelLine:{show:!0,length:30,lineStyle:{type:"dashed",cap:"square",join:"miter",color:"#999"}},labelLayout:e=>({y:"450"}),itemStyle:{opacity:.7,borderColor:"#ffffff"},data:e,z:99},{name:"",type:"funnel",left:"10%",width:"80%",gap:8,color:"#fe346e",maxSize:"80%",orient:"horizontal",label:{show:!1},itemStyle:{borderColor:"#fe346e",borderWidth:1},data:a,z:100}]})&&i.setOption(l)})(a,t)}))},Te=()=>{l({platform_uid:me,industry_id:""==we.value?"0":we.value,type:1}).then((e=>{Fe.value=e.data}))},We=()=>{s({platform_uid:me,industry_id:""==we.value?"0":we.value,type:1}).then((e=>{let a=e.data.map((e=>e.link_value));_e.value=e.data.map((e=>e.date)),r({platform_uid:me,type:1}).then((e=>{let t=e.data.map((e=>e.daily_fans));0==_e.value.length&&(_e.value=e.data.map((e=>e.date))),((e,a,t)=>{const i=document.getElementById("line-chart"),l=be.$echarts.init(i);var s;(s={color:fe,tooltip:{trigger:"axis",axisPointer:{}},legend:{},grid:{top:70,bottom:50},xAxis:[{type:"category",axisTick:{alignWithLabel:!1},axisLine:{onZero:!1,show:!1,lineStyle:{color:fe[0]}},axisTick:{show:!1},axisPointer:{},data:e},{type:"category",axisTick:{alignWithLabel:!1,show:!1},axisLine:{show:!1},axisPointer:{},data:{}}],yAxis:[{type:"value"}],series:[{name:ge[0],type:"line",smooth:!0,emphasis:{focus:"series"},data:a},{name:ge[1],type:"line",smooth:!0,emphasis:{focus:"series"},data:t}]})&&l.setOption(s)})(_e.value,a,t)}))}))};return c((()=>xe.value),((e,a)=>{Be()})),v((()=>{Be(),$e(),Ie(),i({platform_uid:me}).then((e=>{ke.value=e.data})),Te(),We()})),(e,a)=>{var t,i,l,s,r,o,n,d,c,v,me,be,fe,ge,_e,Ae,Le,Ce,Se,Be,Ee;const $e=p("el-option"),Ie=p("el-select"),Te=p("QuestionFilled"),We=p("el-icon"),je=p("el-tooltip"),Ne=p("el-radio-button"),Pe=p("el-radio-group");return h(),u("div",O,[m("div",null,[m("div",S,[b(Ie,{modelValue:_(we),"onUpdate:modelValue":a[0]||(a[0]=e=>w(we)?we.value=e:we=e),size:"small",placeholder:"请选择行业",style:{width:"300px"},onChange:De},{default:f((()=>[b($e,{label:"所有行业",value:""}),(h(!0),u(y,null,g(_(ke),(e=>(h(),x($e,{label:e.name,value:e.industry_id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])]),m("div",D,[m("div",{class:k(["tab-card-item link-card-item","link"==_(ye)?"is-active":""])},[m("div",B,[a[2]||(a[2]=m("span",{class:"ratio-label"},"30天环比",-1)),m("span",E,F((null==(t=_(Fe)[4])?void 0:t.relative_ratio)?Oe(null==(i=_(Fe)[4])?void 0:i.relative_ratio)+"%":"-"),1)]),m("div",$,[b(je,{class:"item el-tooltip",effect:"dark",content:"近30天有效观看了视频的全部用户数",placement:"top"},{default:f((()=>[m("span",I,[a[3]||(a[3]=z("连接用户数")),b(We,{class:"light"},{default:f((()=>[b(Te)])),_:1})])])),_:1}),m("div",T,F((null==(l=_(Fe)[4])?void 0:l.value)?null==(s=_(Fe)[4])?void 0:s.value:"-"),1)])],2),m("div",{class:k(["tab-card-item link-card-item","know"==_(ye)?"is-active":""])},[m("div",W,[a[4]||(a[4]=m("span",{class:"ratio-label"},"30天环比",-1)),m("span",j,F((null==(r=_(Fe)[0])?void 0:r.relative_ratio)?Oe(null==(o=_(Fe)[0])?void 0:o.relative_ratio)+"%":"-"),1)]),m("div",N,[b(je,{class:"item el-tooltip",effect:"dark",content:"近30天内仅观看了达人视频1次的用户数",placement:"top"},{default:f((()=>[m("span",P,[a[5]||(a[5]=z("了解")),b(We,{class:"light"},{default:f((()=>[b(Te)])),_:1})])])),_:1}),m("div",V,F((null==(n=_(Fe)[0])?void 0:n.value)?null==(d=_(Fe)[0])?void 0:d.value:"-"),1)])],2),m("div",{class:k(["tab-card-item link-card-item","interest"==_(ye)?"is-active":""])},[m("div",U,[a[6]||(a[6]=m("span",{class:"ratio-label"},"30天环比",-1)),m("span",q,F((null==(c=_(Fe)[1])?void 0:c.relative_ratio)?Oe(null==(v=_(Fe)[1])?void 0:v.relative_ratio)+"%":"-"),1)]),m("div",M,[b(je,{class:"item el-tooltip",effect:"dark",content:"近30天内与达人有互动的用户数",placement:"top"},{default:f((()=>[m("span",R,[a[7]||(a[7]=z("兴趣")),b(We,{class:"light"},{default:f((()=>[b(Te)])),_:1})])])),_:1}),m("div",Z,F((null==(me=_(Fe)[1])?void 0:me.value)?null==(be=_(Fe)[1])?void 0:be.value:"-"),1)])],2),m("div",{class:k(["tab-card-item link-card-item","like"==_(ye)?"is-active":""])},[m("div",Q,[a[8]||(a[8]=m("span",{class:"ratio-label"},"30天环比",-1)),m("span",Y,F((null==(fe=_(Fe)[2])?void 0:fe.relative_ratio)?Oe(null==(ge=_(Fe)[2])?void 0:ge.relative_ratio)+"%":"-"),1)]),m("div",G,[b(je,{class:"item el-tooltip",effect:"dark",content:"近30天内对达人有持续关注的用户数。持续关注的具体口径",placement:"top"},{default:f((()=>[m("span",H,[a[9]||(a[9]=z("喜欢")),b(We,{class:"light"},{default:f((()=>[b(Te)])),_:1})])])),_:1}),m("div",J,F((null==(_e=_(Fe)[2])?void 0:_e.value)?null==(Ae=_(Fe)[2])?void 0:Ae.value:"-"),1)])],2),m("div",{class:k(["tab-card-item link-card-item","follow"==_(ye)?"is-active":""])},[m("div",K,[a[10]||(a[10]=m("span",{class:"ratio-label"},"30天环比",-1)),m("span",X,F((null==(Le=_(Fe)[3])?void 0:Le.relative_ratio)?Oe(null==(Ce=_(Fe)[3])?void 0:Ce.relative_ratio)+"%":"-"),1)]),m("div",ee,[b(je,{class:"item el-tooltip",effect:"dark",content:"近30天内对达人有追随行为的用户数。追随行为的具体口径",placement:"top"},{default:f((()=>[m("span",ae,[a[11]||(a[11]=z("追随")),b(We,{class:"light"},{default:f((()=>[b(Te)])),_:1})])])),_:1}),m("div",te,F((null==(Se=_(Fe)[3])?void 0:Se.value)?null==(Be=_(Fe)[3])?void 0:Be.value:"-"),1)])],2)]),a[29]||(a[29]=m("div",{class:"card-panel module-card"},[m("div",{class:"title-wrapper"},[m("div",{class:"title"},"连接用户趋势"),m("span",{class:"desc"}),m("div",{class:"operation"})]),m("div",{class:"sub-title"},[m("div",{class:"divider el-divider el-divider--horizontal"})]),m("div",{class:"card-panel-body"},[m("div",{class:"base-chart",id:"line-chart","size-sensor-id":"3",style:{width:"100%",height:"300px"}})])],-1)),m("div",ie,[a[15]||(a[15]=A('<div class="title-wrapper" data-v-306af20b><div class="title" data-v-306af20b>连接用户</div><span class="desc" data-v-306af20b></span><div class="operation" data-v-306af20b></div></div><div class="sub-title" data-v-306af20b><div class="divider el-divider el-divider--horizontal" data-v-306af20b></div></div>',2)),m("div",le,[m("div",se,[a[14]||(a[14]=m("div",{class:"left"},[m("div",{class:"horizontal-funnel"},[m("div",{class:"base-chart","size-sensor-id":"2",style:{position:"relative"}},[m("div",null,[m("div",{class:"describe"},[m("div",null,[m("span",{class:"dot compare"}),m("span",null,"行业均值")]),m("div",{class:"median ml-2"},[m("span",{class:"dot full"}),m("span",null,"达人结构")])])]),m("div",{class:"lightcharts-container",id:"funnel-chart",style:{position:"relative",width:"auto",height:"300px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])])],-1)),m("div",re,[m("div",oe,[m("div",ne,F(null==(Ee=_(ze)[0])?void 0:Ee.author_link_type)+"型达人",1)]),m("section",null,[a[13]||(a[13]=m("div",{class:"section-title"},"总结",-1)),m("p",null,[a[12]||(a[12]=z(" 月度连接总用户数 ")),m("strong",null,F((C.kolDetail.link_cnt/1e4).toFixed(2))+" w",1)])])])])])]),a[30]||(a[30]=m("div",{style:{position:"relative"},class:"mt-4"},[m("div",null,[m("div",{class:"card-panel module-card",style:{}},[m("div",{class:"title-wrapper"},[m("div",{class:"title"},"连接用户来源"),m("span",{class:"desc"}),m("div",{class:"operation"})]),m("div",{class:"card-panel-body"},[m("div",{class:"users-source"},[m("div",{class:"char-card triangle"},[m("span",{class:"title"},"流量来源"),m("div",{class:"base-chart","size-sensor-id":"4",style:{width:"222px",height:"280px"},id:"pie-chart"})]),m("div",{class:"char-card triangle"},[m("div",{class:"char-header"},[m("span",{class:"title"},"内容一级标签")]),m("div",{class:"base-chart age-chart","size-sensor-id":"5",id:"bar-trade1-chat",style:{width:"500px",height:"280px"}})]),m("div",{class:"char-card"},[m("div",{class:"char-header"},[m("span",{class:"title"},"内容二级标签")]),m("div",{class:"base-chart age-chart",id:"bar-trade2-chat",style:{width:"500px",height:"280px"},"size-sensor-id":"6"})])])])])])],-1)),m("div",de,[m("div",null,[m("div",ce,[m("div",ve,[a[19]||(a[19]=m("div",{class:"title"},"连接用户画像",-1)),a[20]||(a[20]=m("span",{class:"desc"},null,-1)),m("div",pe,[b(Pe,{modelValue:_(xe),"onUpdate:modelValue":a[1]||(a[1]=e=>w(xe)?xe.value=e:xe=e)},{default:f((()=>[b(Ne,{label:3},{default:f((()=>a[16]||(a[16]=[z("观众画像")]))),_:1}),b(Ne,{label:1},{default:f((()=>a[17]||(a[17]=[z("粉丝画像")]))),_:1}),b(Ne,{label:5},{default:f((()=>a[18]||(a[18]=[z("铁粉画像")]))),_:1})])),_:1},8,["modelValue"])])]),m("div",ue,[m("div",he,[5!==_(xe)?(h(),u(y,{key:0},[a[21]||(a[21]=m("div",{class:"chart-container gender-container"},[m("p",{class:"title"},"性别分布"),m("div",{class:"base-chart gender-chart","size-sensor-id":"19",style:{position:"relative"}},[m("div",{class:"lightcharts-container",style:{width:"366px",height:"235px"},id:"base-chart1"})])],-1)),a[22]||(a[22]=m("div",{class:"chart-container"},[m("p",{class:"title"},"年龄分布"),m("div",{class:"base-chart age-chart","size-sensor-id":"20",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart2",style:{position:"relative",width:"auto",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px",cursor:"auto"}})])],-1))],64)):L("",!0),a[23]||(a[23]=m("div",{class:"chart-container"},[m("p",{class:"title"},"全国省份分布"),m("div",{class:"base-chart province-chart","size-sensor-id":"21",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart3",style:{position:"relative",width:"366px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])],-1)),a[24]||(a[24]=m("div",{class:"chart-container"},[m("p",{class:"title"}," 地域占比 TOP10 "),m("div",{class:"base-chart region-chart","size-sensor-id":"22",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart4",style:{position:"relative",width:"500px",height:"263px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])],-1)),a[25]||(a[25]=m("div",{class:"chart-container"},[m("p",{class:"title"},"城市等级分布"),m("div",{class:"base-chart city-level-chart","size-sensor-id":"23",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart5",style:{position:"relative",width:"500px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])],-1)),a[26]||(a[26]=m("div",{class:"chart-container"},[m("p",{class:"title"},"兴趣分布"),m("div",{class:"base-chart interest-chart","size-sensor-id":"12",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart6",style:{position:"relative",width:"500px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])],-1)),a[27]||(a[27]=m("div",{class:"chart-container full-width"},[m("p",{class:"title"},"八大人群占比"),m("div",{class:"base-chart eight-crowds-chart","size-sensor-id":"24",style:{position:"relative"}},[m("div",{class:"lightcharts-container",id:"base-chart7",style:{position:"relative",width:"900px",height:"274px",padding:"0px",margin:"0px",overflow:"hidden","border-width":"0px"}})])],-1)),a[28]||(a[28]=m("div",{class:"chart-container device-container"},[m("p",{class:"title"},"设备分布"),m("div",{class:"base-chart","size-sensor-id":"25"},[m("div",{class:"lightcharts-container",id:"base-chart8",style:{width:"366px",height:"274px"}})])],-1))])])])])])])])}}},[["__scopeId","data-v-306af20b"]]);export{me as default};
