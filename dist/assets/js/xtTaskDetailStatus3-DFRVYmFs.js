import{r as l,H as e,f as a,a as o,c as n,o as d,h as i,b as t,w as s,d as u,t as r,g as v,n as p,y as m,E as _}from"./index-C2bfFjZ1.js";import{e as c,c as f}from"./index-P0VwV2wd.js";import{_ as b}from"./_plugin-vue_export-helper-BXFjo1rG.js";const w={class:"container_form mt-2 px-2 pb-20"},g={class:"grey-6"},k={class:"mt-2"},y={key:0,class:"grid grid-cols-3 gap-4"},h={key:1,class:"grid grid-cols-3 gap-4"},D=b({__name:"xtTaskDetailStatus3",props:["type","id","taskDetail"],emits:["addKolHandler","closeDrawer"],setup(b,{expose:D,emit:x}){const V=l(""),j=l("");e();const I=l(5),U=l(null),S=l({}),Y=l([]),H=b,C=x;let M=H.type;const A=H.id,P=l("");P.value=JSON.parse(localStorage.getItem("taskDetail")),P.value&&(j.value=P.value,S.value=P.value,I.value=P.value.platform,V.value=P.value.order_list),a((()=>H.taskDetail),(l=>{j.value=l,S.value=l,I.value=l.platform,V.value=l.order_list})),a([()=>H.type],(l=>{M=H.type}));const T={1:"指派任务",2:"招募任务",3:"投稿任务",4:"星广联投",5:"小红星",6:"小红盟",7:"蒲公英"},q={1:{label:"1-20S视频",value:"price_1_20"},2:{label:"21-60S视频",value:"price_20_60"},3:{label:"60S以上视频",value:"price_60"},4:{label:"图文笔记一口价",value:"redbook_graphic"},5:{label:"视频笔记一口价",value:"redbook_video"}},z=async l=>{let e=E();if(!e.kol_data.length>0)return _.warning("请先添加达人"),!1;U.value&&await U.value.validate(((a,o)=>{a&&("edit"==M?(e.id=A,e.status=l,c(e).then((l=>{_.success(l.msg),C("closeDrawer","refresh")}))):(e.status=l,f(e).then((l=>{_.success(l.msg),C("closeDrawer","refresh")}))))}))},E=l=>{const e=V.value;return{status:l,task_name:S.value.task_name,project_id:S.value.project_id,other_demand:S.value.other_demand,marketing_target:S.value.marketing_target,task_type:S.value.task_type,platform:"add"==M?A:j.value.platform,publishe_time:{start_time:S.value.task_date[0],end_time:S.value.task_date[1]},kol_data:e}};return D({onSubmit:z,updateDate:l=>{P.value=l,l&&(l.value=l,S.value=l,I.value=l.platform,V.value=l.order_list)}}),(l,e)=>{const a=o("el-input"),_=o("el-form-item"),c=o("el-col"),f=o("el-row"),b=o("el-radio"),D=o("el-radio-group"),x=o("el-date-picker"),I=o("el-table-column"),H=o("el-button"),A=o("el-table"),P=o("el-form");return d(),n("div",null,[i("div",w,[t(P,{model:S.value,ref_key:"ruleFormRef",ref:U,"label-width":"120px","label-position":"left"},{default:s((()=>{var l;return[e[56]||(e[56]=i("div",{class:"form_title"},[i("h3",null,"项目信息")],-1)),t(f,{gutter:24,style:{"margin-left":"12px"}},{default:s((()=>[t(c,{span:11},{default:s((()=>[t(_,{label:"项目名称",prop:"project_id"},{default:s((()=>[t(a,{modelValue:S.value.project_name,"onUpdate:modelValue":e[0]||(e[0]=l=>S.value.project_name=l),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),t(c,{span:11},{default:s((()=>[t(_,{label:"营销目标",prop:"marketing_target"},{default:s((()=>[t(a,{"show-word-limit":"",maxlength:"20",disabled:"",modelValue:S.value.marketing_target,"onUpdate:modelValue":e[1]||(e[1]=l=>S.value.marketing_target=l)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),e[57]||(e[57]=i("div",{class:"form_title"},[i("h3",null,"任务信息")],-1)),t(f,{gutter:24,style:{"margin-left":"12px"}},{default:s((()=>[t(c,{span:11},{default:s((()=>[t(_,{label:"任务名称",prop:"task_name"},{default:s((()=>[t(a,{style:{width:"340px"},modelValue:S.value.task_name,"onUpdate:modelValue":e[2]||(e[2]=l=>S.value.task_name=l),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),t(c,{span:11},{default:s((()=>[t(_,{label:"是否为平台任务"},{default:s((()=>[t(D,{modelValue:S.value.platform_status,"onUpdate:modelValue":e[3]||(e[3]=l=>S.value.platform_status=l),disabled:""},{default:s((()=>[t(b,{label:1},{default:s((()=>e[9]||(e[9]=[u("是")]))),_:1}),t(b,{label:0},{default:s((()=>e[10]||(e[10]=[u("否")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),t(f,{gutter:24,style:{"margin-left":"12px"}},{default:s((()=>[t(c,{span:11},{default:s((()=>[t(_,{label:"任务类型"},{default:s((()=>{var l;return[t(a,{modelValue:T[null==(l=S.value)?void 0:l.task_type],"onUpdate:modelValue":e[4]||(e[4]=l=>{var e;return T[null==(e=S.value)?void 0:e.task_type]=l}),disabled:""},null,8,["modelValue"])]})),_:1})])),_:1})])),_:1}),e[58]||(e[58]=i("div",{class:"form_title"},[i("h3",null,"任务要求")],-1)),t(f,{gutter:24,style:{"margin-left":"12px"}},{default:s((()=>[t(c,{span:11},{default:s((()=>[t(_,{label:"期望发布时间",prop:"task_date"},{default:s((()=>[t(x,{style:{width:"100%"},modelValue:S.value.anticipation_time,"onUpdate:modelValue":e[5]||(e[5]=l=>S.value.anticipation_time=l),disabled:"",type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"期望发布时间"},null,8,["modelValue"])])),_:1})])),_:1}),t(c,{span:11},{default:s((()=>[t(_,{label:"其他要求"},{default:s((()=>[t(a,{modelValue:S.value.other_demand,"onUpdate:modelValue":e[6]||(e[6]=l=>S.value.other_demand=l),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),e[59]||(e[59]=i("div",{class:"form_title flex",style:{width:"98%","justify-content":"space-between"}},[i("h3",null,"达人信息")],-1)),i("p",g,"媒体平台："+r(5==(null==(l=S.value)?void 0:l.platform)?"抖音":"小红书"),1),i("div",k,[t(A,{data:V.value,border:"",style:{width:"98%"},"header-cell-style":{textAlign:"center"},ref:"expandTable","row-key":"id","expand-row-keys":Y.value,"cell-style":{textAlign:"center"}},{default:s((()=>[t(I,{type:"expand",width:"1"},{default:s((l=>{var a,o,t,s,v,p,m,_,c,f,b,w,g,k,D,x,V,j,I,U,Y,H,C,M,A,P,T,q,z,E,F,J,K,N,O,R,B,G,L,Q,W,X,Z,$;return[5==(null==(a=S.value)?void 0:a.platform)?(d(),n("div",y,[i("div",null,[e[11]||(e[11]=i("span",{class:"font-semibold"},"达人裸价（不含服务费）",-1)),u(":"+r(null==(o=null==l?void 0:l.row)?void 0:o.kol_base_price),1)]),i("div",null,[e[12]||(e[12]=i("span",{class:"font-semibold"},"达人授权费（不含服务费）",-1)),u(": "+r(null==(t=null==l?void 0:l.row)?void 0:t.kol_licensing_fee),1)]),i("div",null,[e[13]||(e[13]=i("span",{class:"font-semibold"},"实际下单价（改价后含服务费）",-1)),u("："+r(null==(s=null==l?void 0:l.row)?void 0:s.actual_amount_price),1)]),i("div",null,[e[14]||(e[14]=i("span",{class:"font-semibold"},"是否改价",-1)),u("："+r(0==(null==(v=null==l?void 0:l.row)?void 0:v.change_status)?"否":"是"),1)]),i("div",null,[e[15]||(e[15]=i("span",{class:"font-semibold"},"星图价格（含服务费）",-1)),u("："+r(null==(p=null==l?void 0:l.row)?void 0:p.star_price),1)]),i("div",null,[e[16]||(e[16]=i("span",{class:"font-semibold"},"预估应收媒体返点",-1)),u("："+r(null==(m=null==l?void 0:l.row)?void 0:m.predict_receivable_medium_price),1)]),i("div",null,[e[17]||(e[17]=i("span",{class:"font-semibold"},"预估成本",-1)),u("："+r(null==(_=null==l?void 0:l.row)?void 0:_.predict_cost),1)]),i("div",null,[e[18]||(e[18]=i("span",{class:"font-semibold"},"客户返佣",-1)),u("："+r(null==(c=null==l?void 0:l.row)?void 0:c.customer_rebate),1)]),i("div",null,[e[19]||(e[19]=i("span",{class:"font-semibold"},"客户服务费",-1)),u("："+r(null==(f=null==l?void 0:l.row)?void 0:f.customer_service_price),1)]),i("div",null,[e[20]||(e[20]=i("span",{class:"font-semibold"},"预估应收客户款",-1)),u("："+r(null==(b=null==l?void 0:l.row)?void 0:b.predict_receivable_customer_price),1)]),i("div",null,[e[21]||(e[21]=i("span",{class:"font-semibold"},"毛利",-1)),u("："+r(null==(w=null==l?void 0:l.row)?void 0:w.gross_profit),1)]),i("div",null,[e[22]||(e[22]=i("span",{class:"font-semibold"},"毛利率",-1)),u("："+r(null==(g=null==l?void 0:l.row)?void 0:g.gross_profit_margin),1)]),i("div",null,[e[23]||(e[23]=i("span",{class:"font-semibold"},"达人所属机构",-1)),u("："+r(null==(k=null==l?void 0:l.row)?void 0:k.mcn_name),1)]),i("div",null,[e[24]||(e[24]=i("span",{class:"font-semibold"},"期望发布日期",-1)),u("："+r(null==(D=null==l?void 0:l.row)?void 0:D.anticipation_time),1)]),i("div",null,[e[25]||(e[25]=i("span",{class:"font-semibold"},"期望保留时长",-1)),u("："+r(null==(x=null==l?void 0:l.row)?void 0:x.anticipation_reserve_day),1)]),i("div",null,[e[26]||(e[26]=i("span",{class:"font-semibold"},"组件名称",-1)),u("："+r(null==(V=null==l?void 0:l.row)?void 0:V.component_name),1)]),i("div",null,[e[27]||(e[27]=i("span",{class:"font-semibold"},"组件-文案",-1)),u("："+r(null==(j=null==l?void 0:l.row)?void 0:j.component_copywriting),1)]),i("div",null,[e[28]||(e[28]=i("span",{class:"font-semibold"},"组件-链接",-1)),u("："+r(null==(I=null==l?void 0:l.row)?void 0:I.component_link),1)]),i("div",null,[e[29]||(e[29]=i("span",{class:"font-semibold"},"组件-备注",-1)),u("："+r(null==(U=null==l?void 0:l.row)?void 0:U.component_notes),1)]),i("div",null,[e[30]||(e[30]=i("span",{class:"font-semibold"},"其他特殊备注",-1)),u("："+r(null==(Y=null==l?void 0:l.row)?void 0:Y.other_notes),1)]),i("div",null,[e[31]||(e[31]=i("span",{class:"font-semibold"},"是否投放广告",-1)),u("："+r(null==(H=null==l?void 0:l.row)?void 0:H.invest_status),1)]),i("div",null,[e[32]||(e[32]=i("span",{class:"font-semibold"},"巨量广告广告主ID（效果广告）",-1)),u("："+r(null==(C=null==l?void 0:l.row)?void 0:C.advertising_effect_id),1)]),i("div",null,[e[33]||(e[33]=i("span",{class:"font-semibold"},"巨量广告广告主ID（品牌广告）",-1)),u("："+r(null==(M=null==l?void 0:l.row)?void 0:M.advertising_brand_id),1)]),i("div",null,[e[34]||(e[34]=i("span",{class:"font-semibold"},"巨量千川广告主ID",-1)),u("："+r(null==(A=null==l?void 0:l.row)?void 0:A.advertising_qianchuan_id),1)]),i("div",null,[e[35]||(e[35]=i("span",{class:"font-semibold"},"Dou+广告主ID",-1)),u("："+r(null==(P=null==l?void 0:l.row)?void 0:P.advertising_doujia_id),1)]),i("div",null,[e[36]||(e[36]=i("span",{class:"font-semibold"},"Dou+广告主抖音UID",-1)),u("："+r(null==(T=null==l?void 0:l.row)?void 0:T.advertising_doujia_tiktok_id),1)])])):(d(),n("div",h,[i("div",null,[e[37]||(e[37]=i("span",{class:"font-semibold"},"平台裸价（不含服务费）",-1)),u(":"+r(null==(q=null==l?void 0:l.row)?void 0:q.kol_base_price),1)]),i("div",null,[e[38]||(e[38]=i("span",{class:"font-semibold"},"蒲公英下单价（含服务费）",-1)),u(": "+r(null==(z=null==l?void 0:l.row)?void 0:z.dandelion_order_price),1)]),i("div",null,[e[39]||(e[39]=i("span",{class:"font-semibold"},"实际下单价（改价后含服务费）",-1)),u("："+r(null==(E=null==l?void 0:l.row)?void 0:E.actual_amount_price),1)]),i("div",null,[e[40]||(e[40]=i("span",{class:"font-semibold"},"备注",-1)),u("："+r(null==(F=null==l?void 0:l.row)?void 0:F.change_status),1)]),i("div",null,[e[41]||(e[41]=i("span",{class:"font-semibold"},"预估成本",-1)),u("："+r(null==(J=null==l?void 0:l.row)?void 0:J.predict_cost),1)]),i("div",null,[e[42]||(e[42]=i("span",{class:"font-semibold"},"预估应收媒体返点",-1)),u("："+r(null==(K=null==l?void 0:l.row)?void 0:K.predict_receivable_medium_price),1)]),i("div",null,[e[43]||(e[43]=i("span",{class:"font-semibold"},"客户返佣",-1)),u("："+r(null==(N=null==l?void 0:l.row)?void 0:N.customer_rebate),1)]),i("div",null,[e[44]||(e[44]=i("span",{class:"font-semibold"},"客户服务费",-1)),u("："+r(null==(O=null==l?void 0:l.row)?void 0:O.customer_service_price),1)]),i("div",null,[e[45]||(e[45]=i("span",{class:"font-semibold"},"预估应收客户款",-1)),u("："+r(null==(R=null==l?void 0:l.row)?void 0:R.predict_receivable_customer_price),1)]),i("div",null,[e[46]||(e[46]=i("span",{class:"font-semibold"},"毛利",-1)),u("："+r(null==(B=null==l?void 0:l.row)?void 0:B.gross_profit),1)]),i("div",null,[e[47]||(e[47]=i("span",{class:"font-semibold"},"毛利率",-1)),u("："+r(null==(G=null==l?void 0:l.row)?void 0:G.gross_profit_margin),1)]),i("div",null,[e[48]||(e[48]=i("span",{class:"font-semibold"},"达人所属机构",-1)),u("："+r(null==(L=null==l?void 0:l.row)?void 0:L.mcn_name),1)]),i("div",null,[e[49]||(e[49]=i("span",{class:"font-semibold"},"期望发布日期",-1)),u("："+r(null==(Q=null==l?void 0:l.row)?void 0:Q.anticipation_time),1)]),i("div",null,[e[50]||(e[50]=i("span",{class:"font-semibold"},"报备品牌（务必精准对应品牌专业号）",-1)),u("："+r(null==(W=null==l?void 0:l.row)?void 0:W.report_brand),1)]),i("div",null,[e[51]||(e[51]=i("span",{class:"font-semibold"},"产品名称",-1)),u("："+r(null==(X=null==l?void 0:l.row)?void 0:X.product_name),1)]),i("div",null,[e[52]||(e[52]=i("span",{class:"font-semibold"},"是否绑定SPU（提供准确名称/SPUID）",-1)),u("："+r(null==(Z=null==l?void 0:l.row)?void 0:Z.spu_bind),1)]),i("div",null,[e[53]||(e[53]=i("span",{class:"font-semibold"},"商品ID",-1)),u("："+r(null==($=null==l?void 0:l.row)?void 0:$.good_id),1)])]))]})),_:1}),t(I,{prop:"kol_name",label:"达人名称",width:"180"}),t(I,{prop:"platform_uid",label:"达人ID",width:"180"}),t(I,{prop:"name",label:"合作形式",width:"180"},{default:s((l=>{var e,a;return[u(r(null==(a=q[null==(e=l.row)?void 0:e.cooperation_type])?void 0:a.label),1)]})),_:1}),t(I,{prop:"name",label:"平台裸价"},{default:s((l=>{var e;return[i("div",null,r(null==(e=l.row)?void 0:e.kol_base_price),1)]})),_:1}),t(I,{prop:"name",label:"合作返点"},{default:s((l=>{var e;return[u(r(100*(null==(e=l.row)?void 0:e.rebate_ratio))+"% ",1)]})),_:1}),t(I,{prop:"name",label:"操作"},{default:s((l=>[t(H,{link:"",size:"small",class:"btnClass",onClick:e=>{return a=l.row,void(Y.value.includes(a.id)?Y.value=Y.value.filter((l=>l!==a.id)):(Y.value=[],Y.value.push(a.id)));var a}},{default:s((()=>{var e;return[u(r((null==(e=null==l?void 0:l.row)?void 0:e.id)==Y.value[0]?"收起":"展开"),1)]})),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","expand-row-keys"])]),t(_,{class:"form-footer mt-2"},{default:s((()=>{var l;return[t(H,{type:"primary",onClick:e[7]||(e[7]=l=>{C("closeDrawer","back")})},{default:s((()=>e[54]||(e[54]=[u("返回")]))),_:1}),2==(null==(l=j.value)?void 0:l.status)&&"edit"==m(M)?(d(),v(H,{key:0,type:"primary",onClick:e[8]||(e[8]=l=>z(3))},{default:s((()=>e[55]||(e[55]=[u("完成")]))),_:1})):p("",!0)]})),_:1})]})),_:1},8,["model"])])])}}},[["__scopeId","data-v-2bd2eb90"]]);export{D as default};
