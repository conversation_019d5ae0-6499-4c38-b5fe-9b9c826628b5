import{s as e,r as a,H as l,u as t,aA as r,a6 as o,f as n,e as u,aT as i,p as c,I as d,a as s,Q as v,c as _,o as p,m,h as f,b as y,v as g,w,d as h,F as b,i as k,g as j,t as V,n as x,M as C,y as S,aX as z,aS as q,aY as A,E as I,q as N,K as U}from"./index-BE6Fh1xm.js";import{_ as $}from"./default-face-CI2vwNfZ.js";import{S as O}from"./index-C9PKVjyH.js";import P from"./taskDrawer-_07mRr5g.js";import{G as D,y as T,A as L,z as M,H as E,I as J,J as F,K as B,L as R,M as K}from"./business-CcSWCctm.js";import{z as W}from"./order-VDf-VPrU.js";import{u as X}from"./tabs-CGQc3OAd.js";import{_ as Y}from"./_plugin-vue_export-helper-GSmkUi5K.js";const G={class:"table-box"},H={class:"bg-white card table-main"},Q={class:"content_box","element-loading-text":"loading...",style:{position:"relative"},"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},Z={style:{"z-index":"2",display:"flex","justify-content":"flex-end","align-items":"center",position:"absolute",top:"10px",right:"20px"}},ee={class:"task-detail-tabs-filter"},ae={class:"task-detail-tabs-content"},le={class:"flex align-center"},te={class:"task-detail-tabs-content"},re={class:"task-detail-tabs-content"},oe={key:0,class:"avatar-stack"},ne={key:1,class:"avatar-stack"},ue={style:{width:"28px",height:"28px"}},ie={key:2,class:"avatar-stack"},ce={key:0},de={class:"reviewer-names"},se=["onClick"],ve={class:"reviewer-list"},_e=["onClick"],pe={class:"reviewer-names"},me=["onClick"],fe={class:"reviewer-list"},ye=["onClick"],ge={class:"reviewer-names"},we=["onClick"],he={class:"reviewer-list"},be=["onClick"],ke={class:"reviewer-names"},je=["onClick"],Ve={class:"reviewer-list"},xe=["onClick"],Ce={class:"cursor-pointer flex align-center",style:{display:"flex","align-items":"center"}},Se={key:0,style:{display:"flex","flex-direction":"column","align-items":"center"}},ze=["onClick"],qe={key:1},Ae={class:"flex justify-end",style:{"margin-bottom":"10px"}},Ie={class:"search-div-photo flex items-center"},Ne=["src"],Ue={key:1,src:$,width:"45",height:"45",alt:""},$e={class:"ml-4 font-semibold"},Oe={style:{display:"flex","justify-content":"space-between","margin-top":"20px","padding-left":"20px",color:"#666"}},Pe={class:"drawer-container"},De={class:"form-container"},Te={class:"form-item-label-with-icon"},Le={class:"form-input-with-action"},Me={class:"form-action"},Ee={class:"contract-option"},Je={class:"contract-option-title"},Fe={class:"contract-option-id"},Be={class:"contract-option-project"},Re={key:0,class:"contract-details"},Ke={class:"contract-details-item"},We={class:"contract-details-item"},Xe={class:"contract-details-item contract-dates"},Ye={class:"contract-details-item contract-dates"},Ge={class:"expire-date"},He={key:0,class:"contract-details-item"},Qe={key:1,class:"contract-details-item"},Ze={class:"form-input-with-action"},ea={class:"form-action"},aa={key:0,class:"field-note"},la={key:0,class:"field-note"},ta={class:"drawer-footer"},ra={style:{display:"flex","align-items":"center","justify-content":"space-between"}},oa={style:{width:"28px",height:"28px"}},na={style:{flex:"1"}},ua={style:{"font-size":"12px",color:"#999"}},ia={style:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},ca={class:"dialog-footer"},da={class:"account-search-form mb-4"},sa={class:"pagination-container mt-4"},va={class:"dialog-footer"},_a=Y(e({...e({name:"businessProject"}),props:{projectId:{type:Number,default:0},isSearch:{type:Boolean,default:!0}},setup(e){var $;const Y=a(null),_a=l(),pa=t(),ma=r(),fa=a([]),ya=X(),ga=a(!1),wa=e=>({1:"一级审核人",2:"二级审核人",3:"三级审核人",4:"四级审核人",5:"五级审核人"}[e]||`${e}级审核人`),ha=(e,a,l=5)=>{if(!e||!e.length)return"-";const t=e.filter((e=>e.reviewer_level===a)).map((e=>e.reviewer_personnel)).filter(Boolean);return t.length?t.length>l?{nameList:t,displayNames:t.slice(0,l).join("、"),totalCount:t.length,hasMore:!0}:{nameList:t,displayNames:t.join("、"),totalCount:t.length,hasMore:!1}:"-"},ba=a({}),ka=(e,a)=>{const l=`${e}-${a}`;ba.value[l]=!ba.value[l]},ja=(e,a)=>{const l=`${e}-${a}`;return!!ba.value[l]},Va=a(localStorage.getItem("userId")),xa=a(!0),Ca=a({}),Sa={xs:1,sm:2,md:2,lg:3,xl:4},za=a("first"),qa=a(!1),Aa=a([]),Ia=a([]),Na=a(!1),Ua=a(0),$a=a(!1),Oa=a([]),Pa=a([]),Da=a([{label:"全部（0）",value:"",count:0},{label:"待填写下单信息（0）",value:"2",count:0},{label:"待审核（0）",value:"3",count:0},{label:"审核拒绝（0）",value:"4",count:0},{label:"待下单（0）",value:"6",count:0},{label:"已完成（0）",value:"7",count:0}]),Ta=a({project_id:"",task_name:"",promotion_platforms_genres:1,task_type:"",settlement_method:1,order_type:1,oa_project_id:"",customer_entity:"",customer_contract:"",contract_id:"",usc_code:"",performance_owner:"",performance_department:"",platform_type:1,platform_type_entity:"",order_method:"",recharge_contract:"",remark:"",account_id:"",account_name:"",account_brand_name:"",customer_code:"",approval_type_text:"",contract_type:"",media_or_channel:"",contract_sign_date:"",contract_start_date:"",contract_end_date:"",contract_amount:"",contract_relative_name:"",company:"",oa_project_name:"",operator:"",department:""}),La=a({1:"抖音",2:"抖音星立方",3:"小红书",4:"快手",5:"B站",6:"腾讯互选"}),Ma=o({task_name:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 字符",trigger:"blur"}],project_id:[{required:!0,message:"请选择所属项目",trigger:"change"}],oa_project_id:[{required:!0,message:"所属OA项目不能为空",trigger:"change"}],contract_id:[{required:!0,message:"请选择客户合同",trigger:"change"}],customer_entity:[{required:!0,message:"客户主体不能为空，请选择所属项目",trigger:"change"}],performance_owner:[{required:!0,message:"业绩归属人不能为空，请选择客户合同",trigger:"change"}],performance_department:[{required:!0,message:"业绩归属部门不能为空，请选择客户合同",trigger:"change"}],platform_type:[{required:!0,message:"请选择媒体平台",trigger:"change"}],order_method:[{required:!0,message:"请选择下单方式",trigger:"change"}],order_type:[{required:!0,message:"请选择订单类型",trigger:"change"}],account_id:[{required:!1,validator:(e,a,l)=>{"2"!==Ta.value.order_method||a?l():l(new Error("集采线下下单时请选择账户"))},trigger:"change"}],recharge_contract:[{required:!1,validator:(e,a,l)=>{Ra()&&!a?l(new Error("该媒体平台类型需要选择充值合同")):l()},trigger:"change"}],task_type:[{required:!1,validator:(e,a,l)=>{const t=Number(Ta.value.platform_type);Ta.value.project_id&&[1,2,5].includes(t)&&!a?l(new Error("请选择任务模式")):l()},trigger:"change"}]}),Ea=a({1:"指派",2:"招募",3:"投稿"}),Ja=a({douyin:[{label:"指派",value:"1"},{label:"招募",value:"2"},{label:"投稿",value:"3"}],bilibili:[{label:"京火",value:"1"},{label:"花火",value:"2"}]}),Fa=a({2:"待填写下单信息",3:"待审核",4:"审核拒绝",5:"待审核",6:"审核通过待下单",7:"下单前流程结束"}),Ba=a([]),Ra=()=>{const e=Number(Ta.value.platform_type),a=Za.value.find((e=>e.id===Ta.value.project_id));return!(1!==e||!a||1!==a.promote_type)||(3===e||5===e)},Ka=e;n(ma.messages,(()=>{let e=ma.messages[ma.messages.length-1];"error_message"==e.event&&I.error(e.data.msg),"success_message"==e.event&&I.success(e.data.msg)}),{deep:!0});const Wa=e=>{Aa.value.length>1&&(Aa.value=[Aa.value[Aa.value.length-1]]),yl.value.status=Aa.value[0],pl()},Xa=e=>{yl.value.page_size=e,pl()},Ya=e=>{yl.value.page=e,pl()},Ga=e=>{"first"==za.value&&((null==e?void 0:e.task_time)&&(yl.value.task_create_time=e.task_time[0],yl.value.task_end_time=e.task_time[1]),yl.value={...yl.value,...e},pl())},Ha=a(null),Qa=()=>{var e;"first"==za.value&&(yl.value={task_type:"",task_search:"",custom_name:"",project_name:"",project_id:"",status:"",order_status:"",created_id:"",created_name:"",task_create_time:"",task_end_time:"",page_size:10,page:1},Ca.value.task_time=null,Ba.value=[],null==(e=Ha.value)||e.reset(),pl())},Za=a([]),el=()=>{al.value=!0;T({search:"",page:1,page_size:10}).then((e=>{if(al.value=!1,e&&990===e.code&&e.data&&Array.isArray(e.data.lists)){const a=e.data.lists;Za.value=a}}))},al=a(!1),ll=e=>{if(e){al.value=!0,T({project_name:e,page:1,page_size:10}).then((e=>{if(al.value=!1,e&&990===e.code&&e.data&&Array.isArray(e.data.lists)){const a=e.data.lists;Za.value=a}}))}else el()},tl=()=>{Na.value=!1,Ua.value=0,Ta.value={project_id:"",task_name:"",promotion_platforms_genres:1,task_type:"",settlement_method:1,oa_project_id:"",customer_entity:"",customer_contract:"",contract_id:"",usc_code:"",performance_owner:"",performance_department:"",platform_type:0,platform_type_entity:"",order_method:"",recharge_contract:"",remark:"",task_type:"",account_id:"",account_name:"",account_brand_name:"",customer_code:"",approval_type_text:"",contract_type:"",media_or_channel:"",contract_sign_date:"",contract_start_date:"",contract_end_date:"",contract_amount:"",contract_relative_name:"",company:"",oa_project_name:"",operator:"",department:""},Za.value=[],fa.value=[],Vl.value=null,Ml.value="未知",Rl.value=[],Gl.value=null,Hl.value=null,Ql.value="",et.value={currentPage:1,pageSize:10,total:0},Zl.value={customer_account_id:"",account_name:"",customer_name:""}},rl=o([{prop:"platform_type",label:"平台",search:{el:"select",props:{placeholder:"全部"}},enum:[{label:"抖音",value:1},{label:"小红书",value:3},{label:"快手",value:4},{label:"B站",value:5},{label:"腾讯互选",value:6}]},{prop:"order_method",label:"下单方式",search:{el:"select",props:{placeholder:"请选择下单方式"}},enum:[{label:"星推下单",value:1},{label:"线下下单",value:2}]},{prop:"task_search",label:"任务信息",search:{el:"input",props:{placeholder:"请输入任务名称或ID"}}},{prop:"custom_name",label:"客户名称",search:{el:"input",props:{placeholder:"请输入客户名称"}}},{prop:"project_name",label:"项目名称",search:{el:"input",props:{placeholder:"请输入项目名称"}}},{prop:"task_time",label:"创建时间",search:{el:"date-picker",span:1,props:{type:"daterange",valueFormat:"YYYY-MM-DD",rangeSeparator:"至",startPlaceholder:"开始时间",endPlaceholder:"结束时间"},defaultValue:Ba.value}},{prop:"operation",label:"操作",fixed:"right",width:100}]),ol=u((()=>null==rl?void 0:rl.filter((e=>{var a,l;return(null==(a=e.search)?void 0:a.el)||(null==(l=e.search)?void 0:l.render)})).sort(((e,a)=>e.search.order-a.search.order))));null==($=ol.value)||$.forEach(((e,a)=>{var l,t,r;e.search.order=(null==(l=e.search)?void 0:l.order)??a+2;const o=(null==(t=e.search)?void 0:t.key)??i(e.prop),n=null==(r=e.search)?void 0:r.defaultValue;null!=n&&(Ca.value[o]=n)}));const nl=(e,a)=>{"createOrder"==e?ml(a.id):_a.push({path:"/business/task/"+e,query:{page_type:"add",id:a.id}})},ul=a(!1),il=a([]),cl=a(""),dl=a([]),sl=a(!1),vl=[{label:"1-20S视频",value:1},{label:"21-60S视频",value:2},{label:"60S以上视频",value:71},{label:"招募任务一口价",value:100}];n(il,(e=>{dl.value=e.map((e=>0!==e.order_status&&null))}),{immediate:!0}),n(dl,(e=>{const a=il.value.every(((a,l)=>0!==a.order_status||e[l]));sl.value=a}));const _l=()=>{il.value.forEach(((e,a)=>{0===e.order_status&&(dl.value[a]=sl.value)}))},pl=async()=>{try{qa.value=!0,Ka.isSearch||(yl.value.project_id=Ka.projectId),yl.value.status=Aa.value[0],sessionStorage.getItem("task_id")&&(yl.value.task_search=sessionStorage.getItem("task_id"));const e=await D(yl.value);if(sessionStorage.removeItem("task_id"),sessionStorage.removeItem("status"),sessionStorage.removeItem("active"),!(null==e?void 0:e.data))return Pa.value=[],fl.total=0,void(Da.value=[{label:"全部（0）",value:"",count:0},{label:"待填写下单信息（0）",value:"2",count:0},{label:"待审核（0）",value:"3",count:0},{label:"审核拒绝（0）",value:"4",count:0},{label:"待下单（0）",value:"6",count:0},{label:"已完成（0）",value:"7",count:0}]);const a=e.data.tasks,l=e.data.count;Pa.value=a,fl.total=l;let t=null==e?void 0:e.status_aggregate,r=0;Object.values(t||{}).forEach((e=>{r+=e})),Da.value=[{label:`全部（${r||0}）`,value:"",count:r||0},{label:`待填写下单信息（${t[2]||0}）`,value:"2",count:t[2]||0},{label:`待审核（${t[3]||0}）`,value:"3",count:t[3]||0},{label:`审核拒绝（${t[4]||0}）`,value:"4",count:t[4]||0},{label:`待下单（${t[6]||0}）`,value:"6",count:t[6]||0},{label:`已完成（${t[7]||0}）`,value:"7",count:t[7]||0}]}catch(e){}finally{qa.value=!1}},ml=e=>{B({task_id:e}).then((a=>{il.value=a.data,cl.value=e,ul.value=!0}))},fl=o({total:0}),yl=a({task_type:"",task_search:"",custom_name:"",project_name:"",project_id:"",status:"",order_status:"",created_id:"",created_name:"",task_create_time:"",task_end_time:"",page_size:10,page:1}),gl=a(""),wl=()=>{let e=0;const a=window.screen,l=navigator.userAgent.toLowerCase();return void 0!==window.devicePixelRatio?e=window.devicePixelRatio:~l.indexOf("msie")?a.deviceXDPI&&a.logicalXDPI&&(e=a.deviceXDPI/a.logicalXDPI):void 0!==window.outerWidth&&void 0!==window.innerWidth&&(e=window.outerWidth/window.innerWidth),e&&(e=Math.round(100*e)),e},hl=a("");a("");const bl=a(!1),kl=(e,a)=>{e&&(bl.value=!0,L({usc_code:e,contract_id:a||"",page:1,page_size:999}).then((e=>{if(e&&990===e.code&&e.data&&Array.isArray(e.data.lists)){e.data.lists.some((e=>e.project_name));fa.value=e.data.lists,1!==fa.value.length||a||(Ta.value.customer_contract=fa.value[0].contract_id,Cl(fa.value[0].contract_id)),0===fa.value.length&&I.warning("未找到该客户的合同信息")}else fa.value=[],I.warning("获取合同信息失败")})).catch((e=>{I.error("获取合同信息失败"),fa.value=[]})).finally((()=>{bl.value=!1})))},jl=e=>{hl.value=e;let a=Ta.value.usc_code;if(!a){const e=Za.value.find((e=>e.id===Ta.value.project_id));a=null==e?void 0:e.usc_code}a?(bl.value=!0,kl(a,e)):I.warning("请先选择项目或客户主体")},Vl=a(),xl=e=>e&&(e.project_name||e.contract_project_name||e.oa_project_name||e.project_id||e.oa_project_id||e.contract_name)||"",Cl=e=>{if(!e)return Ta.value.recharge_contract="",void(Al.value=[]);const a=fa.value.find((a=>a.contract_id===e));if(!a)return;Vl.value=a;const l=xl(a);l?(Ta.value.oa_project_id=l,Ta.value.oa_project_name=l,a.project_name||a.contract_project_name?Ml.value="合同项目名称":a.oa_project_name?Ml.value="合同OA项目名称":a.project_id?Ml.value="项目ID":a.oa_project_id?Ml.value="合同OA项目ID":Ml.value="合同名称"):(Ta.value.oa_project_id=a.contract_id||"",Ml.value="合同编号"),Ta.value.customer_code=a.contract_id||"",Ta.value.customer_contract=a.contract_name||"",Ta.value.contract_id=a.contract_id||"",Ta.value.approval_type_text=a.approval_type_text||"",Ta.value.contract_type=a.contract_type||"",Ta.value.media_or_channel=a.media_or_channel||Ta.value.platform_type_entity,Ta.value.contract_sign_date=a.contract_sign_date||"",Ta.value.contract_start_date=a.contract_start_date||"",Ta.value.contract_end_date=a.contract_end_date||"",Ta.value.contract_amount=a.contract_amount||"",Ta.value.contract_relative_name=a.relative_party_name||a.contract_relative_name||"",Ta.value.company=a.company||"",Ta.value.performance_owner=a.perf_owner_detail||"",Ta.value.performance_department=a.perf_dept_detail||"",a.perf_owner_detail&&a.perf_dept_detail||I.warning("所选合同缺少业绩归属人或业绩归属部门信息，请选择包含完整信息的合同"),Ta.value.operator=Ta.value.performance_owner,Ta.value.department=Ta.value.performance_department};n((()=>Ta.value.performance_owner),(e=>{Ta.value.operator=e})),n((()=>Ta.value.performance_department),(e=>{Ta.value.department=e})),n((()=>Ta.value.platform_type),(e=>{const a=Number(e||0),l=zl.value.find((e=>e.value===a));switch(ht.value=l?l.label:"",a){case 1:case 2:Ta.value.platform_type_entity="武汉星图新视界科技有限公司";break;case 3:Ta.value.platform_type_entity="薯鸿文化传媒（上海）有限公司";break;case 4:Ta.value.platform_type_entity="北京晨钟科技有限公司";break;case 5:Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌";break;case 6:Ta.value.platform_type_entity="深圳市腾讯文化传媒有限公司";break;default:Ta.value.platform_type_entity=""}Ra()?Ta.value.contract_id&&Ll(Ta.value.contract_id,Ta.value.recharge_contract,!1):(Ta.value.recharge_contract="",Al.value=[])})),n((()=>Ta.value.contract_id),((e,a)=>{ga.value||e!==a&&(Ta.value.recharge_contract="",Al.value=[],Il&&(clearTimeout(Il),Il=null),Nl=0,Ra()&&Ll(e,"",!1))}));c((()=>{if(window.addEventListener("beforeunload",(()=>{pa.query.task_id&&sessionStorage.removeItem("task_id")})),Aa.value=[""],Ia.value=[""],sessionStorage.getItem("active")&&(za.value="1"==sessionStorage.getItem("active")?"first":"second","2"==sessionStorage.getItem("active")?Ia.value=[sessionStorage.getItem("status")]:Aa.value=[sessionStorage.getItem("status")]),pa.query.task_id&&mt(),pa.query.task_id&&yt.value){const e=Array.isArray(pa.query.task_id)?pa.query.task_id[0]:pa.query.task_id,a=String(e);yl.value.task_search=a,yl.value.id=a,yl.value.task_id=a,/^\d+$/.test(a)&&sessionStorage.setItem("task_id",a),yt.value=!1,pl();const l={...pa.query};return delete l.task_id,ya.removeTabs(pa.fullPath),void _a.replace({path:pa.path,query:l})}if(pa.query.id){if("4-2"==pa.query.key)return void pl();const e=Array.isArray(pa.query.id)?pa.query.id[0]:pa.query.id;return yl.value.task_search=e,sessionStorage.setItem("task_id",e),void pl()}if(pa.query.project_id){const e=Array.isArray(pa.query.project_id)?pa.query.project_id[0]:pa.query.project_id;return yl.value.project_id=e,void pl()}Ga(),gl.value=wl(),window.addEventListener("resize",(()=>{gl.value=wl()})),pageFun(),getUpTaskList(),getOptionsApi(),taskId.value&&(formStatus.value=!1,initGetTask(),setTimeout((()=>{if(Ta.value.platform_type){const e=zl.value.find((e=>e.value===Ta.value.platform_type));ht.value=e?e.label:""}}),500))})),d((()=>{window.removeEventListener("beforeunload",(()=>{})),window.removeEventListener("resize",(()=>{gl.value=wl()}))}));const Sl=a([{label:"自运营",value:1},{label:"走单（不含代理服务费）",value:2},{label:"代下单（含代理服务费）",value:3},{label:"资源包订单",value:4},{label:"水下订单",value:5}]),zl=a([{label:"抖音",value:1},{label:"小红书",value:3},{label:"快手",value:4},{label:"B站",value:5},{label:"腾讯互选",value:6}]),ql=a([{label:"星推下单",value:"1"},{label:"集采线下下单",value:"2"}]),Al=a([{label:"请选择充值合同",value:""}]);let Il=null,Nl=0;const Ul=a(""),$l=u((()=>Ul.value?Al.value.filter((e=>e.label.toLowerCase().includes(Ul.value.toLowerCase()))):Al.value)),Ol=e=>(Ul.value=e,e.length>=2&&Ta.value.contract_id&&(Il&&clearTimeout(Il),Il=setTimeout((()=>{Dl(e)}),500)),!0),Pl=()=>{Ta.value.recharge_contract="",Ul.value=""},Dl=e=>{Ul.value=e,e&&e.length<=1||(Ra()?Ta.value.contract_id?Ll(Ta.value.contract_id,e,!0):I.warning("请先选择客户合同，然后才能搜索充值合同"):I.info("当前媒体平台不需要选择充值合同"))},Tl=a(!1),Ll=(e="",a="",l=!1)=>{if(!e)return Al.value=[],Ul.value="",void(Tl.value=!1);const t=ga.value?Ta.value.recharge_contract:null,r=Date.now();if(!(r-Nl>5e3)&&r-Nl<300)return Il&&clearTimeout(Il),void(Il=setTimeout((()=>{Nl=Date.now(),Ll(e,a,l)}),300));Nl=r,Tl.value=!0;R({customer_code:e,search:a||"",page:1,page_size:999}).then((r=>{if(ga.value,r&&990===r.code&&r.data){const o=Array.isArray(r.data.list)?r.data.list:[];if(0!==o.length||a){const e=o.map((e=>({label:`${e.recharge_contract_id} (${e.customer_contract_name||"无合同名称"})`,value:e.recharge_contract_id,id:e.id,customer_contract_name:e.customer_contract_name,customer_contract_number:e.customer_contract_number})));if(Al.value=e,Ul.value=a||"",a&&e.length>0&&!l){e.some((e=>e.value===a))||(Al.value.push({label:`${a} (未找到详细信息)`,value:a}),Ta.value.recharge_contract=a)}else 1!==e.length||Ta.value.recharge_contract||a||ga.value||(Ta.value.recharge_contract=e[0].value);ga.value&&t&&(Ta.value.recharge_contract=t)}else I.warning(`未找到客户合同 ${e} 对应的充值合同`),Al.value=[],Ul.value="",ga.value&&t&&(Ta.value.recharge_contract=t)}else Al.value=[],Ul.value="",ga.value&&t&&(Ta.value.recharge_contract=t),r&&r.msg&&"success"!==r.msg&&I.warning(`获取充值合同失败: ${r.msg}`);Tl.value=!1})).catch((e=>{I.error("获取充值合同列表失败，请稍后重试"),Al.value=[],Ul.value="",ga.value&&t&&(Ta.value.recharge_contract=t),Tl.value=!1}))};n((()=>Ta.value.platform_type),(e=>{Ra()?e&&Ta.value.contract_id&&Ll(Ta.value.contract_id,Ta.value.recharge_contract,!1):Ta.value.recharge_contract=""})),n((()=>Na.value),(e=>{})),n((()=>Ta.value.project_id),((e,a)=>{if(!ga.value&&e&&e!==a){Ta.value.customer_contract="",Ta.value.contract_id="",Ta.value.oa_project_id="",Ml.value="未知",Ta.value.recharge_contract="",Al.value=[],Ta.value.order_method="",Ta.value.account_id="",Ta.value.account_name="",Ta.value.account_brand_name="";const a=Za.value.find((a=>a.id===e));if(a){Ta.value.platform_type=a.project_platform||1,1===Number(Ta.value.platform_type)&&(Ta.value.task_type="");switch(Number(Ta.value.platform_type)){case 1:case 2:Ta.value.platform_type_entity="武汉星图新视界科技有限公司";break;case 3:Ta.value.platform_type_entity="薯鸿文化传媒（上海）有限公司";break;case 4:Ta.value.platform_type_entity="北京晨钟科技有限公司";break;case 5:Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌";break;case 6:Ta.value.platform_type_entity="深圳市腾讯文化传媒有限公司";break;default:Ta.value.platform_type_entity=""}const e=zl.value.find((e=>e.value===Ta.value.platform_type));ht.value=e?e.label:"",Ta.value.customer_entity=a.custom_name||"",a.custom_name||I.warning("所选项目缺少客户主体信息，请选择包含完整信息的项目"),a.usc_code&&(bl.value=!0,kl(a.usc_code,""))}}}));const Ml=a("未知"),El=u((()=>{const e=Za.value.find((e=>e.id===Ta.value.project_id)),a=e&&1===e.promote_type,l=Number(Ta.value.platform_type),t=Number(Ta.value.task_type),r=5===l;return ql.value.map((e=>"1"===e.value?{...e,disabled:1!==l||1===l&&[2,3].includes(t)||r}:"2"===e.value?{...e,disabled:a&&1===l&&1===t&&!r}:{...e,disabled:!1}))})),Jl=a(!1),Fl=()=>{Wl.value.customer_entity=Ta.value.customer_entity||"",ct(),Jl.value=!0},Bl=()=>{Wl.value.customer_entity?(Ta.value.customer_entity=Wl.value.customer_entity,Ta.value.hasOwnProperty("custom_id")&&Wl.value.custom_id&&(Ta.value.custom_id=Wl.value.custom_id),Ta.value.hasOwnProperty("usc_code")&&Wl.value.usc_code&&(Ta.value.usc_code=Wl.value.usc_code),Ta.value.contract_id="",Ta.value.customer_contract="",fa.value=[],Vl.value=null,Ta.value.recharge_contract="",Al.value=[],Wl.value.usc_code&&(bl.value=!0,kl(Wl.value.usc_code,"")),Jl.value=!1,I.success("客户主体已更新，合同列表已刷新")):I.warning("请选择客户主体")},Rl=a([]),Kl=a(!1);n((()=>Ta.value.order_method),(e=>{"2"!==e&&(Ta.value.account_id="",Ta.value.account_name="")}));const Wl=a({customer_entity:"",custom_id:void 0,custom_company:"",cust_abbr:"",usc_code:""}),Xl=a(!1),Yl=a(null),Gl=a(null),Hl=a(null),Ql=a(""),Zl=a({customer_account_id:"",account_name:"",customer_name:""}),et=a({currentPage:1,pageSize:10,total:0}),at=()=>{Xl.value=!0,Gl.value=null,Ql.value="",Ta.value.account_id?(tt(),Ql.value=Ta.value.account_id):lt()},lt=()=>{Zl.value={customer_account_id:"",account_name:"",customer_name:""},et.value.currentPage=1,Ql.value="",Gl.value=null,tt()},tt=()=>{Kl.value=!0;const e={account_name:Zl.value.account_name||"",customer_name:Zl.value.customer_name||"",customer_account_id:Zl.value.customer_account_id||"",page:et.value.currentPage,page_size:et.value.pageSize};K(e).then((e=>{if(Kl.value=!1,e&&990===e.code&&e.data){if(Rl.value=e.data.list||[],et.value.total=e.data.count||0,Ql.value){const e=Rl.value.find((e=>e.id===Ql.value));e&&Yl.value&&(Yl.value.setCurrentRow(e),Gl.value=e)}}else Rl.value=[],et.value.total=0,e&&e.msg&&I.warning(e.msg)})).catch((e=>{Kl.value=!1,I.error("获取账户列表失败"),Rl.value=[],et.value.total=0}))},rt=e=>{et.value.pageSize=e,tt()},ot=e=>{et.value.currentPage=e,tt()},nt=e=>{Gl.value=e,e&&(Ql.value=e.id)},ut=()=>{if(!Gl.value)return void I.warning("请选择一个账户");Ta.value.account_id=Gl.value.id;const e=Gl.value,a=`${e.customer_account_id} | ${e.account_name}${e.brand_name?" | "+e.brand_name:""}`;Ta.value.account_name=a,Ta.value.account_brand_name=e.brand_name||"",Hl.value=JSON.parse(JSON.stringify(Gl.value)),Xl.value=!1,I.success(`已选择账户: ${e.account_name}`)},it=e=>{if(e){st.value=!0,W({search:e,page:1,page_size:10}).then((e=>{var a;st.value=!1,(null==(a=e.data)?void 0:a.lists)&&(dt.value=e.data.lists)}))}else ct()},ct=()=>{st.value=!0;W({search:"",page:1,page_size:10}).then((e=>{var a;st.value=!1,(null==(a=e.data)?void 0:a.lists)&&(dt.value=e.data.lists)}))},dt=a([]),st=a(!1),vt=e=>{if(dt.value.length){const a=dt.value.find((a=>e===a.custom_name));a&&(Wl.value.customer_entity=a.custom_name,Wl.value.custom_id=a.custom_id,Wl.value.custom_company=a.custom_company||"",Wl.value.cust_abbr=a.cust_abbr||"",Wl.value.usc_code=a.usc_code||"")}},_t=(e,a)=>{if(!e||!e.length)return null;const l=e.filter((e=>e.reviewer_level===a));return l.length>0?l[0]:null},pt=()=>{const e=Hl.value;return e?`\n      <div style="text-align: left;">\n        <div><strong>所属公司:</strong> ${e.company||"-"}</div>\n        <div><strong>媒体平台:</strong> ${e.media_platform||"-"}</div>\n        <div><strong>客户名称:</strong> ${e.customer_name||"-"}</div>\n        <div><strong>账户ID:</strong> ${e.customer_account_id||"-"}</div>\n        <div><strong>账户名称:</strong> ${e.account_name||"-"}</div>\n        <div><strong>品牌名称:</strong> ${e.brand_name||"-"}</div>\n      </div>\n    `:`<div>账户信息: ${Ta.value.account_name||"-"}</div>`},mt=()=>{const e=pa.query.task_id;if(e){const a=Array.isArray(e)?e[0]:String(e);sessionStorage.setItem("task_search_debug",a);const l={id:a,page:1,page_size:10},t={task_id:a,page:1,page_size:10};D({task_search:a,page:1,page_size:10}).then((e=>(ft(e,a,"Test 1"),D(l)))).then((e=>(ft(e,a,"Test 2"),D(t)))).then((e=>{ft(e,a,"Test 3")})).catch((e=>{}))}},ft=(e,a,l)=>{var t;if(null==(t=null==e?void 0:e.data)?void 0:t.tasks){e.data.tasks.some((e=>String(e.id)===a))}},yt=a(!0),gt=()=>{if(!Ta.value.contract_id)return"请先选择客户合同";if(!Ra())return"当前媒体平台不需要选择充值合同";const e=Number(Ta.value.platform_type);return 2===e?"请选择抖音星立方的充值合同":3===e?"请选择小红书的充值合同":5===e?"请选择B站的充值合同":"请点击选择充值合同"},wt=e=>{var a;if(!e)return"";const l=e.project_platform||1,t=1==e.promote_type?"星立方":"其他",r=(null==(a=zl.value.find((e=>e.value===l)))?void 0:a.label)||"";return 1===l?`${r}-${t}-${e.project_name}`:`${r}-${e.project_name}`};n((()=>Ta.value.project_id),((e,a)=>{if(!ga.value&&e&&e!==a){Ta.value.customer_contract="",Ta.value.contract_id="",Ta.value.oa_project_id="",Ml.value="未知",Ta.value.recharge_contract="",Al.value=[],Ta.value.order_method="",Ta.value.account_id="",Ta.value.account_name="",Ta.value.account_brand_name="";const a=Za.value.find((a=>a.id===e));if(a){Ta.value.platform_type=a.project_platform||1,1===Number(Ta.value.platform_type)&&(Ta.value.task_type="");switch(Number(Ta.value.platform_type)){case 1:case 2:Ta.value.platform_type_entity="武汉星图新视界科技有限公司";break;case 3:Ta.value.platform_type_entity="薯鸿文化传媒（上海）有限公司";break;case 4:Ta.value.platform_type_entity="北京晨钟科技有限公司";break;case 5:Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌";break;case 6:Ta.value.platform_type_entity="深圳市腾讯文化传媒有限公司";break;default:Ta.value.platform_type_entity=""}const e=zl.value.find((e=>e.value===Ta.value.platform_type));ht.value=e?e.label:"",Ta.value.customer_entity=a.custom_name||"",Ta.value.usc_code=a.usc_code||"",a.custom_name||I.warning("所选项目缺少客户主体信息，请选择包含完整信息的项目"),a.usc_code&&(bl.value=!0,kl(a.usc_code,""))}}}));const ht=a(""),bt=()=>{const e=Number(Ta.value.platform_type),a=Za.value.find((e=>e.id===Ta.value.project_id));if(1===e){return a&&1===a.promote_type?Ja.value.douyin.map((e=>({...e,disabled:"1"!==e.value}))):Ja.value.douyin}return 5===e?Ja.value.bilibili:[]};n((()=>Ta.value.platform_type),((e,a)=>{if(ga.value)return;const l=Number(e||0),t=zl.value.find((e=>e.value===l));switch(ht.value=t?t.label:"",l){case 1:case 2:Ta.value.platform_type_entity="武汉星图新视界科技有限公司";break;case 3:Ta.value.platform_type_entity="薯鸿文化传媒（上海）有限公司";break;case 4:Ta.value.platform_type_entity="北京晨钟科技有限公司";break;case 5:Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌";break;case 6:Ta.value.platform_type_entity="深圳市腾讯文化传媒有限公司";break;default:Ta.value.platform_type_entity=""}Ra()?Ta.value.contract_id&&Ll(Ta.value.contract_id,Ta.value.recharge_contract,!1):(Ta.value.recharge_contract="",Al.value=[])})),n((()=>Ta.value.task_type),(e=>{if(ga.value)return;const a=Za.value.find((e=>e.id===Ta.value.project_id)),l=a&&1===a.promote_type,t=Number(Ta.value.platform_type),r=Number(e);1===t&&[2,3].includes(r)?Ta.value.order_method="2":l&&1===t&&1===r&&(Ta.value.order_method="1")}));const kt=e=>{const a=Number(e.platform_type);return 1===a||5===a},jt=e=>{if(!e.task_type)return"";const a=Number(e.platform_type),l=Number(e.task_type);if(1===a)switch(l){case 1:return"指派";case 2:return"招募";case 3:return"投稿";default:return""}if(5===a)switch(l){case 1:return"京火";case 2:return"花火";default:return""}return Ea[l]||""},Vt=()=>{Ra()?Ta.value.contract_id?(Il&&(clearTimeout(Il),Il=null),Nl=0,Ll(Ta.value.contract_id,"",!0),Ul.value=""):I.warning("请先选择客户合同，然后才能选择充值合同"):I.info("当前媒体平台不需要选择充值合同")};return(e,a)=>{const l=s("el-button"),t=s("el-checkbox"),r=s("el-checkbox-group"),o=s("el-tag"),n=s("el-table-column"),u=s("el-avatar"),i=s("MoreFilled"),c=s("el-icon"),d=s("el-tooltip"),$=s("el-table"),D=s("el-pagination"),T=s("el-option"),B=s("el-select"),R=s("el-input-number"),K=s("el-dialog"),W=s("el-form-item"),X=s("el-col"),pa=s("el-input"),ya=s("el-row"),ba=s("el-form"),za=s("el-drawer"),Ia=s("el-radio"),Ea=v("loading");return p(),_("div",G,[m(y(O,{onSearch:Ga,onReset:Qa,columns:ol.value,"search-param":Ca.value,"search-col":Sa},null,8,["columns","search-param"]),[[g,xa.value&&Ka.isSearch]]),f("div",H,[m((p(),_("div",Q,[f("div",Z,[m(y(l,{type:"primary",onClick:a[0]||(a[0]=e=>(()=>{switch(Na.value=!0,Ua.value=0,ga.value=!1,Ta.value={project_id:"",task_name:"",promotion_platforms_genres:1,task_type:"",settlement_method:1,order_type:1,oa_project_id:"",customer_entity:"",customer_contract:"",contract_id:"",usc_code:"",performance_owner:"",performance_department:"",platform_type:0,platform_type_entity:"",order_method:"",recharge_contract:"",remark:"",account_id:"",account_name:"",account_brand_name:"",customer_code:"",approval_type_text:"",contract_type:"",media_or_channel:"",contract_sign_date:"",contract_start_date:"",contract_end_date:"",contract_amount:"",contract_relative_name:"",company:"",oa_project_name:"",operator:"",department:""},Number(Ta.value.platform_type)){case 1:case 2:Ta.value.platform_type_entity="武汉星图新视界科技有限公司";break;case 3:Ta.value.platform_type_entity="薯鸿文化传媒（上海）有限公司";break;case 4:Ta.value.platform_type_entity="北京晨钟科技有限公司";break;case 5:Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌";break;case 6:Ta.value.platform_type_entity="深圳市腾讯文化传媒有限公司";break;default:Ta.value.platform_type_entity=""}return void el()})())},{default:w((()=>a[40]||(a[40]=[h("新建任务")]))),_:1},512),[[g,Ka.isSearch]])]),f("div",ee,[y(r,{modelValue:Aa.value,"onUpdate:modelValue":a[1]||(a[1]=e=>Aa.value=e),size:"default",onChange:Wa},{default:w((()=>[(p(!0),_(b,null,k(Da.value,(e=>(p(),j(t,{disabled:!e.count,class:"task-detail-tabs-filter-item",key:e.value,label:e.value},{default:w((()=>[h(V(e.label),1)])),_:2},1032,["disabled","label"])))),128))])),_:1},8,["modelValue"])]),y($,{data:Pa.value,style:{width:"100%",height:"calc(100% - 100px)"}},{default:w((()=>[y(n,{prop:"date",label:"任务信息",width:"240px"},{default:w((e=>[f("div",null,V(e.row.task_name),1),f("div",ae,[f("div",null,"任务ID："+V(e.row.id),1),f("div",null,"创建时间："+V(e.row.created_at),1),f("div",le,[kt(e.row)?(p(),j(o,{key:0,class:"mr-2",type:"success",size:"small"},{default:w((()=>[h(V(jt(e.row)),1)])),_:2},1024)):x("",!0),y(o,{size:"small"},{default:w((()=>[h(V(La.value[e.row.platform_type]),1)])),_:2},1024)])])])),_:1}),y(n,{prop:"date",label:"项目信息",width:"220px"},{default:w((e=>{var a,l,t,r;return[f("div",null,V(null==(l=null==(a=null==e?void 0:e.row)?void 0:a.project)?void 0:l.project_name),1),f("div",te,"项目ID："+V(null==(t=null==e?void 0:e.row)?void 0:t.project_id),1),f("div",re,"创建时间："+V(null==(r=null==e?void 0:e.row)?void 0:r.created_at),1)]})),_:1}),y(n,{prop:"total_predict_receivable_customer_price",label:"任务预估金额",width:"120"},{default:w((e=>{var a,l;return[h(" ¥ "+V(null==(l=(null==(a=null==e?void 0:e.row)?void 0:a.total_predict_receivable_customer_price)||0)?void 0:l.toLocaleString()),1)]})),_:1}),y(n,{prop:"date",label:"总毛利率",width:"100px"},{default:w((e=>[f("div",null,V((e.row.total_gross?e.row.total_gross:0).toFixed(2)+"%"),1)])),_:1}),y(n,{prop:"date",label:"客户信息",width:"240px"},{default:w((e=>{var a,l;return[f("div",null,V(null==(l=null==(a=null==e?void 0:e.row)?void 0:a.project)?void 0:l.custom_name),1)]})),_:1}),y(n,{prop:"name",label:"达人信息",width:"200px"},{default:w((e=>{var l,t,r,o,n,i,c,d,s;return[(null==(t=null==(l=null==e?void 0:e.row)?void 0:l.process_kol)?void 0:t.length)>1?(p(),_("div",oe,[(p(!0),_(b,null,k(null==(r=null==e?void 0:e.row)?void 0:r.process_kol.filter(((e,a)=>a<=2)),(e=>(p(),j(u,{key:e.platform_uid,class:"avatar",shape:"circle",size:"small",src:null==e?void 0:e.kol_photo},null,8,["src"])))),128)),m(f("div",{class:"number-avatar"}," +"+V((null==(o=null==e?void 0:e.row)?void 0:o.process_kol.length)-3),513),[[g,(null==(i=null==(n=null==e?void 0:e.row)?void 0:n.process_kol)?void 0:i.length)>3]])])):1==(null==(d=null==(c=null==e?void 0:e.row)?void 0:c.process_kol)?void 0:d.length)?(p(),_("div",ne,[(p(!0),_(b,null,k(null==(s=null==e?void 0:e.row)?void 0:s.process_kol,(e=>(p(),_("div",{style:{display:"flex","align-items":"center"},key:null==e?void 0:e.platform_uid},[f("div",ue,[y(u,{class:"avatar",shape:"circle",size:"small",src:null==e?void 0:e.kol_photo},null,8,["src"])]),f("p",null,V(null==e?void 0:e.kol_name),1)])))),128))])):(p(),_("div",ie,a[41]||(a[41]=[f("div",{class:"avatar-stack-name"},"无",-1)])))]})),_:1}),y(n,{prop:"amount",label:"参与人员",width:"130"},{default:w((e=>[y(d,{placement:"bottom",effect:"light"},{content:w((()=>{var a,l,t,r,o,n,u,i,c,d,s,v,y,w,j,S,z,q,A,I,N,U,$,O,P,D,T,L;return[2===(null==(a=null==e?void 0:e.row)?void 0:a.status)?(p(),_("p",ce,"信息填写人员："+V((null==(t=null==(l=null==e?void 0:e.row)?void 0:l.participants)?void 0:t.information_personnel)||"-"),1)):5===(null==(r=null==e?void 0:e.row)?void 0:r.status)||3===(null==(o=null==e?void 0:e.row)?void 0:o.status)?(p(),_(b,{key:1},[f("p",null,"信息填写人员："+V((null==(n=null==e?void 0:e.row.participants)?void 0:n.information_personnel)||"-"),1),(null==(u=null==e?void 0:e.row)?void 0:u.reviewers)&&(null==(i=null==e?void 0:e.row)?void 0:i.reviewers.length)?(p(!0),_(b,{key:0},k([...new Set(null==(c=null==e?void 0:e.row)?void 0:c.reviewers.map((e=>e.reviewer_level)))].sort(),(a=>{var l,t,r,o,n,u,i;return p(),_("p",null,[h(V(wa(a))+"： ",1),"string"==typeof ha(null==(l=null==e?void 0:e.row)?void 0:l.reviewers,a)?(p(),_(b,{key:0},[h(V(ha(null==(t=null==e?void 0:e.row)?void 0:t.reviewers,a)),1)],64)):(p(),_(b,{key:1},[f("span",de,V(ha(null==(r=null==e?void 0:e.row)?void 0:r.reviewers,a).displayNames),1),ha(null==(o=null==e?void 0:e.row)?void 0:o.reviewers,a).hasMore?(p(),_(b,{key:0},[ja(null==(n=null==e?void 0:e.row)?void 0:n.id,a)?(p(),_(b,{key:1},[f("div",ve,V(ha(null==(i=null==e?void 0:e.row)?void 0:i.reviewers,a).nameList.join("、")),1),f("span",{class:"show-less",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (收起) ",8,_e)],64)):(p(),_("span",{key:0,class:"show-more",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (共"+V(ha(null==(u=null==e?void 0:e.row)?void 0:u.reviewers,a).totalCount)+"人，点击展开) ",9,se))],64)):x("",!0)],64))])})),256)):x("",!0)],64)):4===(null==(d=null==e?void 0:e.row)?void 0:d.status)?(p(),_(b,{key:2},[f("p",null,"信息填写人员："+V((null==(s=null==e?void 0:e.row.participants)?void 0:s.information_personnel)||"-"),1),(null==(v=null==e?void 0:e.row)?void 0:v.reviewers)&&(null==(y=null==e?void 0:e.row)?void 0:y.reviewers.length)?(p(!0),_(b,{key:0},k([...new Set(null==(w=null==e?void 0:e.row)?void 0:w.reviewers.map((e=>e.reviewer_level)))].sort(),(a=>{var l,t,r,o,n,u,i;return p(),_("p",null,[h(V(wa(a))+"： ",1),"string"==typeof ha(null==(l=null==e?void 0:e.row)?void 0:l.reviewers,a)?(p(),_(b,{key:0},[h(V(ha(null==(t=null==e?void 0:e.row)?void 0:t.reviewers,a)),1)],64)):(p(),_(b,{key:1},[f("span",pe,V(ha(null==(r=null==e?void 0:e.row)?void 0:r.reviewers,a).displayNames),1),ha(null==(o=null==e?void 0:e.row)?void 0:o.reviewers,a).hasMore?(p(),_(b,{key:0},[ja(null==(n=null==e?void 0:e.row)?void 0:n.id,a)?(p(),_(b,{key:1},[f("div",fe,V(ha(null==(i=null==e?void 0:e.row)?void 0:i.reviewers,a).nameList.join("、")),1),f("span",{class:"show-less",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (收起) ",8,ye)],64)):(p(),_("span",{key:0,class:"show-more",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (共"+V(ha(null==(u=null==e?void 0:e.row)?void 0:u.reviewers,a).totalCount)+"人，点击展开) ",9,me))],64)):x("",!0)],64))])})),256)):x("",!0)],64)):6===(null==(j=null==e?void 0:e.row)?void 0:j.status)?(p(),_(b,{key:3},[f("p",null,"信息填写人员："+V((null==(S=null==e?void 0:e.row.participants)?void 0:S.information_personnel)||"-"),1),(null==(z=null==e?void 0:e.row)?void 0:z.reviewers)&&(null==(q=null==e?void 0:e.row)?void 0:q.reviewers.length)?(p(!0),_(b,{key:0},k([...new Set(null==(A=null==e?void 0:e.row)?void 0:A.reviewers.map((e=>e.reviewer_level)))].sort(),(a=>{var l,t,r,o,n,u,i;return p(),_("p",null,[h(V(wa(a))+"： ",1),"string"==typeof ha(null==(l=null==e?void 0:e.row)?void 0:l.reviewers,a)?(p(),_(b,{key:0},[h(V(ha(null==(t=null==e?void 0:e.row)?void 0:t.reviewers,a)),1)],64)):(p(),_(b,{key:1},[f("span",ge,V(ha(null==(r=null==e?void 0:e.row)?void 0:r.reviewers,a).displayNames),1),ha(null==(o=null==e?void 0:e.row)?void 0:o.reviewers,a).hasMore?(p(),_(b,{key:0},[ja(null==(n=null==e?void 0:e.row)?void 0:n.id,a)?(p(),_(b,{key:1},[f("div",he,V(ha(null==(i=null==e?void 0:e.row)?void 0:i.reviewers,a).nameList.join("、")),1),f("span",{class:"show-less",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (收起) ",8,be)],64)):(p(),_("span",{key:0,class:"show-more",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (共"+V(ha(null==(u=null==e?void 0:e.row)?void 0:u.reviewers,a).totalCount)+"人，点击展开) ",9,we))],64)):x("",!0)],64))])})),256)):x("",!0),m(f("p",null,"下单人员："+V((null==(I=null==e?void 0:e.row)?void 0:I.order_created_name)||"-"),513),[[g,null==(N=null==e?void 0:e.row)?void 0:N.order_created_name]])],64)):7===(null==(U=null==e?void 0:e.row)?void 0:U.status)?(p(),_(b,{key:4},[f("p",null,"信息填写人员："+V((null==($=null==e?void 0:e.row.participants)?void 0:$.information_personnel)||"-"),1),(null==(O=null==e?void 0:e.row)?void 0:O.reviewers)&&(null==(P=null==e?void 0:e.row)?void 0:P.reviewers.length)?(p(!0),_(b,{key:0},k([...new Set(null==(D=null==e?void 0:e.row)?void 0:D.reviewers.map((e=>e.reviewer_level)))].sort(),(a=>{var l,t,r,o,n,u,i;return p(),_("p",null,[h(V(wa(a))+"： ",1),"string"==typeof ha(null==(l=null==e?void 0:e.row)?void 0:l.reviewers,a)?(p(),_(b,{key:0},[h(V(ha(null==(t=null==e?void 0:e.row)?void 0:t.reviewers,a)),1)],64)):(p(),_(b,{key:1},[f("span",ke,V(ha(null==(r=null==e?void 0:e.row)?void 0:r.reviewers,a).displayNames),1),ha(null==(o=null==e?void 0:e.row)?void 0:o.reviewers,a).hasMore?(p(),_(b,{key:0},[ja(null==(n=null==e?void 0:e.row)?void 0:n.id,a)?(p(),_(b,{key:1},[f("div",Ve,V(ha(null==(i=null==e?void 0:e.row)?void 0:i.reviewers,a).nameList.join("、")),1),f("span",{class:"show-less",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (收起) ",8,xe)],64)):(p(),_("span",{key:0,class:"show-more",onClick:C((l=>{var t;return ka(null==(t=null==e?void 0:e.row)?void 0:t.id,a)}),["stop"])}," (共"+V(ha(null==(u=null==e?void 0:e.row)?void 0:u.reviewers,a).totalCount)+"人，点击展开) ",9,je))],64)):x("",!0)],64))])})),256)):x("",!0),m(f("p",null,"下单人员："+V((null==(T=null==e?void 0:e.row)?void 0:T.order_created_name)||"-"),513),[[g,null==(L=null==e?void 0:e.row)?void 0:L.order_created_name]])],64)):x("",!0)]})),default:w((()=>{var a,l,t,r,o,n,u,d,s,v,k,j,C,S,z,q,A,I,N,U,$,O,P;return[f("p",Ce,[m(f("span",null,V(null==(a=null==e?void 0:e.row)?void 0:a.created_name),513),[[g,0===(null==(l=null==e?void 0:e.row)?void 0:l.status)]]),m(f("span",null,V(null==(t=null==e?void 0:e.row)?void 0:t.participants.alliance_personnel),513),[[g,1===(null==(r=null==e?void 0:e.row)?void 0:r.status)]]),m(f("span",null,V(null==(o=null==e?void 0:e.row)?void 0:o.participants.information_personnel),513),[[g,2===(null==(n=null==e?void 0:e.row)?void 0:n.status)]]),m(f("span",null,[((null==(u=null==e?void 0:e.row)?void 0:u.reviewers)||[]).length>1?(p(),_(b,{key:0},[h(V((null==(v=_t(null==(d=null==e?void 0:e.row)?void 0:d.reviewers,null==(s=null==e?void 0:e.row)?void 0:s.level))?void 0:v.reviewer_personnel)||"-"),1)],64)):x("",!0)],512),[[g,3===(null==(k=null==e?void 0:e.row)?void 0:k.status)||5==(null==(j=null==e?void 0:e.row)?void 0:j.status)]]),m(f("span",null,[((null==(C=null==e?void 0:e.row)?void 0:C.reviewers)||[]).length>1?(p(),_(b,{key:0},[h(V((null==(I=_t(null==(S=null==e?void 0:e.row)?void 0:S.reviewers,null==(A=null==(q=null==(z=null==e?void 0:e.row)?void 0:z.reviewers)?void 0:q[0])?void 0:A.reviewer_level))?void 0:I.reviewer_personnel)||"-"),1)],64)):x("",!0)],512),[[g,4===(null==(N=null==e?void 0:e.row)?void 0:N.status)]]),m(f("span",null,V(null==($=null==(U=null==e?void 0:e.row)?void 0:U.participants)?void 0:$.information_personnel),513),[[g,6===(null==(O=null==e?void 0:e.row)?void 0:O.status)||7===(null==(P=null==e?void 0:e.row)?void 0:P.status)]]),y(c,{style:{transform:"rotate(90deg)"}},{default:w((()=>[y(i)])),_:1})])]})),_:2},1024)])),_:1}),y(n,{prop:"stats",label:"任务状态",align:"center",width:"140"},{default:w((e=>{var a,l,t;return[4==(null==(a=null==e?void 0:e.row)?void 0:a.status)?(p(),_("div",Se,[h(V(Fa.value[null==(l=null==e?void 0:e.row)?void 0:l.status])+" ",1),f("p",{class:"cursor-pointer color-blue500",onClick:a=>{return l=null==e?void 0:e.row,$a.value=!0,void(Oa.value=l.last_rejected);var l}},"查看拒绝原因",8,ze)])):(p(),_("div",qe,V(Fa.value[null==(t=null==e?void 0:e.row)?void 0:t.status]),1))]})),_:1}),y(n,{label:"操作","min-width":"180px",fixed:"right",align:"center"},{default:w((e=>{var t,r,o,n,u,i,c,d,s,v,_,p,f,b,k,j,V,x,C,S,z,q,$,O,P,D,T,M,E,B,R;return[m(y(l,{type:"text",disabled:!(null==(t=e.row)?void 0:t.is_owner_status),class:"cursor-pointer color-blue500",onClick:a=>nl("selectAuthor",null==e?void 0:e.row)},{default:w((()=>a[42]||(a[42]=[h("去选号")]))),_:2},1032,["disabled","onClick"]),[[g,0==(null==(r=e.row)?void 0:r.status)&&(null==(o=e.row)?void 0:o.is_owner_status)]]),m(y(l,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:a=>{var l;(2==(l=null==e?void 0:e.row).status||3==l.status)&&_a.push({path:"/business/task/informationAuthor",query:{page_type:"edit",id:l.id}})}},{default:w((()=>a[43]||(a[43]=[h("修改")]))),_:2},1032,["onClick"]),[[g,(null==(n=e.row)?void 0:n.is_update)&&3==(null==(u=e.row)?void 0:u.status)]]),m(y(l,{type:"text",disabled:!(null==(i=e.row)?void 0:i.is_owner_status),class:"cursor-pointer color-blue500 ml-2",onClick:a=>(async e=>{var a,l,t,r;Na.value=!0,Ua.value=1,ga.value=!0;const o=A.service({fullscreen:!0,text:"加载任务数据中...",background:"rgba(0, 0, 0, 0.7)"});try{ll(e.project_id);const o=(null==(a=e.participants)?void 0:a.customer_code)||e.customer_code||"",n=e.recharge_contract_id||e.recharge_contract||"",u=Number(e.platform_type||0),i=Number(e.task_type||0),c=Number(e.order_method||0);let d=e.platform_type_entity||"";if(!d||5===u&&"上海东方传媒（集团）有限公司‌‌"!==d)switch(u){case 1:case 2:d="武汉星图新视界科技有限公司";break;case 3:d="薯鸿文化传媒（上海）有限公司";break;case 4:d="北京晨钟科技有限公司";break;case 5:d="上海东方传媒（集团）有限公司‌‌";break;case 6:d="深圳市腾讯文化传媒有限公司"}Ta.value={project_id:e.project_id,task_name:e.task_name,promotion_platforms_genres:1,task_type:String(i||""),settlement_method:1,order_type:Number(e.order_type||1),oa_project_id:e.oa_project_id||"",customer_entity:(null==(l=e.project)?void 0:l.custom_name)||e.customer_entity||"",customer_contract:e.customer_contract||"",contract_id:o,usc_code:(null==(t=e.project)?void 0:t.usc_code)||"",performance_owner:e.performance_owner||"",performance_department:e.performance_department||"",platform_type:u,platform_type_entity:d,order_method:String(c||""),recharge_contract:n,remark:e.remark||"",account_id:e.account_id||"",account_name:e.account_name||"",account_brand_name:e.account_brand_name||"",customer_code:o,approval_type_text:e.approval_type_text||"",contract_type:e.contract_type||"",media_or_channel:e.media_or_channel||"",contract_sign_date:e.contract_sign_date||"",contract_start_date:e.contract_start_date||"",contract_end_date:e.contract_end_date||"",contract_amount:e.contract_amount||"",contract_relative_name:e.contract_relative_name||"",company:e.company||"",oa_project_name:e.oa_project_name||"",operator:e.operator||"",department:e.department||""};const s=zl.value.find((e=>e.value===u));if(ht.value=s?s.label:"",e.id&&(Ta.value.task_id=e.id),setTimeout((()=>{5===u&&(Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌")}),500),!e.account_id||"2"!==e.order_method&&5!==u||(Ta.value.account_id=e.account_id,Ta.value.account_name=e.account_name,Ta.value.account_brand_name=e.account_brand_name||"",5===u&&(Ta.value.order_method="2")),n&&o&&(Il&&(clearTimeout(Il),Il=null),Nl=0,Ta.value.recharge_contract=n,Ll(o,n,!1),setTimeout((()=>{Ta.value.recharge_contract||(Ta.value.recharge_contract=n)}),500)),e.project&&e.project.usc_code)await new Promise((a=>{bl.value=!0,L({usc_code:e.project.usc_code,contract_id:o||"",page:1,page_size:999}).then((l=>{if(l&&990===l.code&&l.data){const a=l.data,t=Array.isArray(a.lists)?a.lists:[];fa.value=t;const r=fa.value.find((e=>e.contract_id===o));r?(Vl.value=r,Cl(o)):Vl.value={contract_id:o,contract_name:e.customer_contract||"",contract_sign_date:e.contract_sign_date||"",contract_start_date:e.contract_start_date||"",contract_end_date:e.contract_end_date||"",project_name:e.oa_project_name||""}}else Vl.value={contract_id:o,contract_name:e.customer_contract||"",contract_sign_date:e.contract_sign_date||"",contract_start_date:e.contract_start_date||"",contract_end_date:e.contract_end_date||"",project_name:e.oa_project_name||""};bl.value=!1,a(!0)})).catch((e=>{bl.value=!1,a(!0)}))}));else{await new Promise((e=>setTimeout(e,300)));const a=Za.value.find((a=>a.id===e.project_id));a&&a.usc_code?(await new Promise((e=>{bl.value=!0,L({usc_code:a.usc_code,contract_id:o||"",page:1,page_size:999}).then((a=>{if(a&&990===a.code&&a.data){const e=a.data,l=Array.isArray(e.lists)?e.lists:[];fa.value=l;const t=fa.value.find((e=>e.contract_id===o));t&&(Vl.value=t,Cl(o))}bl.value=!1,e(!0)})).catch((a=>{bl.value=!1,e(!0)}))})),Ta.value.customer_entity=a.custom_name||(null==(r=e.project)?void 0:r.custom_name)||e.customer_entity||""):I.warning("无法加载合同信息：缺少项目USC代码")}setTimeout((()=>{n&&!Ta.value.recharge_contract&&(Ta.value.recharge_contract=n),N((()=>{i&&Ta.value.task_type!==String(i)&&(Ta.value.task_type=String(i)),c&&Ta.value.order_method!==String(c)&&(Ta.value.order_method=String(c)),5===u&&(Ta.value.platform_type_entity="上海东方传媒（集团）有限公司‌‌",Ta.value.order_method="2"),ga.value=!1}))}),1500)}catch(n){I.error("加载任务数据失败"),ga.value=!1}finally{o.close()}})(null==e?void 0:e.row)},{default:w((()=>a[44]||(a[44]=[h("编辑")]))),_:2},1032,["disabled","onClick"]),[[g,!((null==(c=e.row)?void 0:c.status)>=5||3==(null==(d=e.row)?void 0:d.status))&&(null==(s=e.row)?void 0:s.is_owner_status)]]),m(y(l,{type:"text",disabled:!(null==(v=e.row)?void 0:v.is_owner_status),class:"cursor-pointer color-blue500",onClick:a=>nl("informationAuthor",null==e?void 0:e.row)},{default:w((()=>a[45]||(a[45]=[h("去填写")]))),_:2},1032,["disabled","onClick"]),[[g,2==(null==(_=e.row)?void 0:_.status)&&(null==(p=e.row)?void 0:p.is_owner_status)]]),m(y(l,{type:"text",disabled:!(null==(f=e.row)?void 0:f.is_update),class:"cursor-pointer color-blue500",onClick:a=>nl("informationAuthor",null==e?void 0:e.row)},{default:w((()=>a[46]||(a[46]=[h("去修改")]))),_:2},1032,["disabled","onClick"]),[[g,4==(null==(b=e.row)?void 0:b.status)&&(null==(k=e.row)?void 0:k.is_update)]]),m(y(l,{type:"text",disabled:!(null==(j=e.row)?void 0:j.is_owner_status),class:"cursor-pointer color-blue500",onClick:a=>nl("examineAuthor",null==e?void 0:e.row)},{default:w((()=>a[47]||(a[47]=[h("去审核")]))),_:2},1032,["disabled","onClick"]),[[g,(3==(null==(V=e.row)?void 0:V.status)||5==(null==(x=e.row)?void 0:x.status))&&(null==(C=e.row)?void 0:C.is_owner_status)]]),m(y(l,{type:"text",disabled:!(null==(S=e.row)?void 0:S.is_owner_status)||1!=(null==(z=e.row)?void 0:z.order_method),class:"cursor-pointer color-blue500",onClick:a=>nl("createOrder",null==e?void 0:e.row)},{default:w((()=>a[48]||(a[48]=[h("去下单")]))),_:2},1032,["disabled","onClick"]),[[g,6==(null==(q=e.row)?void 0:q.status)&&(null==($=e.row)?void 0:$.is_owner_status)]]),y(l,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:a=>{var l;2==(l=null==e?void 0:e.row).status?_a.push({path:"/business/task/informationAuthor",query:{page_type:"info",id:l.id}}):3!=l.status&&4!=l.status&&5!=l.status&&6!=l.status&&7!=l.status||_a.push({path:"/business/task/informationAuthor",query:{page_type:"info",id:l.id}})}},{default:w((()=>a[49]||(a[49]=[h("详情")]))),_:2},1032,["onClick"]),m(y(l,{type:"text",class:"cursor-pointer color-blue500 ml-2",onClick:a=>{return l=null==e?void 0:e.row,void ma.sendMessage("urgent_message",JSON.stringify({task_id:l.id}));var l}},{default:w((()=>a[50]||(a[50]=[h("催办")]))),_:2},1032,["onClick"]),[[g,(null==(O=e.row)?void 0:O.created_id)==Va.value&&(3==(null==(P=e.row)?void 0:P.status)||5==(null==(D=e.row)?void 0:D.status))]]),m(y(l,{type:"text",disabled:!(null==(T=e.row)?void 0:T.withdraw_task_permission),class:"cursor-pointer color-blue500",onClick:a=>{return l=null==e?void 0:e.row,void U.confirm("确认撤回该任务吗？").then((()=>{F({task_id:l.id}).then((e=>{990==e.code&&(I.success("撤回成功！"),Aa.value=["2"],pl())}))})).catch((()=>{}));var l}},{default:w((()=>a[51]||(a[51]=[h("撤回")]))),_:2},1032,["disabled","onClick"]),[[g,3==(null==(M=e.row)?void 0:M.status)&&(null==(E=e.row)?void 0:E.withdraw_task_permission)]]),m(y(l,{type:"text",disabled:!(null==(B=e.row)?void 0:B.delete_task_permission),class:"cursor-pointer color-blue500",onClick:a=>{return l=null==e?void 0:e.row,void U.confirm("确认删除该任务吗？").then((()=>{J({task_id:l.id}).then((e=>{990==e.code&&(I.success("删除成功！"),Aa.value=[""],pl())}))})).catch((()=>{}));var l}},{default:w((()=>a[52]||(a[52]=[h("删除")]))),_:2},1032,["disabled","onClick"]),[[g,null==(R=e.row)?void 0:R.delete_task_permission]])]})),_:1})])),_:1},8,["data"]),f("div",Ae,[y(D,{"current-page":yl.value.page,"onUpdate:currentPage":a[2]||(a[2]=e=>yl.value.page=e),"page-size":yl.value.page_size,"onUpdate:pageSize":a[3]||(a[3]=e=>yl.value.page_size=e),total:fl.total,"page-sizes":[10,20,30,50],onSizeChange:Xa,onCurrentChange:Ya,background:"",layout:"total ,sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])])),[[Ea,qa.value]])]),y(K,{modelValue:ul.value,"onUpdate:modelValue":a[7]||(a[7]=e=>ul.value=e),title:"下单信息"},{footer:w((()=>[f("div",Oe,[y(t,{modelValue:sl.value,"onUpdate:modelValue":a[5]||(a[5]=e=>sl.value=e),onChange:_l},{default:w((()=>a[53]||(a[53]=[h(" 全选未下单达人")]))),_:1},8,["modelValue"]),y(l,{type:"primary",onClick:a[6]||(a[6]=e=>(()=>{const e=il.value.filter(((e,a)=>dl.value[a])).map((e=>e.id.toString()));e.length>0?(localStorage.setItem("kol_info",JSON.stringify(e)),_a.push({path:"/business/task/createOrder",query:{page_type:"add",id:cl.value}})):I.warning("请选择达人")})())},{default:w((()=>a[54]||(a[54]=[h("确定下单")]))),_:1})])])),default:w((()=>[y($,{data:il.value,"cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center",backgroundColor:"#ffffff",color:"#606266"}},{default:w((()=>[y(n,{width:"55"},{default:w((e=>{var l;return[m(y(t,{modelValue:dl.value[e.$index],"onUpdate:modelValue":a=>dl.value[e.$index]=a,onChange:a[4]||(a[4]=e=>(e=>{dl.value[e]=!dl.value[e];const a=il.value.every(((e,a)=>0!==e.order_status||dl.value[a]));sl.value=a})(e))},null,8,["modelValue","onUpdate:modelValue"]),[[g,0===(null==(l=e.row)?void 0:l.order_status)]])]})),_:1}),y(n,{prop:"kol_name",label:"达人名称",width:"200"},{default:w((e=>{var a,l,t,r;return[f("div",Ie,[(null==(a=e.row)?void 0:a.kol_photo)?(p(),_("img",{key:0,src:null==(l=e.row)?void 0:l.kol_photo,width:"45",height:"45",alt:""},null,8,Ne)):(p(),_("img",Ue)),f("span",$e,V(null==(t=e.row)?void 0:t.kol_name),1)]),h(" ID:"+V(null==(r=e.row)?void 0:r.platform_uid),1)]})),_:1}),y(n,{prop:"name",label:"服务类型",width:gl.value>150?200:null},{default:w((e=>[y(B,{style:{width:"120px"},disabled:"",modelValue:e.row.cooperation_type,"onUpdate:modelValue":a=>e.row.cooperation_type=a},{default:w((()=>[(p(),_(b,null,k(vl,(e=>y(T,{key:e.value,value:e.value,label:e.label},null,8,["value","label"]))),64))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1},8,["width"]),y(n,{prop:"name",label:"数量",width:gl.value>150?140:null},{default:w((e=>[y(R,{style:{width:"100px"},modelValue:e.row.kol_num,"onUpdate:modelValue":a=>e.row.kol_num=a,disabled:""},null,8,["modelValue","onUpdate:modelValue"])])),_:1},8,["width"]),y(n,{prop:"name",label:"价格",width:"150"},{default:w((e=>{var a;return[f("div",null,"¥"+V(null==(a=e.row)?void 0:a.kol_price),1)]})),_:1}),y(n,{prop:"name",label:"状态",width:"90"},{default:w((e=>{var a;return[f("div",null,V(0===(null==(a=e.row)?void 0:a.order_status)?"未下单":"已下单"),1)]})),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue"]),y(za,{"destroy-on-close":"",title:"任务管理 / "+(0==Ua.value?"新建任务":"编辑任务"),modelValue:Na.value,"onUpdate:modelValue":a[26]||(a[26]=e=>Na.value=e),size:"1200px"},{footer:w((()=>[f("div",ta,[y(l,{onClick:tl},{default:w((()=>a[71]||(a[71]=[h("取消")]))),_:1}),y(l,{type:"primary",onClick:a[24]||(a[24]=e=>(async e=>{e&&await e.validate(((e,a)=>{if(e){let e=JSON.parse(JSON.stringify(Ta.value));""===e.task_type&&(e.task_type=0);let a=Za.value.find((a=>a.id===e.project_id));if(!a)return void I.warning("项目信息获取失败");if(e.project_name=a.project_name,!e.contract_id)return void I.warning("请选择客户合同");if(!e.customer_entity)return void I.warning("客户主体不能为空，请选择所属项目");if(e.customer_contract_id=e.contract_id,e.recharge_contract&&(e.recharge_contract_id=e.recharge_contract),Vl.value&&(e.contract_sign_date=Vl.value.contract_sign_date||e.contract_sign_date||"",e.contract_start_date=Vl.value.contract_start_date||e.contract_start_date||"",e.contract_end_date=Vl.value.contract_end_date||e.contract_end_date||""),!e.performance_owner||!e.performance_department)return void I.warning("业绩归属人和业绩归属部门不能为空，请选择包含这些信息的客户合同");if(!e.order_method)return void I.warning("请选择下单方式");if(2===e.order_method&&!e.account_id)return void I.warning("集采线下下单时必须选择账户");2===e.order_method&&e.account_id&&e.account_brand_name,e.operator=e.performance_owner,e.department=e.performance_department,e.platform_type=Ta.value.platform_type,e.platform_type_entity=Ta.value.platform_type_entity,e.platform_type=Number(e.platform_type),e.recharge_contract&&(e.recharge_contract_id=e.recharge_contract,delete e.recharge_contract),0==Ua.value?(A.service({fullscreen:!0,text:"创建中..."}),M(e).then((e=>{A.service().close(),e&&990===e.code?(I.success("创建成功！"),Na.value=!1,pl()):I.error((null==e?void 0:e.msg)||"创建失败")})).catch((e=>{A.service().close(),I.error("创建失败")}))):(A.service({fullscreen:!0,text:"更新中..."}),E(e).then((e=>{A.service().close(),e&&990===e.code?(I.success("修改成功！"),Na.value=!1,pl()):I.error((null==e?void 0:e.msg)||"修改失败")})).catch((e=>{A.service().close(),I.error("修改失败")})))}else I.warning("请填写完整表单信息")}))})(Y.value))},{default:w((()=>a[72]||(a[72]=[h("确认")]))),_:1}),y(l,{type:"primary",onClick:a[25]||(a[25]=e=>(async e=>{if(!e)return;const a=A.service({lock:!0,text:"提交中...",background:"rgba(0, 0, 0, 0.7)"});try{if(!(await e.validate().catch((e=>!1))))return void a.close();let l=JSON.parse(JSON.stringify(Ta.value));""===l.task_type&&(l.task_type=0);let t=Za.value.find((e=>e.id===l.project_id));if(!t)return I.error("项目信息获取失败"),void a.close();if(l.project_name=t.project_name,!l.contract_id)return I.warning("请选择客户合同"),void a.close();if(!l.customer_entity)return I.warning("客户主体不能为空，请选择所属项目"),void a.close();if(l.customer_contract_id=l.contract_id,l.recharge_contract&&(l.recharge_contract_id=l.recharge_contract),Vl.value&&(l.contract_sign_date=Vl.value.contract_sign_date||l.contract_sign_date||"",l.contract_start_date=Vl.value.contract_start_date||l.contract_start_date||"",l.contract_end_date=Vl.value.contract_end_date||l.contract_end_date||""),!l.performance_owner||!l.performance_department)return I.warning("业绩归属人和业绩归属部门不能为空，请选择包含这些信息的客户合同"),void a.close();if(!l.order_method)return I.warning("请选择下单方式"),void a.close();if(2===l.order_method&&!l.account_id)return I.warning("集采线下下单时必须选择账户"),void a.close();if(2===l.order_method&&l.account_id&&l.account_brand_name,l.operator=l.performance_owner,l.department=l.performance_department,l.platform_type=Ta.value.platform_type,l.platform_type_entity=Ta.value.platform_type_entity,l.platform_type=Number(l.platform_type),l.recharge_contract&&(l.recharge_contract_id=l.recharge_contract,delete l.recharge_contract),0==Ua.value){const e=await M(l);e&&990===e.code?(I.success("创建成功！"),Na.value=!1,e.data&&e.data.data&&e.data.data.id?_a.push({path:"/business/task/informationAuthor",query:{page_type:"add",id:e.data.data.id}}):(I.warning("未能获取任务ID，无法跳转到下一步"),pl())):I.error((null==e?void 0:e.msg)||"创建失败")}else{const e=await E(l);e&&990===e.code?(I.success("修改成功！"),Na.value=!1,l.task_id?_a.push({path:"/business/task/informationAuthor",query:{page_type:"add",id:l.task_id}}):(I.warning("未能获取任务ID，无法跳转到下一步"),pl())):I.error((null==e?void 0:e.msg)||"修改失败")}}catch(l){I.error("提交失败，请稍后重试")}finally{a.close()}})(Y.value))},{default:w((()=>a[73]||(a[73]=[h("确认并下一步")]))),_:1})])])),default:w((()=>[f("div",Pe,[y(ba,{model:Ta.value,rules:Ma,ref_key:"ruleForm",ref:Y,"label-width":"120px",class:"form-style"},{default:w((()=>[f("div",De,[y(ya,{gutter:20},{default:w((()=>[y(X,{span:12},{default:w((()=>[y(W,{label:"所属项目",prop:"project_id",required:""},{default:w((()=>[y(B,{modelValue:Ta.value.project_id,"onUpdate:modelValue":a[8]||(a[8]=e=>Ta.value.project_id=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请选择项目","remote-method":ll,loading:al.value,style:{width:"100%"}},{default:w((()=>[(p(!0),_(b,null,k(Za.value,(e=>(p(),j(T,{key:e.custom_id,label:wt(e),value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"媒体平台",required:""},{default:w((()=>[y(pa,{modelValue:ht.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ht.value=e),placeholder:"根据所属项目自动带出",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),Ta.value.project_id&&[1,2,5].includes(Number(Ta.value.platform_type))?(p(),j(X,{key:0,span:12},{default:w((()=>[y(W,{label:"任务模式",prop:"task_type",required:""},{default:w((()=>[y(B,{modelValue:Ta.value.task_type,"onUpdate:modelValue":a[10]||(a[10]=e=>Ta.value.task_type=e),placeholder:"请选择任务模式",style:{width:"100%"}},{default:w((()=>[(p(!0),_(b,null,k(bt(),(e=>(p(),j(T,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})):x("",!0),y(X,{span:12},{default:w((()=>[y(W,{prop:"order_method",required:""},{label:w((()=>[f("div",Te,[a[55]||(a[55]=f("span",null,"下单方式",-1)),y(d,{content:"'抖音-星立方'仅支持星推下单；'抖音-其他'平台的招募和投稿任务仅支持线下下单；其他平台仅支持集采线下下单",placement:"top",effect:"light"},{default:w((()=>[y(c,{class:"question-icon"},{default:w((()=>[y(S(z))])),_:1})])),_:1})])])),default:w((()=>[y(B,{modelValue:Ta.value.order_method,"onUpdate:modelValue":a[11]||(a[11]=e=>Ta.value.order_method=e),placeholder:"请选择下单方式",style:{width:"100%"}},{default:w((()=>[(p(!0),_(b,null,k(El.value,(e=>(p(),j(T,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"任务名称",prop:"task_name",required:""},{default:w((()=>[y(pa,{modelValue:Ta.value.task_name,"onUpdate:modelValue":a[12]||(a[12]=e=>Ta.value.task_name=e),placeholder:"请输入任务名称",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"客户主体",prop:"customer_entity",required:""},{default:w((()=>[f("div",Le,[y(pa,{modelValue:Ta.value.customer_entity,"onUpdate:modelValue":a[13]||(a[13]=e=>Ta.value.customer_entity=e),placeholder:"根据所属项目自动带出",disabled:"",style:{width:"100%"}},null,8,["modelValue"]),f("div",Me,[y(l,{type:"primary",link:"",size:"small",onClick:Fl},{default:w((()=>a[56]||(a[56]=[h("修改")]))),_:1})])])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"客户合同",prop:"contract_id",required:""},{default:w((()=>[y(B,{modelValue:Ta.value.contract_id,"onUpdate:modelValue":a[14]||(a[14]=e=>Ta.value.contract_id=e),placeholder:"请搜索合同名称或合同编号",filterable:"",remote:"","reserve-keyword":"","remote-method":jl,onChange:Cl,loading:bl.value,style:{width:"100%"}},{default:w((()=>[(p(!0),_(b,null,k(fa.value,(e=>(p(),j(T,{key:e.contract_id,label:e.contract_id+" - "+e.contract_name,value:e.contract_id},{default:w((()=>[f("div",Ee,[f("div",Je,[f("b",null,V(e.contract_id),1),h(" - "+V(e.contract_name),1)]),f("div",Fe,"合同编号: "+V(e.contract_id),1),f("div",Be,[h(" 项目名称: "+V(xl(e)||"无项目名称")+" ",1),a[57]||(a[57]=f("span",{class:"contract-option-note"},"(将用于OA项目)",-1))])])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),Ta.value.contract_id&&Vl.value.value?(p(),_("div",Re,[f("div",Ke,[a[59]||(a[59]=f("span",{class:"contract-label"},"合同名称:",-1)),f("span",null,V(Vl.value.value.contract_name||"--"),1),y(o,{size:"small",type:"success"},{default:w((()=>a[58]||(a[58]=[h("已保存至客户合同字段")]))),_:1})]),f("div",We,[a[60]||(a[60]=f("span",{class:"contract-label"},"合同编号:",-1)),f("span",null,V(Vl.value.value.contract_id||"--"),1)]),f("div",Xe,[a[61]||(a[61]=f("span",{class:"contract-label"},"合同签订日期:",-1)),f("span",null,V(Vl.value.value.contract_sign_date||"--"),1)]),f("div",Ye,[a[63]||(a[63]=f("span",{class:"contract-label"},"合同有效期:",-1)),f("span",null,"从 "+V(Vl.value.value.contract_start_date||"--"),1),f("span",null,[a[62]||(a[62]=h("至 ")),f("strong",Ge,V(Vl.value.value.contract_end_date||"--"),1)])]),Vl.value.value.project_name?(p(),_("div",He,[a[65]||(a[65]=f("span",{class:"contract-label"},"项目名称:",-1)),f("span",null,V(Vl.value.value.project_name),1),y(o,{size:"small",type:"warning"},{default:w((()=>a[64]||(a[64]=[h("自动填充到OA项目")]))),_:1})])):Vl.value.value.oa_project_name?(p(),_("div",Qe,[a[67]||(a[67]=f("span",{class:"contract-label"},"OA项目:",-1)),f("span",null,V(Vl.value.value.oa_project_name),1),y(o,{size:"small",type:"warning"},{default:w((()=>a[66]||(a[66]=[h("自动填充到OA项目")]))),_:1})])):x("",!0)])):x("",!0)])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"所属OA项目",prop:"oa_project_id",required:""},{default:w((()=>[y(pa,{modelValue:Ta.value.oa_project_id,"onUpdate:modelValue":a[15]||(a[15]=e=>Ta.value.oa_project_id=e),placeholder:"选择客户合同后自动填充",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"业绩归属人",prop:"performance_owner",required:""},{default:w((()=>[y(pa,{modelValue:Ta.value.performance_owner,"onUpdate:modelValue":a[16]||(a[16]=e=>Ta.value.performance_owner=e),placeholder:"选择客户合同后自动填充",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"业绩归属部门",prop:"performance_department",required:""},{default:w((()=>[y(pa,{modelValue:Ta.value.performance_department,"onUpdate:modelValue":a[17]||(a[17]=e=>Ta.value.performance_department=e),placeholder:"选择客户合同后自动填充",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"媒体平台主体",prop:"platform_type_entity",required:""},{default:w((()=>[y(pa,{modelValue:Ta.value.platform_type_entity,"onUpdate:modelValue":a[18]||(a[18]=e=>Ta.value.platform_type_entity=e),placeholder:"固定主体信息",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"订单类型",prop:"order_type",required:""},{default:w((()=>[y(B,{modelValue:Ta.value.order_type,"onUpdate:modelValue":a[19]||(a[19]=e=>Ta.value.order_type=e),placeholder:"请选择订单类型",style:{width:"100%"}},{default:w((()=>[(p(!0),_(b,null,k(Sl.value,(e=>(p(),j(T,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"账户选择",prop:"account_id"},{default:w((()=>[f("div",Ze,[Ta.value.account_id?(p(),j(d,{key:0,placement:"top",effect:"light",content:pt(),"popper-style":{maxWidth:"320px",padding:"10px"},"raw-content":""},{default:w((()=>[y(pa,{modelValue:Ta.value.account_name,"onUpdate:modelValue":a[20]||(a[20]=e=>Ta.value.account_name=e),placeholder:"请选择账户",disabled:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1},8,["content"])):(p(),j(pa,{key:1,modelValue:Ta.value.account_name,"onUpdate:modelValue":a[21]||(a[21]=e=>Ta.value.account_name=e),placeholder:"请选择账户",disabled:"",style:{width:"100%"}},null,8,["modelValue"])),f("div",ea,[y(l,{type:"primary",onClick:at,disabled:"2"!==Ta.value.order_method},{default:w((()=>a[68]||(a[68]=[h("选择账户")]))),_:1},8,["disabled"])])]),"2"!==Ta.value.order_method||Ta.value.account_id?x("",!0):(p(),_("div",aa,[y(o,{size:"small",type:"danger"},{default:w((()=>a[69]||(a[69]=[h("集采线下下单时必须选择账户")]))),_:1})]))])),_:1})])),_:1}),y(X,{span:12},{default:w((()=>[y(W,{label:"充值合同",prop:"recharge_contract"},{default:w((()=>[(p(),j(B,{modelValue:Ta.value.recharge_contract,"onUpdate:modelValue":a[22]||(a[22]=e=>Ta.value.recharge_contract=e),placeholder:gt(),filterable:"",remote:"","reserve-keyword":"","remote-method":Dl,loading:Tl.value,onFocus:Vt,onClear:Pl,clearable:"",style:{width:"100%"},disabled:!Ra()||!Ta.value.contract_id,key:`recharge-contract-${Ta.value.contract_id}`,"filter-method":Ol},{default:w((()=>[(p(!0),_(b,null,k($l.value,(e=>(p(),j(T,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","loading","disabled"])),Ra()&&!Ta.value.recharge_contract?(p(),_("div",la,[y(o,{size:"small",type:"warning"},{default:w((()=>a[70]||(a[70]=[h(" 当前媒体平台必须选择充值合同 ")]))),_:1})])):x("",!0)])),_:1})])),_:1}),y(X,{span:24},{default:w((()=>[y(W,{label:"备注",prop:"remark"},{default:w((()=>[y(pa,{modelValue:Ta.value.remark,"onUpdate:modelValue":a[23]||(a[23]=e=>Ta.value.remark=e),type:"textarea",placeholder:"请输入备注信息",rows:4,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])])),_:1},8,["model","rules"])])])),_:1},8,["title","modelValue"]),y(K,{modelValue:$a.value,"onUpdate:modelValue":a[28]||(a[28]=e=>$a.value=e),title:"拒绝原因",width:"500px"},{footer:w((()=>[f("div",ia,[y(l,{type:"primary",onClick:a[27]||(a[27]=e=>$a.value=!1)},{default:w((()=>a[74]||(a[74]=[h("确定")]))),_:1})])])),default:w((()=>[y($,{data:Oa.value},{default:w((()=>[y(n,{property:"kols",label:"达人",width:"240px"},{default:w((e=>{var a,l,t;return[f("div",ra,[f("div",oa,[y(u,{class:"avatar",shape:"circle",size:"small",src:null==(a=e.row)?void 0:a.kols.kol_photo},null,8,["src"])]),f("div",na,[f("p",null,V(null==(l=e.row)?void 0:l.kols.kol_name),1),f("p",ua,"ID:"+V(null==(t=e.row)?void 0:t.kols.platform_uid),1)])])]})),_:1}),y(n,{property:"reviewer_comments",label:"拒绝原因",width:"200"})])),_:1},8,["data"])])),_:1},8,["modelValue"]),y(P,{ref:"drawerRef"},null,512),y(K,{modelValue:Jl.value,"onUpdate:modelValue":a[31]||(a[31]=e=>Jl.value=e),title:"修改客户主体",width:"500px"},{footer:w((()=>[f("span",ca,[y(l,{onClick:a[30]||(a[30]=e=>Jl.value=!1)},{default:w((()=>a[75]||(a[75]=[h("取消")]))),_:1}),y(l,{type:"primary",onClick:Bl},{default:w((()=>a[76]||(a[76]=[h("确认")]))),_:1})])])),default:w((()=>[y(ba,{model:Wl.value,"label-width":"100px"},{default:w((()=>[y(W,{label:"客户主体",required:""},{default:w((()=>[y(B,{modelValue:Wl.value.customer_entity,"onUpdate:modelValue":a[29]||(a[29]=e=>Wl.value.customer_entity=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请搜索客户简称/全称/集团名称","remote-method":it,loading:st.value,style:{width:"100%"},onChange:vt},{default:w((()=>[(p(!0),_(b,null,k(dt.value,(e=>(p(),j(T,{key:e.custom_id,label:e.custom_name,value:e.custom_name},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),y(K,{modelValue:Xl.value,"onUpdate:modelValue":a[39]||(a[39]=e=>Xl.value=e),title:"选择账户",width:"900px"},{footer:w((()=>[f("span",va,[y(l,{onClick:a[38]||(a[38]=e=>Xl.value=!1)},{default:w((()=>a[80]||(a[80]=[h("取消")]))),_:1}),y(l,{type:"primary",onClick:ut},{default:w((()=>a[81]||(a[81]=[h("确认")]))),_:1})])])),default:w((()=>[f("div",da,[y(ba,{inline:!0,model:Zl.value},{default:w((()=>[y(W,{label:"账户ID"},{default:w((()=>[y(pa,{modelValue:Zl.value.customer_account_id,"onUpdate:modelValue":a[32]||(a[32]=e=>Zl.value.customer_account_id=e),placeholder:"请输入账户ID",clearable:"",onKeyup:q(tt,["enter"])},null,8,["modelValue"])])),_:1}),y(W,{label:"账户名称"},{default:w((()=>[y(pa,{modelValue:Zl.value.account_name,"onUpdate:modelValue":a[33]||(a[33]=e=>Zl.value.account_name=e),placeholder:"请输入账户名称",clearable:"",onKeyup:q(tt,["enter"])},null,8,["modelValue"])])),_:1}),y(W,{label:"客户名称"},{default:w((()=>[y(pa,{modelValue:Zl.value.customer_name,"onUpdate:modelValue":a[34]||(a[34]=e=>Zl.value.customer_name=e),placeholder:"请输入客户名称",clearable:"",onKeyup:q(tt,["enter"])},null,8,["modelValue"])])),_:1}),y(W,null,{default:w((()=>[y(l,{type:"primary",onClick:tt},{default:w((()=>a[77]||(a[77]=[h("查询")]))),_:1}),y(l,{onClick:lt},{default:w((()=>a[78]||(a[78]=[h("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),m((p(),j($,{ref_key:"accountTable",ref:Yl,data:Rl.value,style:{width:"100%"},height:"400px","highlight-current-row":"",onCurrentChange:nt},{default:w((()=>[y(n,{width:"50",align:"center"},{default:w((e=>[y(Ia,{modelValue:Ql.value,"onUpdate:modelValue":a[35]||(a[35]=e=>Ql.value=e),label:e.row.id,onChange:()=>{return a=e.row,Gl.value=a,void(Yl.value&&Yl.value.setCurrentRow(a));var a}},{default:w((()=>a[79]||(a[79]=[h(" ")]))),_:2},1032,["modelValue","label","onChange"])])),_:1}),y(n,{prop:"company",label:"所属公司",width:"120"}),y(n,{prop:"media_platform",label:"媒体平台",width:"80"}),y(n,{prop:"customer_name",label:"客户名称",width:"120"}),y(n,{prop:"customer_account_id",label:"客户账户ID","min-width":"160"}),y(n,{prop:"account_name",label:"账户名称","min-width":"160"}),y(n,{prop:"brand_name",label:"品牌名称","min-width":"160"})])),_:1},8,["data"])),[[Ea,Kl.value]]),f("div",sa,[y(D,{"current-page":et.value.currentPage,"onUpdate:currentPage":a[36]||(a[36]=e=>et.value.currentPage=e),"page-size":et.value.pageSize,"onUpdate:pageSize":a[37]||(a[37]=e=>et.value.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:et.value.total,onSizeChange:rt,onCurrentChange:ot},null,8,["current-page","page-size","total"])])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-fac76c5d"]]);export{_a as default};
