import{e,a,d as t,u as l,b as i,c as o,i as s,f as n,h as r,j as p}from"./temp-api-Cg3f9oUp.js";import{d}from"./index-WDAssdXv.js";import{a as c,Q as h,c as m,o as u,g as y,b as _,w as f,n as b,d as g,m as w,h as x,aJ as v,F as L,i as C,t as k,E as K}from"./index-C2bfFjZ1.js";import{_ as V}from"./_plugin-vue_export-helper-BXFjo1rG.js";const F=d(),S=JSON.parse(localStorage.getItem("LOCAL_PERMISSION_KEY"));let j=!1,q=!1;S.forEach((e=>{"system:userMenu:importMCN"==e&&(j=!0),"system:userMenu:edit"==e&&(q=!0)}));F.tabs;const R=[{label:"机构简称",prop:"keyword",search:!0,hide:!0,display:!1},{label:"机构简称",width:160,prop:"mcn_short_name",rules:[{required:!0,message:"请输入机构简称",trigger:"blur"}]},{label:"机构全称",prop:"mcn_name",hide:!0,rules:[{required:!0,message:"请输入机构全称",trigger:"blur"}]},{label:"分级",prop:"grade",width:120},{label:"是否签年框",prop:"is_year_box",width:150},{label:"微信群",hide:!0,prop:"wechat_name"},{label:"微信群主",hide:!0,prop:"wechat_leader",type:"textarea"},{label:"集采对接人",prop:"jicai_contact_person",type:"textarea",width:200},{label:"供应商标签",prop:"mcn_tag",width:160},{label:"对外签约主体",prop:"external_contracting_parties",width:200},{label:"供应商对接人",prop:"mcn_contact_person",type:"textarea",width:200},{label:"公司概况",prop:"company_desc",type:"textarea",width:250},{label:"所在城市",prop:"city",width:150},{label:"机构年度营收流水及利润情况",prop:"profit_situation",type:"textarea",width:300},{label:"组织架构",prop:"org_structure",type:"textarea",width:250},{label:"优势平台",prop:"advantage_platform",width:180},{label:"账号数量平台占比、合作模式占比",prop:"account_coop_percent",hide:!0,width:250},{label:"核心账号类型",prop:"account_type",hide:!0,width:250},{label:"代表账号",prop:"represent_account",hide:!0,width:250},{label:"业务分布和未来规划",prop:"future_plan",hide:!0,width:250},{label:"经营情况的同比和环比",prop:"business_condition",hide:!0,width:250},{label:"核心优势/合作机会点",prop:"cooperate_opportunity",hide:!0,width:250},{label:"年框政策",prop:"year_box_policy",hide:!0,width:250},{label:"历史合作案例及体量、引力排名",prop:"history_cooperate_case",hide:!0,width:250},{prop:"platform_type",label:"起步返点",display:!1,hide:!0,search:!0,type:"select",dicData:[{label:"星图",value:5},{label:"聚星",value:2},{label:"蒲公英",value:106},{label:"花火",value:4}]},{label:"平台返点政策",prop:"rebate_policy",width:250,sortMethod:(e,a)=>{const t=e=>{let a=0;return e.star_policy&&a++,e.dandelion_policy&&a++,e.juxing_policy&&a++,e.fireworks_policy&&a++,e.tencent_policy&&a++,a};return t(e)-t(a)},cellClassName:"combined-policy-cell"},{label:"框架政策",hide:!0,prop:"framework_policy",type:"textarea"},{label:"大客特殊政策",prop:"special_policy",hide:!0,width:250},{label:"促销节日618/双11特殊政策",prop:"festive_policy",hide:!0,width:250},{label:"入库信息",prop:"ext_info",hide:!0,width:250},{label:"返点政策(蒲公英)",width:220,hide:!0,prop:"dandelion_policy",type:"number"},{label:"返点政策(花火)",width:220,hide:!0,prop:"fireworks_policy",type:"number"},{label:"返点政策(聚星)",hide:!0,prop:"juxing_policy",type:"number"},{label:"返点政策(星图)",hide:!0,prop:"star_policy",type:"number"},{label:"返点政策(腾讯互选)",hide:!0,prop:"tencent_policy",type:"number"},{label:"内部对接人",hide:!0,prop:"internal_contact_person",type:"textarea"},{label:"合作客户",hide:!0,prop:"cooperative_clients"},{label:"达人类型",hide:!0,prop:"talent_type"},{label:"合作平台",hide:!0,prop:"cooperative_platform"},{label:"机构属性",hide:!0,prop:"label_attribute"},{label:"备注2",hide:!0,prop:"ext_field2"},{label:"KA_政策",hide:!0,prop:"ka_policy",type:"textarea"},{label:"竞对政策",hide:!0,prop:"competition_policy",type:"textarea"},{label:"机构属性",display:!1,prop:"label_attribute",hide:!0,search:!0,type:"select",dicData:[{label:"MCN",value:"MCN"},{label:"KOC",value:"KOC"},{label:"外采",value:"外采"},{label:"热搜冲榜",value:"热搜冲榜"},{label:"数据服务",value:"数据服务"},{label:"SEO",value:"SEO"},{label:"艺人经纪",value:"艺人经纪"},{label:"个人工作室",value:"个人工作室"},{label:"直播",value:"直播"}],width:130},{label:"机构评级",width:200,hide:!0,search:!0,prop:"ext_field1"},{label:"",prop:"platform_type1",search:!0,display:!1,searchSpan:12,hide:!0},{label:"",prop:"fujian",formslot:!0,hide:!0}],O={class:"card content-box"},E={class:"flex",style:{display:"flex","justify-content":"left","align-items":"center"}},M={key:0},U={class:"flex space-between ml2 pl2 pr2 hoverClass"},D=["href"],z={class:"policy-container"},P={class:"policy-name"},T={style:{"text-align":"right",margin:"0"}},A={class:"request_box_input"},N={class:"flex",style:{"margin-top":"0px"}},$={class:"flex"},B={class:"flex"},I={class:"mediaList"},H={slot:"footer",class:"dialog-footer",style:{display:"flex","justify-content":"right"}},J={class:"mediaList"},W={slot:"footer",class:"dialog-footer",style:{display:"flex","justify-content":"right"}};const Q=V({data:()=>({page:{},form:{policy:{min:"",max:""}},params:{},attributeList:["MCN","KOC","外采","热搜冲榜","数据服务","SEO","艺人经纪","个人工作室","直播"],platformList:{1:"抖音",2:"快手",3:"小红书",4:"B站",5:"快手"},loading:!1,uploadFile:{},fileList:[],fileListKL:[],query:{policy:{min:"",max:""},talent_type:"",label_attribute:"",main_platform:"",ext_field1:"",keyword:"",platform_type:"",page:1,page_size:10,order_by:"",sort:""},type:"",uploadFile1:[],dialogVisible:!1,dialogKLVisible:!1,uploadFileList:"",data:[],activeName:"1",is_mcn_admin_role:!1,option:{},dialogTitle:{},detailData:"新增机构",dialogMCNVisible:!1,componentLoading:!1,uploadFileKL:[],noteTypeObj_Test:{1:"图文",2:"视频"},originalObj:{0:"定制合作",1:"共创合作",2:"招募合作",3:"新芽合作",4:"明星合作"},noteTypeObj:{0:"图文",1:"视频"},policyFields:["star_policy","dandelion_policy","juxing_policy","fireworks_policy","tencent_policy"],tableKey:0}),watch:{dialogKLVisible:function(e,a){e||(this.fileListKL=[])},dialogVisible:function(e,a){e||(this.fileList=[])},is_mcn_admin_role:{handler(e){this.updateTableOptions(),this.tableKey++,this.$nextTick((()=>{this.$refs.crud&&this.$refs.crud.refreshTable()}))}}},methods:{updateTableOptions(){this.option=(e=>{const a=Boolean(e.is_mcn_admin_role),t=["company_desc","profit_situation","org_structure","history_cooperate_project"];return R.forEach((e=>{t.includes(e.prop)&&(e.hide=!a,e.display=a)})),{index:!1,gridBtn:!1,labelWidth:120,addBtn:j,align:"center",searchEnter:!0,headerAlign:"center",border:!0,menu:q,dialogDrag:!0,searchSpan:6,stripe:!0,menuType:"icon",searchMenuPosition:"right",searchIcon:!0,column:R,dialogCustomClass:"dialog-custom-class",dialogWidth:"1000px"}})({is_mcn_admin_role:this.is_mcn_admin_role})},download(e){window.location.href=e},beforeOpen(e,a,t){this.type=a,["view","edit"].includes(a)?(this.policyFields.forEach((e=>{null!==this.form[e]&&this.form[e]})),this.uploadFileList=this.form.annex_url,e()):(this.uploadFileList="",e())},deleteMcnKol(){p({mcn_short_name:this.form.mcn_short_name}).then((e=>{K.success("数据清除成功")}))},sizeChange(e){this.params.page=1,this.params.page_size=e,this.getList()},replaceText(e){try{e=(e=(e=e.replace(/\r\n/g,"<br>")).replace(/\n/g,"<br>")).replace(/\r/g,"<br>")}catch(a){}return e},operationHandler(e){e.mcn_short_name&&e.id&&this.$router.push({path:"/talentSquare/mcnList/detail",query:{mcn_short_name:encodeURIComponent(e.mcn_short_name),mcn_id:e.id}})},sortChange(e){this.query.order_by=e.prop,"descending"==e.order?this.query.sort=1:this.query.sort=2,this.getList()},uploadAnnex(){let e=new FormData;e.append("file",this.uploadFile),this.loading=!0,r(e).then((e=>{this.loading=!1,this.form.annex_url=e.data,K.success("上传成功"),this.uploadFileList=e.data}))},onSuccessLoad(e){let a=e.file;this.uploadFile=a,a&&this.uploadAnnex()},onSuccessLoad1(e){let a=e.raw;this.uploadFile1.push(a)},onSuccessLoadKL(e){let a=e.raw;this.uploadFileKL.push(a)},handleRemove(e){this.uploadFile=this.uploadFile.filter((a=>a.uid!==e.uid))},handleRemoveKL(e){this.uploadFileKL=this.uploadFileKL.filter((a=>a.uid!==e.uid))},async importAgencyApi(){this.uploadFile1.length>0?this.uploadFile1.forEach((async e=>{let a=new FormData;a.append("file",e),this.loading=!0,await n(a).then((e=>{this.loading=!1,this.dialogVisible=!1,K.success("上传成功"),this.getList()}))})):K.error("请选择文件")},handleEnter(){o()},async importPublicationMediaApi(){this.uploadFileKL.length>0?this.uploadFileKL.forEach((async e=>{this.loading=!0;let a=new FormData;a.append("file",e),await s(a).then((e=>{this.loading=!1,this.dialogKLVisible=!1,K.success("上传成功")}))})):K.error("请选择文件")},getList(){this.loading=!0,(this.query.policy.min||this.query.policy.max)&&(this.params.policy={min:this.query.policy.min,max:this.query.policy.max});const e=Object.assign({page:this.page.currentPage,page_size:this.page.pageSize},this.params);o(e).then((e=>{const a=e.data;this.loading=!1,this.page.total=a.count;const t=a.list;this.data=t,void 0!==e.is_mcn_admin_role?this.is_mcn_admin_role=Boolean(e.is_mcn_admin_role):void 0!==a.is_mcn_admin_role?this.is_mcn_admin_role=Boolean(a.is_mcn_admin_role):this.is_mcn_admin_role=!0}))},rowSave(e,a){let t={};for(let l in e)e[l]&&(t[l]=e[l]);i(t).then((e=>{990==e.code&&(a(),this.$message.success("添加成功"),this.getList())}))},rowUpdate(e,a,t,i){const o={...e};this.policyFields.forEach((e=>{})),l(Object.assign(o)).then((()=>{this.$message.success("修改成功"),t(),this.getList()})).catch((()=>{i()}))},rowDel(e){this.$confirm("此操作将永久删除, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>t({id:e.id}))).then((()=>{this.$message.success("删除成功"),this.getList()}))},searchChange(e,a){a&&a(),e.created_at&&e.created_at.length>0&&(e.created_at={end_time:e.created_at[1],start_time:e.created_at[0]}),this.params=e,this.page.currentPage=1,this.getList()},searchChange1(e,a){a&&a(),this.params={},this.query={policy:{min:"",max:""},talent_type:"",label_attribute:"",main_platform:"",ext_field1:"",keyword:"",platform_type:"",page:1,page_size:10,order_by:"",sort:""},this.params=e,this.page.currentPage=1,this.getList()},refreshChange(){this.getList(),this.$message.success("刷新成功")},exportDetail(){this.loading=!0;const e={};this.params.keyword&&(e.keyword=this.params.keyword),this.params.main_platform&&(e.main_platform=this.params.main_platform),this.params.platform_type&&(e.platform_type=this.params.platform_type),this.params.talent_type&&(e.talent_type=this.params.talent_type),(this.query.policy.min||this.query.policy.max)&&(e.policy={min:this.query.policy.min,max:this.query.policy.max}),this.params.label_attribute&&(e.label_attribute=this.params.label_attribute),this.params.ext_field1&&(e.ext_field1=this.params.ext_field1),a(e).then((e=>{try{const a=e.headers["content-disposition"];let t="供应商明细.xlsx";if(a){const e=a.match(/filename=(.*)/);e&&(t=e[1])}const l=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=document.createElement("a");i.href=window.URL.createObjectURL(l),i.download=t,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(i.href),document.body.removeChild(i),this.$message.success("导出成功")}catch(a){throw new Error(a.message||"文件下载失败")}})).catch((e=>{})).finally((()=>{this.loading=!1}))},exportSupplier(){this.loading=!0;const a={};this.params.keyword&&(a.keyword=this.params.keyword),this.params.main_platform&&(a.main_platform=this.params.main_platform),this.params.platform_type&&(a.platform_type=this.params.platform_type),this.params.talent_type&&(a.talent_type=this.params.talent_type),(this.query.policy.min||this.query.policy.max)&&(a.policy={min:this.query.policy.min,max:this.query.policy.max}),this.params.label_attribute&&(a.label_attribute=this.params.label_attribute),this.params.ext_field1&&(a.ext_field1=this.params.ext_field1),e(a).then((e=>{try{const a=e.headers["content-disposition"];let t="供应商更新记录.xlsx";if(a){const e=a.match(/filename=(.*)/);e&&(t=e[1])}const l=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=document.createElement("a");i.href=window.URL.createObjectURL(l),i.download=t,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(i.href),document.body.removeChild(i),this.$message.success("导出成功")}catch(a){throw new Error(a.message||"文件下载失败")}})).catch((e=>{})).finally((()=>{this.loading=!1}))},getPolicyCount(e){let a=0;return e.star_policy&&a++,e.dandelion_policy&&a++,e.juxing_policy&&a++,e.fireworks_policy&&a++,e.tencent_policy&&a++,a},getValidPolicies:e=>[{name:"星图",value:e.star_policy},{name:"蒲公英",value:e.dandelion_policy},{name:"聚星",value:e.juxing_policy},{name:"花火",value:e.fireworks_policy},{name:"腾讯互选",value:e.tencent_policy}].filter((e=>null!==e.value&&void 0!==e.value&&""!==e.value)),getPolicyColorClass(e){const a=parseFloat(e);return a>=50?"policy-value-high":a>=30?"policy-value-medium":"policy-value-low"}},mounted(){this.updateTableOptions(),this.getList()}},[["render",function(e,a,t,l,i,o){const s=c("el-button"),n=c("el-upload"),r=c("el-popover"),p=c("el-input-number"),d=c("el-popconfirm"),K=c("avue-crud"),V=c("el-form-item"),F=c("UploadFilled"),S=c("el-icon"),j=c("el-form"),q=c("el-dialog"),R=h("p"),Q=h("permission");return u(),m("div",O,[(u(),y(K,{ref:"crud",key:i.tableKey,option:i.option,onOnLoad:o.getList,page:i.page,"onUpdate:page":a[4]||(a[4]=e=>i.page=e),"table-loading":i.loading,onRowClick:o.operationHandler,onRowUpdate:o.rowUpdate,"before-open":o.beforeOpen,onRowDel:o.rowDel,onRowSave:o.rowSave,onSizeChange:o.sizeChange,onRefreshChange:o.refreshChange,onSearchReset:o.searchChange1,onSearchChange:o.searchChange,modelValue:i.form,"onUpdate:modelValue":a[5]||(a[5]=e=>i.form=e),data:i.data},{"fujian-form":f((({})=>[x("div",E,[a[13]||(a[13]=x("div",null,"附件：",-1)),_(n,{class:"upload-demo",ref:"uploadRef","show-file-list":!1,"http-request":o.onSuccessLoad,accept:".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.jpg,.jpeg,.png",action:"#"},{default:f((()=>[_(s,{size:"small",type:"primary"},{default:f((()=>a[12]||(a[12]=[g("点击上传")]))),_:1})])),_:1},8,["http-request"]),i.uploadFileList?(u(),m("div",M,[x("div",U,[x("a",{href:i.uploadFileList},"附件下载 ",8,D)])])):b("",!0)])])),rebate_policy:f((e=>[x("div",z,[(u(!0),m(L,null,C(o.getValidPolicies(e.row),((e,a)=>(u(),m("div",{key:a,class:"policy-item"},[x("span",P,k(e.name)+":",1),x("span",null,k(e.value)+"%",1)])))),128))])])),operation:f((t=>[w((u(),y(s,{type:"primary",link:"",icon:e.EditPen,onClick:a=>e.openDrawer("编辑",t.row)},{default:f((()=>a[14]||(a[14]=[g("编辑")]))),_:2},1032,["icon","onClick"])),[[R]]),_(r,{visible:1==t.row.visible,placement:"top"},{reference:f((()=>[_(s,{type:"primary",link:"",icon:e.Delete,onClick:e=>t.row.visible=!0},{default:f((()=>a[17]||(a[17]=[g("删除")]))),_:2},1032,["icon","onClick"])])),default:f((()=>[a[18]||(a[18]=x("p",null,"请确认是否删除该机构",-1)),x("div",T,[_(s,{size:"small",text:"",onClick:e=>t.row.visible=!1},{default:f((()=>a[15]||(a[15]=[g("否")]))),_:2},1032,["onClick"]),_(s,{size:"small",type:"primary",onClick:a=>e.deleteMcn(t)},{default:f((()=>a[16]||(a[16]=[g("是")]))),_:2},1032,["onClick"])])])),_:2},1032,["visible"])])),"platform_type1-search":f((({})=>[x("div",A,[x("div",N,[x("div",$,[_(p,{size:"small",min:0,modelValue:i.query.policy.min,"onUpdate:modelValue":a[0]||(a[0]=e=>i.query.policy.min=e),placeholder:"请输入",clearable:"",onKeyup:v(o.handleEnter,["enter"]),class:"handle-input-ys"},null,8,["modelValue","onKeyup"]),a[19]||(a[19]=x("span",null,"%",-1))]),a[21]||(a[21]=x("span",{class:"ml1 mr1"},"-",-1)),x("div",B,[_(p,{size:"small",min:0,modelValue:i.query.policy.max,"onUpdate:modelValue":a[1]||(a[1]=e=>i.query.policy.max=e),placeholder:"请输入",clearable:"",onKeyup:v(o.handleEnter,["enter"]),class:"handle-input-ys"},null,8,["modelValue","onKeyup"]),a[20]||(a[20]=x("span",null,"%",-1))])])])])),"menu-left":f((()=>[w((u(),y(s,{type:"primary",icon:"upload",onClick:a[2]||(a[2]=e=>i.dialogVisible=!0)},{default:f((()=>a[22]||(a[22]=[g(" 导入机构 ")]))),_:1})),[[Q,"system:userMenu:importMCN"]]),w((u(),y(s,{type:"primary",icon:"upload",onClick:a[3]||(a[3]=e=>i.dialogKLVisible=!0)},{default:f((()=>a[23]||(a[23]=[g("导入刊例")]))),_:1})),[[Q,"system:userMenu:importMCN"]]),w((u(),y(s,{type:"primary",icon:"download",onClick:o.exportDetail},{default:f((()=>a[24]||(a[24]=[g("导出明细")]))),_:1},8,["onClick"])),[[Q,"system:userMenu:exportDetail"]]),w((u(),y(s,{type:"primary",icon:"download",onClick:o.exportSupplier},{default:f((()=>a[25]||(a[25]=[g("导出变更记录")]))),_:1},8,["onClick"])),[[Q,"system:userMenu:exportSupplier"]])])),"menu-form-before":f((({row:e,index:t,type:l})=>["edit"===l?(u(),y(d,{key:0,title:"确定删除机构下的所有达人么?","confirm-button-type":"danger","cancel-button-type":"primary",onConfirm:o.deleteMcnKol},{reference:f((()=>[_(s,{slot:"",type:"danger"},{default:f((()=>a[26]||(a[26]=[g("清空达人")]))),_:1})])),_:1},8,["onConfirm"])):b("",!0)])),_:1},8,["option","onOnLoad","page","table-loading","onRowClick","onRowUpdate","before-open","onRowDel","onRowSave","onSizeChange","onRefreshChange","onSearchReset","onSearchChange","modelValue","data"])),_(q,{title:"导入机构",modelValue:i.dialogVisible,"onUpdate:modelValue":a[8]||(a[8]=e=>i.dialogVisible=e),"append-to-body":!0,width:"40%"},{default:f((()=>[x("div",null,[_(j,{"label-width":"80px"},{default:f((()=>[_(V,{label:"模板下载"},{default:f((()=>[x("div",I,[_(s,{type:"primary",class:"el-button",onClick:a[6]||(a[6]=e=>o.download("https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/kol-mcn-mould-v3.xlsx"))},{default:f((()=>a[27]||(a[27]=[g("下载机构模板 ")]))),_:1})])])),_:1}),_(V,{label:"导入"},{default:f((()=>[_(n,{class:"upload-demo",ref:"uploadRef","auto-upload":!1,limit:1,"file-list":i.fileList,"on-change":o.onSuccessLoad1,"on-remove":o.handleRemove,accept:".xlsx,.xls",drag:"",action:"#"},{default:f((()=>[_(S,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:f((()=>[_(F)])),_:1}),a[28]||(a[28]=x("div",{class:"el-upload__text"},[g("将文件拖到此处，或"),x("em",null,"点击上传")],-1))])),_:1},8,["file-list","on-change","on-remove"])])),_:1})])),_:1})]),x("span",H,[_(s,{onClick:a[7]||(a[7]=e=>i.dialogVisible=!1)},{default:f((()=>a[29]||(a[29]=[g("取 消")]))),_:1}),_(s,{type:"primary",onClick:o.importAgencyApi},{default:f((()=>a[30]||(a[30]=[g("确 定")]))),_:1},8,["onClick"])])])),_:1},8,["modelValue"]),_(q,{title:"导入刊例",modelValue:i.dialogKLVisible,"onUpdate:modelValue":a[11]||(a[11]=e=>i.dialogKLVisible=e),"append-to-body":!0,width:"40%"},{default:f((()=>[x("div",null,[_(j,{"label-width":"80px"},{default:f((()=>[_(V,{label:"模板下载"},{default:f((()=>[x("div",J,[_(s,{type:"primary",class:"el-button",onClick:a[9]||(a[9]=e=>o.download("https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/kol-mcn-Publication-mould.xlsx"))},{default:f((()=>a[31]||(a[31]=[g("下载刊例模板 ")]))),_:1})])])),_:1}),_(V,{label:"导入"},{default:f((()=>[_(n,{class:"upload-demo","auto-upload":!1,"file-list":i.fileListKL,"on-change":o.onSuccessLoadKL,"on-remove":o.handleRemoveKL,accept:".xlsx,.xls",limit:1,drag:"",action:"#"},{default:f((()=>[_(S,{style:{"font-size":"60px",color:"#b8bcc5"}},{default:f((()=>[_(F)])),_:1}),a[32]||(a[32]=x("div",{class:"el-upload__text"},[g("将文件拖到此处，或"),x("em",null,"点击上传")],-1))])),_:1},8,["file-list","on-change","on-remove"])])),_:1})])),_:1})]),x("span",W,[_(s,{onClick:a[10]||(a[10]=e=>i.dialogKLVisible=!1)},{default:f((()=>a[33]||(a[33]=[g("取 消")]))),_:1}),_(s,{type:"primary",onClick:o.importPublicationMediaApi},{default:f((()=>a[34]||(a[34]=[g("确 定")]))),_:1},8,["onClick"])])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-ebdeff63"]]);export{Q as default};
