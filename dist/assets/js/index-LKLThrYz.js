import{s,a as t,Q as a,c as e,o as r,h as o,m as c,g as n,w as p,d,E as l}from"./index-BE6Fh1xm.js";import{_ as u}from"./_plugin-vue_export-helper-GSmkUi5K.js";const i={class:"card content-box"},m=s({name:"throttleDirect"}),_=u(s({...m,setup(s){const u=()=>{l.success("我是节流按钮触发的事件 🍍🍓🍌")};return(s,l)=>{const m=t("el-button"),_=a("throttle");return r(),e("div",i,[l[1]||(l[1]=o("span",{class:"text"},"节流指令 🍇🍇🍇🍓🍓🍓",-1)),c((r(),n(m,{type:"primary"},{default:p((()=>l[0]||(l[0]=[d(" 节流按钮 (每隔1S秒后执行) ")]))),_:1})),[[_,u]])])}}}),[["__scopeId","data-v-233e09d1"]]);export{_ as default};
