import{k as e,a,Q as t,c as l,o as r,h as s,b as o,w as n,m as _,d as i,t as E,g as c}from"./index-C2bfFjZ1.js";import{d}from"./index-WDAssdXv.js";import{_ as R}from"./_plugin-vue_export-helper-BXFjo1rG.js";d().tabs;const p=[{prop:"demand_id",label:"任务ID",width:150,search:!0},{prop:"project_name",label:"项目名称",width:150},{prop:"demand_name",label:"任务名称",width:150,search:!0},{prop:"demand_type",label:"任务类型",type:"select",slot:!0,search:!0,width:150,dicData:[{label:"指派任务",value:1},{label:"招募任务",value:2},{label:"投稿任务",value:3}]},{prop:"universal_settlement_type",label:"结算方式",width:150},{prop:"component_type",label:"组件类型",width:150,slot:!0,search:!0,type:"select",dicData:[{label:"不限",value:"ALL"},{label:"落地页 小程序 应用下载",value:"LINK"},{label:"门店",value:"POI"},{label:"购物车 商品卡",value:"CART"},{label:"游戏",value:"GAME_ANCHOR"},{label:"影视",value:"VARIETY_ANCHOR"},{label:"小程序/小游戏",value:"MICROAPP_ANCHOR"},{label:"品牌百科",value:"BRAND_ANCHOR"},{label:"直播预约组件",value:"LIVE_ORDER_COMPONENT"},{label:"汽车行业",value:"ANCHOR_CAR"},{label:"电商下载",value:"ANCHOR_ECOM"},{label:"剪映",value:"ANCHOR_MOVIE"},{label:"保险行业",value:"ANCHOR_INSURANCE"},{label:"教育行业",value:"ANCHOR_EDUCATION"},{label:"家装行业",value:"ANCHOR_HOME"},{label:"网服下载",value:"ANCHOR_DOWNLOAD"},{label:"企业号小程序",value:"ENTERPRISE_MICRO_APP"},{label:"企业应用下载",value:"ENTERPRISE_DOWNLOAD_APP"},{label:"预约服务",value:"ENTERPRISE_ORDER_SERVICE"},{label:"摄影行业",value:"ENTERPRISE_WEDDING_PHOTO"},{label:"美容美发行业",value:"ENTERPRISE_SALON"},{label:"小说行业",value:"ENTERPRISE_NOVEL"},{label:"团购",value:"ENTERPRISE_COUPON"},{label:"招商行业",value:"ANCHOR_INVESTMENT"},{label:"旅游行业",value:"ANCHOR_TOURISM"},{label:"企业汽车行业",value:"ENTERPRISE_CAR"},{label:"游戏行业锚点",value:"ANCHOR_E_GAME"},{label:"通信行业锚点",value:"ANCHOR_TELECOM"},{label:"房产行业锚点",value:"ANCHOR_ESTATE_SERVICE"},{label:"小程序POI融合锚点",value:"ANCHOR_MICRO_APP_POI"},{label:"企业电商行业锚点",value:"ENTERPRISE_ECOM"},{label:"企业网服行业锚点",value:"ENTERPRISE_DOWNLOAD"},{label:"西瓜行业锚点",value:"ANCHOR_XIGUA"}]},{prop:"customer_name",label:"客户名称",width:150,search:!0},{prop:"star_id",label:"星图账户ID",width:150,search:!0},{prop:"company",label:"客户公司名称",width:150,search:!0},{prop:"account_name",label:"账户名",search:!0},{prop:"spread_play",label:"播放总量"},{prop:"share",label:"分享总量"},{prop:"like",label:"点赞总量"},{prop:"comment",label:"评论量"},{prop:"operation_amount",label:"扣款金额"},{prop:"create_time",label:"创建时间"},{prop:"created_at",label:"创建时间",search:!0,hide:!0,type:"daterange",align:"center",searchRange:!0,format:"YYYY-MM-DD",span:24,valueFormat:"YYYY-MM-DD",startPlaceholder:"日期开始",endPlaceholder:"日期结束"},{prop:"caozuo",label:"操作",slot:!0,width:"200px"}],u={class:"card content-box"},O={style:{height:"90%"}},h={class:"flex cursor text-light",style:{"justify-content":"center",color:"var(--el-color-primary)",cursor:"pointer"}},N=["onClick"],A={style:{display:"flex","justify-content":"center","align-items":"center"}};const C=R({data:()=>({page:{},form:{},params:{},loading:!1,data:[],activeName:"1",total_kol_count:0,option:{index:!1,gridBtn:!1,addBtn:!1,align:"center",searchLabelWidth:100,headerAlign:"center",border:!0,menu:!1,searchEnter:!0,calcHeight:"auto",height:"auto",dialogDrag:!0,searchSpan:6,stripe:!0,menuType:"text",searchMenuPosition:"right",searchIcon:!0,column:p},DEMAND_TYPE:{0:"默认",1:"指派",2:"招募",3:"其他"},UNIVERSAL_SETTLEMENT_TYPE:{ALL:"不限",CPM:"按播放量计费",FIXED_PRICE:"一口价",EXCHANGE:"资源置换",CPA:"按视频等级结算",RANK:"按视频等级结算",EFFECT:"按效果结算",GIFT:"礼品奖励",MONEY_SHARE:"现金奖励",FLOW_SHARE:"流量奖励",STAR_SUPPORT:"应援奖励",DOU_PLUS:"DOU + 奖励",CUSTOMIZE:"自定义结算"},COMPONENT_TYPE:{ALL:"不限",LINK:"落地页 小程序 应用下载",POI:"门店",CART:"购物车 商品卡",GAME_ANCHOR:"游戏",VARIETY_ANCHOR:"影视",MICROAPP_ANCHOR:"小程序/小游戏",BRAND_ANCHOR:"品牌百科",LIVE_ORDER_COMPONENT:"直播预约组件",ANCHOR_CAR:"汽车行业",ANCHOR_ECOM:"电商下载",ANCHOR_MOVIE:"剪映",ANCHOR_INSURANCE:"保险行业",ANCHOR_EDUCATION:"教育行业",ANCHOR_HOME:"家装行业",ANCHOR_DOWNLOAD:"网服下载",ENTERPRISE_MICRO_APP:"企业号小程序",ENTERPRISE_DOWNLOAD_APP:"企业应用下载",ENTERPRISE_ORDER_SERVICE:"预约服务",ENTERPRISE_WEDDING_PHOTO:"摄影行业",ENTERPRISE_SALON:"美容美发行业",ENTERPRISE_NOVEL:"小说行业",ENTERPRISE_COUPON:"团购",ANCHOR_INVESTMENT:"招商行业",ANCHOR_TOURISM:"旅游行业",ENTERPRISE_CAR:"企业汽车行业",ANCHOR_E_GAME:"游戏行业锚点",ANCHOR_TELECOM:"通信行业锚点",ANCHOR_ESTATE_SERVICE:"房产行业锚点",ANCHOR_MICRO_APP_POI:"小程序POI融合锚点",ENTERPRISE_ECOM:"企业电商行业锚点",ENTERPRISE_DOWNLOAD:"企业网服行业锚点",ANCHOR_XIGUA:"西瓜行业锚点"}}),created(){this.getListAll()},methods:{async getListAll(){const a=await e.post("/kolStarTaskTotal",t);var t;this.total_kol_count=a.data.total_kol_count},getList(){this.loading=!0;const a=Object.assign({demand_name:"",demand_id:"",demand_type:"",component_type:"",customer_name:"",account_name:"",star_id:"",company:"",created_at:{start_time:"",end_time:""},page:1,page_size:10},this.params);this.data=[],(a=>e.post("/kolStarTaskList",a))(a).then((e=>{const a=e.data;this.loading=!1,this.page.total=a.count;const t=a.list;this.data=t}))},rowSave(e,a){this.loading=!0,a(e).then((e=>{this.loading=!1,990==e.code&&this.$message.success("导出成功")}))},rowUpdate(a,t,l,r){var s;(s=Object.assign({updateUser:this.userInfo.name},a),e.post("/sys/departmentsUpdate",s)).then((()=>{this.$message.success("修改成功"),l(),this.getList()})).catch((()=>{r()}))},rowDel(a){this.$confirm("此操作将永久删除, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{return t=a.id,e.post("/sys/departmentsUpdate",t);var t})).then((()=>{this.$message.success("删除成功"),this.getList()}))},searchChange(e,a){a&&a(),e.created_at&&e.created_at.length>0&&(e.created_at={end_time:e.created_at[1],start_time:e.created_at[0]}),this.params=e,this.page.currentPage=1,this.getList()},refreshChange(){this.getList(),this.$message.success("刷新成功")},checkVideo(e,a){this.$emit("checkVideo",e,a)},exportTask(){var a;this.form&&this.form.created_at&&(this.form.created_at={start_time:this.form.created_at[0],end_time:this.form.created_at[1]}),(a=this.form,e.post("/starTaskExport",a)).then((e=>{this.$message.success(e.msg)}))}}},[["render",function(e,d,R,p,C,m){const v=a("el-space"),g=a("el-button"),T=a("avue-crud"),b=t("permission");return r(),l("div",u,[s("div",O,[o(T,{ref:"crud",option:C.option,onOnLoad:m.getList,page:C.page,"onUpdate:page":d[1]||(d[1]=e=>C.page=e),"table-loading":C.loading,onRowUpdate:m.rowUpdate,onRowDel:m.rowDel,onRefreshChange:m.refreshChange,onSearchReset:m.searchChange,onSearchChange:m.searchChange,search:C.form,"onUpdate:search":d[2]||(d[2]=e=>C.form=e),data:C.data},{demand_type:n((e=>{var a;return[i(E(C.DEMAND_TYPE[null==(a=e.row)?void 0:a.demand_type]),1)]})),universal_settlement_type:n((e=>{var a;return[i(E(C.UNIVERSAL_SETTLEMENT_TYPE[null==(a=e.row)?void 0:a.universal_settlement_type]),1)]})),component_type:n((e=>{var a;return[i(E(C.COMPONENT_TYPE[null==(a=e.row)?void 0:a.component_type]),1)]})),pic_read3_s_rate:n((e=>{var a;return[s("div",null,E(null==(a=e.row)?void 0:a.pic_read3_s_rate)+"%",1)]})),avg_view_time:n((e=>{var a;return[s("div",null,E(null==(a=e.row)?void 0:a.avg_view_time)+"s",1)]})),engage_rate:n((e=>{var a;return[s("div",null,E(null==(a=e.row)?void 0:a.engage_rate)+"%",1)]})),read_cost:n((e=>{var a;return[s("div",null,E("¥"+(null==(a=e.row)?void 0:a.read_cost)/100),1)]})),engage_cost:n((e=>{var a;return[s("div",null,E("¥"+(null==(a=e.row)?void 0:a.engage_cost)/100),1)]})),caozuo:n((e=>[s("div",h,[s("a",{onClick:a=>m.checkVideo(e.row,"demand_id")},"查看视频",8,N)])])),"menu-left":n((()=>[s("div",A,[o(v,{size:20},{default:n((()=>[s("span",null,[d[3]||(d[3]=s("b",null,"达人数量：",-1)),i(E(C.total_kol_count)+" 个",1)])])),_:1}),_((r(),c(g,{style:{"margin-left":"20px"},type:"primary",icon:"Download",onClick:d[0]||(d[0]=e=>m.exportTask())},{default:n((()=>d[4]||(d[4]=[i("导出 ")]))),_:1})),[[b,"system:talentList:export"]])])])),_:1},8,["option","onOnLoad","page","table-loading","onRowUpdate","onRowDel","onRefreshChange","onSearchReset","onSearchChange","search","data"])])])}],["__scopeId","data-v-b1b5b90b"]]);export{C as default};
