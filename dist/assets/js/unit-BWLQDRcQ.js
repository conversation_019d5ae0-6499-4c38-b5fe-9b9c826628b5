import{a as e,o as t,b as a}from"./temp-api-Beo9ZE5w.js";import{l}from"./lodash-Y6-DaIz7.js";import r from"./AnchorNavigation-CkCK1Jb1.js";import{s as o,r as d,a6 as n,f as s,p as i,E as c,e as g,a as u,Q as p,c as v,o as _,b as m,h as w,w as f,d as y,t as h,F as k,i as b,n as C,y as A,m as V,g as D,z as R,q as T}from"./index-C2bfFjZ1.js";import{_ as S}from"./_plugin-vue_export-helper-BXFjo1rG.js";import"./setupRequest-CaDpMqvE.js";const x={class:"unit-section"},I={id:"notes",class:"content-section",ref:"notesRef"},N={class:"section-content"},U={class:"section-header"},K={class:"note-table"},L={class:"note-description"},z={class:"tag-spu"},P={id:"placement",class:"content-section",ref:"targetingRef"},E={class:"section-content"},B={class:"section-header"},F={class:"target-cards"},M={class:"card-header"},$={class:"card-actions"},J={class:"card-content"},O={class:"target-section"},j={class:"section-right"},q={class:"type-desc"},Q={key:0,class:"target-section",style:{"flex-direction":"column"}},G={class:"section-right target-details"},H={class:"keyword-section"},W={class:"keyword-search"},X={class:"keyword-content"},Y={class:"recommend-keywords"},Z={class:"section-header"},ee={class:"keyword-table"},te={class:"selected-keywords"},ae={class:"section-header"},le={class:"keyword-list"},re={class:"keyword-info"},oe={class:"keyword"},de={key:1,class:"empty-text"},ne={class:"crowd-package-section"},se={class:"crowd-package-content"},ie={class:"crowd-packages"},ce={class:"section-header"},ge={class:"crowd-package-table"},ue={class:"selected-crowd-packages"},pe={class:"section-header"},ve={class:"crowd-package-list"},_e={class:"crowd-package-info"},me={class:"crowd-package-name"},we={class:"crowd-package-id"},fe={key:1,class:"empty-text"},ye={class:"unit-section-header"},he={class:"target-section"},ke={class:"section-right target-details"},be={class:"detail-item"},Ce={class:"detail-item",style:{display:"flex","align-items":"self-start"}},Ae={class:"location-section"},Ve={key:0},De={class:"location-mode"},Re={class:"location-selector"},Te={class:"cascader-wrapper"},Se={key:0},xe={key:1,class:"empty-data"},Ie={class:"selected-locations"},Ne={class:"selected-header"},Ue={class:"selected-content"},Ke={class:"location-row"},Le={key:1,class:"empty-text"},ze={class:"detail-item"},Pe={class:"detail-item"},Ee={id:"targeting",class:"content-section",ref:"biddingRef"},Be={class:"section-content"},Fe={class:"bid-range"},Me={class:"drawer-header"},$e={class:"drawer-tabs"},Je=["onClick"],Oe={class:"search-bar"},je={class:"search-input"},qe={class:"search-actions"},Qe={class:"tab-content"},Ge={class:"search-results"},He={class:"note-description"},We={class:"tag-spu"},Xe={class:"pagination-container"},Ye={class:"dialog-content"},Ze={class:"flex items-center"},et={style:{color:"#999","font-size":"13px","margin-left":"10px"}},tt={class:"dialog-footer"},at=S(o({__name:"unit",props:{unitData:{},savedState:{},campaignData:{}},emits:["update:unit-data"],setup(o,{expose:S,emit:at}){var lt,rt,ot,dt,nt,st,it,ct,gt,ut,pt,vt,_t;const mt=e=>{switch(Number(e)||1){case 1:default:return"不限人群，向小红书域内全部人群进行投放";case 2:return"系统智能寻找相似人群，精准触达广告主目标人群";case 3:return"通过关键词定向触达潜在用户"}},wt=o,ft=at,yt=d(wt.savedState?[...wt.savedState.noteList]:[]),ht=n({note_ids:(null==(lt=wt.unitData)?void 0:lt.note_ids)||[],notes:(null==(rt=wt.savedState)?void 0:rt.noteList)||[],target_cards:(null==(ot=wt.savedState)?void 0:ot.targetCards)||[],bid_form:(null==(dt=wt.savedState)?void 0:dt.bidForm)||{},event_bid:(null==(nt=wt.unitData)?void 0:nt.event_bid)||0,target_config:(null==(st=wt.unitData)?void 0:st.target_config)||{},noteList:(null==(it=wt.savedState)?void 0:it.noteList)||[],targetCards:(null==(ct=wt.savedState)?void 0:ct.targetCards)||[]}),kt=d(!1),bt=d(""),Ct=d([]);d([]);const At=d([]),Vt=d(null),Dt=n({gender:wt.unitData.target_config.target_gender||"all",age:wt.unitData.target_config.target_age||"all",location:"all"===wt.unitData.target_config.target_city?"all":"custom",interests:[],crowds:wt.unitData.target_config.crowd_target.crowd_pkg.map((e=>e.value))}),Rt=[{id:"notes",title:"推广笔记"},{id:"placement",title:"定向组合"},{id:"targeting",title:"出价"}],Tt=n(wt.savedState?{...wt.savedState.bidForm}:{bid:wt.unitData.event_bid/100,smartBid:!1,minBid:.1,maxBid:100}),St=n({currentPage:1,pageSize:10,total:0}),xt=d(!1),It=async()=>{var t,a;xt.value=!0;try{let l=[];if(bt.value.match(/[,\s\n]+/))l=bt.value.split(/[,\s\n]+/).map((e=>e.trim())).filter(Boolean);else if(bt.value){const e=bt.value.trim();e&&(l=[e])}else l="";const r=await e({keyword:l,advertiser_id:null==(t=wt.campaignData)?void 0:t.plan.advertiser_id,page:St.currentPage,page_size:St.pageSize,order_field:"",order_type:"desc",note_content_type:0,placement_type:0,spu_id:"",filter_taobao:0,market_target:0,spu_type:0,note_type:Et[Ft.value],base_only:!1});if(990===r.code&&(null==(a=r.data)?void 0:a.data)){const e=(r.data.data.notes||[]).map((e=>{var t;return{noteId:e.note_id,image:e.image,spu:(null==(t=e.note_multi_spu_info)?void 0:t.length)||0,desc:e.desc,author:e.author,noteType:1===e.note_type?"图文笔记":"视频笔记",createTime:new Date(e.create_time).toLocaleString()}}));Ct.value=e;const t=r.data.data.total;return St.total=t||0,{data:e}}return{data:[]}}catch(l){return c.error("搜索笔记失败"),{data:[]}}finally{xt.value=!1}},Nt=e=>{St.currentPage=e,It()},Ut=e=>{St.pageSize=e,St.currentPage=1,It()},Kt=async()=>{St.currentPage=1;const e=bt.value.split(/[,\s\n]+/).map((e=>e.trim())).filter(Boolean);if(!e.length)return void c.warning("请输入有效的笔记ID");await It();const t=Ct.value.filter((t=>e.includes(t.noteId)));0!==t.length?(Ct.value.forEach((e=>{var t;null==(t=Vt.value)||t.toggleRowSelection(e,!1)})),T((()=>{t.forEach((e=>{var t;null==(t=Vt.value)||t.toggleRowSelection(e,!0)})),At.value=[...t],c.success(`已选中 ${t.length} 条笔记，点击确定按钮完成添加`)}))):c.warning("未找到匹配的笔记")};s([Dt,Tt],(()=>{ht.event_bid=100*Tt.bid,ht.target_config={...wt.unitData.target_config,target_gender:Dt.gender,target_age:Dt.age,target_city:"all"===Dt.location?"all":Dt.location,crowd_target:{...wt.unitData.target_config.crowd_target,crowd_pkg:Dt.crowds.map((e=>{var t;return{value:e,name:(null==(t=wt.unitData.target_config.crowd_target.crowd_pkg.find((t=>t.value===e)))?void 0:t.name)||""}}))}}}),{deep:!0});const Lt=e=>{At.value=e};i((async()=>{var t;if(ha()){(null==(t=wt.unitData.note_ids)?void 0:t.length)&&await async function(t){var a,l,r,o,d,n,s,i,g,u,p,v;try{const r=await e({note_ids:t,advertiser_id:null==(a=wt.campaignData)?void 0:a.plan.advertiser_id,order_field:"",order_type:"desc",note_content_type:0,placement_type:0,spu_id:"",filter_taobao:0,market_target:0,spu_type:0,page:1,page_size:t.length,note_type:Et[Ft.value]});if(990===r.code&&(null==(l=r.data)?void 0:l.data)){(r.data.data.notes||[]).map((e=>({noteId:e.note_id,image:e.cover_url,desc:e.content,author:e.author_name,noteType:Et[e.note_type],spu:e.spu_id,createTime:e.create_time})))}}catch(w){c.error("获取笔记详情失败")}ht.note_ids=yt.value.map((e=>e.noteId)),ht.notes=[...yt.value],0===Yt.value.length&&((null==(o=null==(r=wt.unitData)?void 0:r.targetCards)?void 0:o.length)>0?Yt.value=JSON.parse(JSON.stringify(wt.unitData.targetCards)).map((e=>({...e,targetType:Number(e.targetType)||1}))):Yt.value=[fa()]);const _=JSON.parse(JSON.stringify(Yt.value)).map((e=>({...e,targetType:Number(e.targetType)||1})));ht.note_ids=yt.value.map((e=>e.noteId)),ht.event_bid=100*Tt.bid,ht.noteList=yt.value,ht.target_cards=_,ht.targetCards=_,ht.bid_form={...Tt},Yt.value.length>0&&(ht.target_config={...wt.unitData.target_config,target_gender:(null==(d=Yt.value[0])?void 0:d.gender[0])||"all",target_age:(null==(n=Yt.value[0])?void 0:n.ageRanges[0])||"all",target_city:"all"===(null==(s=Yt.value[0])?void 0:s.location[0])?"all":{mode:null==(i=Yt.value[0])?void 0:i.locationMode,countries:null==(g=Yt.value[0])?void 0:g.selectedLocations,cities:null==(u=Yt.value[0])?void 0:u.selectedCities,filter:null==(p=Yt.value[0])?void 0:p.locationFilter},target_device:(null==(v=Yt.value[0])?void 0:v.platform[0])||"all",crowd_target:{...wt.unitData.target_config.crowd_target,crowd_pkg:((null==(m=Yt.value[0])?void 0:m.crowds)||[]).map((e=>{const t=Gt.value.find((t=>t.value===e));return{value:e,name:(null==t?void 0:t.name)||""}}))||[]}});var m}(wt.unitData.note_ids);try{if(await Xt(),Yt.value.forEach((e=>{e.targetType=Number(e.targetType)||1;const t=Ot.value.filter((e=>"all"!==e.code)).map((e=>e.code)),a=jt.value.filter((e=>"all"!==e.code)).map((e=>e.code)),l=qt.value.filter((e=>"all"!==e.code)).map((e=>e.code));e.gender.includes("all")&&(e.gender=["all",...t]),e.ageRanges.includes("all")&&(e.ageRanges=["all",...a]),e.platform.includes("all")&&(e.platform=["all",...l])})),0===Yt.value.length){const e=Ot.value.filter((e=>"all"!==e.code)).map((e=>e.code)),t=jt.value.filter((e=>"all"!==e.code)).map((e=>e.code)),a=qt.value.filter((e=>"all"!==e.code)).map((e=>e.code));Yt.value.push({targetPackage:"",targetType:1,gender:["all",...e],location:["all"],ageRanges:["all",...t],platform:["all",...a],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[]})}}catch(a){c.error("获取定向数据失败")}await It()}else c.error("数据不完整，请检查")}));const zt=()=>{if(!At.value.length)return void c.warning("请选择要添加的笔记");const e=At.value.map((e=>({noteId:e.noteId,image:e.image,desc:e.desc,author:e.author,noteType:e.noteType,spu:e.spu,createTime:e.createTime}))),t=new Set(yt.value.map((e=>e.noteId))),a=e.filter((e=>!t.has(e.noteId)));0!==a.length?(yt.value=[...yt.value,...a],ht.note_ids=yt.value.map((e=>e.noteId)),ht.notes=[...yt.value],c.success(`成功添加 ${a.length} 条笔记`),kt.value=!1,bt.value="",Ct.value=[],At.value=[]):c.warning("所选笔记已全部添加")},Pt=async()=>{kt.value=!0,At.value=[...yt.value];try{await It(),T((()=>{const e=new Set(yt.value.map((e=>e.noteId)));Ct.value.forEach((t=>{var a;e.has(t.noteId)&&(null==(a=Vt.value)||a.toggleRowSelection(t,!0))}))}))}catch(e){c.error("加载笔记列表失败")}},Et={my:1,cooperation:2,protagonist:4,employee:6,authorized:11};const Bt=e=>{const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})},Ft=d("cooperation"),Mt=[{key:"cooperation",name:"合作笔记"},{key:"my",name:"我的笔记"},{key:"authorized",name:"授权笔记"},{key:"employee",name:"员工笔记"},{key:"protagonist",name:"主理人笔记"}],$t=d("content_interests");d(!1),d(!1);const Jt=d(!1),Ot=d([]),jt=d([]),qt=d([]),Qt=d([]),Gt=d([]),Ht=d({content_interests:[],shopping_interests:[]}),Wt=g((()=>Ht.value[$t.value].map((e=>e))));d([]);const Xt=async()=>{var e,a,l,r,o,d,n,s,i;Jt.value=!0;try{if(!(null==(a=null==(e=wt.campaignData)?void 0:e.plan)?void 0:a.advertiser_id))throw new Error("广告主ID不能为空");const c=await t({advertiser_id:wt.campaignData.plan.advertiser_id});if(!c)throw new Error("获取定向数据接口返回为空");if(990!==c.code)throw new Error(c.message||"获取定向数据接口返回错误");if(!(null==(l=c.data)?void 0:l.data))throw new Error("获取定向数据接口返回数据格式错误");const g=c.data.data;return Ot.value=g.gender_targets||[],jt.value=g.age_targets||[],qt.value=g.device_targets||[],Qt.value=(e=>e.filter((e=>"-1"!==e.code)).map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.filter((e=>"-1"!==e.code)).map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.filter((e=>"-1"!==e.code)).map((e=>({code:e.code,name:e.name})))}}))}})))(g.area_targets||[]),Gt.value=((null==(r=g.crowd_target)?void 0:r.crowd_pkg)||[]).map((e=>({group_id:e.group_id||"",name:e.name||"",status:e.status||0,sync_status:e.sync_status||0,value:e.value||""}))),Ht.value=g.industry_interest_target||{content_interests:[],shopping_interests:[]},Ot.value.length>0&&!Ot.value.some((e=>"all"===e.code))&&Ot.value.unshift({code:"all",name:"全部"}),jt.value.length>0&&!jt.value.some((e=>"all"===e.code))&&jt.value.unshift({code:"all",name:"全部"}),qt.value.length>0&&!qt.value.some((e=>"all"===e.code))&&qt.value.unshift({code:"all",name:"全部"}),(null==(d=null==(o=wt.savedState)?void 0:o.targetCards)?void 0:d.length)>0?Yt.value=wt.savedState.targetCards.map((e=>{var t,a,l,r,o,d,n,s,i,c,g;return!e.crowd_target&&(null==(t=e.target_config)?void 0:t.crowd_target)&&(e.crowd_target=e.target_config.crowd_target),{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(a=e.target_config)?void 0:a.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(l=e.target_config)?void 0:l.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:[e.target_age||(null==(r=e.target_config)?void 0:r.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(o=e.target_config)?void 0:o.target_device)||"all"],locationMode:e.locationMode||"country",selectedLocations:e.selectedLocations||[],selectedCities:e.selectedCities||{},locationFilter:e.locationFilter||"",crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(d=e.crowd_target)?void 0:d.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(s=null==(n=e.target_config)?void 0:n.crowd_target)?void 0:s.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(i=e.crowd_target)?void 0:i.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(g=null==(c=e.target_config)?void 0:c.crowd_target)?void 0:g.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):(null==(i=null==(s=null==(n=wt.campaignData)?void 0:n.unit)?void 0:s.targetCards)?void 0:i.length)>0?Yt.value=wt.campaignData.unit.targetCards.map((e=>{var t,a,l,r,o,d,n,s,i,c,g;return!e.crowd_target&&(null==(t=e.target_config)?void 0:t.crowd_target)&&(e.crowd_target=e.target_config.crowd_target),{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(a=e.target_config)?void 0:a.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(l=e.target_config)?void 0:l.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:[e.target_age||(null==(r=e.target_config)?void 0:r.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(o=e.target_config)?void 0:o.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(d=e.crowd_target)?void 0:d.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(s=null==(n=e.target_config)?void 0:n.crowd_target)?void 0:s.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(i=e.crowd_target)?void 0:i.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(g=null==(c=e.target_config)?void 0:c.crowd_target)?void 0:g.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):0===Yt.value.length&&(Yt.value=[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}]),!0}catch(g){return c.error(g.message||"获取定向数据失败，请检查网络连接或刷新页面重试"),!1}finally{Jt.value=!1}},Yt=d((null==(ut=null==(gt=wt.savedState)?void 0:gt.targetCards)?void 0:ut.length)>0?JSON.parse(JSON.stringify(wt.savedState.targetCards)).map((e=>{var t,a,l,r,o,d,n,s,i,c;return{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(t=e.target_config)?void 0:t.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(a=e.target_config)?void 0:a.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:[e.target_age||(null==(l=e.target_config)?void 0:l.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(r=e.target_config)?void 0:r.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(o=e.crowd_target)?void 0:o.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(n=null==(d=e.target_config)?void 0:d.crowd_target)?void 0:n.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(s=e.crowd_target)?void 0:s.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(c=null==(i=e.target_config)?void 0:i.crowd_target)?void 0:c.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):(null==(_t=null==(vt=null==(pt=wt.campaignData)?void 0:pt.unit)?void 0:vt.targetCards)?void 0:_t.length)>0?JSON.parse(JSON.stringify(wt.campaignData.unit.targetCards)).map((e=>{var t,a,l,r,o,d,n,s,i,c;return{...e,targetType:Number(e.targetType)||1,gender:Array.isArray(e.gender)?e.gender:[e.target_gender||(null==(t=e.target_config)?void 0:t.target_gender)||"all"],location:Array.isArray(e.location)?e.location:[e.target_city||(null==(a=e.target_config)?void 0:a.target_city)||"all"],ageRanges:Array.isArray(e.ageRanges)?e.ageRanges:[e.target_age||(null==(l=e.target_config)?void 0:l.target_age)||"all"],platform:Array.isArray(e.platform)?e.platform:[e.target_device||(null==(r=e.target_config)?void 0:r.target_device)||"all"],crowds:Array.isArray(e.crowds)?e.crowds:Array.isArray(null==(o=e.crowd_target)?void 0:o.crowd_pkg)?e.crowd_target.crowd_pkg.map((e=>e.value||e.code)):Array.isArray(null==(n=null==(d=e.target_config)?void 0:d.crowd_target)?void 0:n.crowd_pkg)?e.target_config.crowd_target.crowd_pkg.map((e=>e.value||e.code)):[],selectedDistricts:e.selectedDistricts||{},selectedNodes:e.selectedNodes||[],selectedCrowds:e.selectedCrowds||[],selectedKeywords:e.selectedKeywords||[],crowd_target:{crowd_pkg:Array.isArray(null==(s=e.crowd_target)?void 0:s.crowd_pkg)?e.crowd_target.crowd_pkg:Array.isArray(null==(c=null==(i=e.target_config)?void 0:i.crowd_target)?void 0:c.crowd_pkg)?e.target_config.crowd_target.crowd_pkg:[]}}})):[{targetPackage:"",targetType:1,gender:["all"],location:["all"],ageRanges:["all"],platform:["all"],locationMode:"country",selectedLocations:[],selectedCities:{},locationFilter:"",crowds:[],selectedDistricts:{},selectedNodes:[],selectedCrowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}]);s(Yt,(e=>{e.forEach((e=>{0===e.gender.length&&(e.gender=["all"]),0===e.location.length&&(e.location=["all"]),0===e.ageRanges.length&&(e.ageRanges=["all"]),0===e.platform.length&&(e.platform=["all"]),void 0===e.targetType||null===e.targetType?e.targetType=1:e.targetType=Number(e.targetType)||1}))}),{deep:!0});d(null);const Zt=()=>{const e={targetType:1,targetPackage:"",gender:["all",...Ot.value.filter((e=>"all"!==e.code)).map((e=>e.code))],ageRanges:["all",...jt.value.filter((e=>"all"!==e.code)).map((e=>e.code))],platform:["all",...qt.value.filter((e=>"all"!==e.code)).map((e=>e.code))],location:["all"],locationMode:"country",locationFilter:"",selectedNodes:[],selectedCrowds:[],selectedKeywords:[],selectedLocations:[],selectedCities:{},selectedDistricts:{},selectedContentInterests:[],selectedShoppingInterests:[],industry_interest_target:[]};Yt.value.push(e)};S({scrollToSection:Bt,validate:async()=>{var e,t;if(Yt.value.forEach((e=>wa(e))),0===yt.value.length)return c.error("请至少添加一条笔记"),Bt("notes"),!1;if(0===Yt.value.length)return c.error("请至少添加一个定向组合"),Bt("targeting"),!1;for(let l=0;l<Yt.value.length;l++){const a=Yt.value[l];void 0!==a.targetType&&null!==a.targetType&&0!==a.targetType||(a.targetType=1);const r=(null==(t=null==(e=a.crowd_target)?void 0:e.crowd_pkg)?void 0:t.length)>0,o=[...a.selectedKeywords||[],...a.selectedContentInterests||[],...a.selectedShoppingInterests||[],...r?a.crowd_target.crowd_pkg:[]];if(3===a.targetType&&(0===o.length||o.length>150))return c.error(`定向组合${l+1}：高级定向不能为空且不能超过150`),!1;if(!a.gender||0===a.gender.length)return c.error(`定向组合${l+1}：请选择性别`),!1;if(!a.location||0===a.location.length)return c.error(`定向组合${l+1}：请选择地域`),!1;if(a.location.includes("custom")&&!a.location.includes("all")&&(!a.selectedNodes||0===a.selectedNodes.length))return c.error(`定向组合${l+1}：请选择具体地域`),!1;if(!a.ageRanges||0===a.ageRanges.length)return c.error(`定向组合${l+1}：请选择年龄范围`),!1;if(!a.platform||0===a.platform.length)return c.error(`定向组合${l+1}：请选择平台`),!1}if(!Tt.bid)return c.error("请设置目标成本"),Bt("bidding"),!1;const a=Yt.value.map((e=>{const t={...e,targetType:Number(e.targetType)||1};return t.gender&&t.gender.includes("all")&&(t.gender=["all"]),t.ageRanges&&t.ageRanges.includes("all")&&(t.ageRanges=["all"]),t.platform&&t.platform.includes("all")&&(t.platform=["all"]),!t.crowd_target||t.crowd_target.crowd_pkg&&0!==t.crowd_target.crowd_pkg.length||delete t.crowd_target,t}));return ht.note_ids=yt.value.map((e=>e.noteId)),ht.notes=[...yt.value],ht.target_cards=a,ht.bid_form=Tt,ft("update:unit-data",{note_ids:yt.value.map((e=>e.noteId)),notes:yt.value,target_cards:a,bid_form:Tt}),!0}}),s([yt,Yt,Tt],(()=>{var e,t,a,l,r,o,d,n;const s=Yt.value.map((e=>{const t=Number(e.targetType),a={...e,targetType:t||1};return a.gender&&a.gender.includes("all")&&(a.gender=["all"]),a.ageRanges&&a.ageRanges.includes("all")&&(a.ageRanges=["all"]),a.platform&&a.platform.includes("all")&&(a.platform=["all"]),a}));if(ht.note_ids=yt.value.map((e=>e.noteId)),ht.notes=[...yt.value],ht.event_bid=100*Tt.bid,ht.noteList=yt.value,ht.target_cards=s,ht.targetCards=s,ht.bid_form={...Tt},Yt.value.length>0){const s=(()=>{var e,t;if(!(null==(t=null==(e=Yt.value[0])?void 0:e.crowds)?void 0:t.length))return[];return Yt.value[0].crowds.map((e=>{const t=Gt.value.find((t=>t.value===e));return{value:e,name:(null==t?void 0:t.name)||""}}))})();ht.target_config={...wt.unitData.target_config,target_gender:(null==(e=Yt.value[0])?void 0:e.gender[0])||"all",target_age:(null==(t=Yt.value[0])?void 0:t.ageRanges[0])||"all",target_city:"all"===(null==(a=Yt.value[0])?void 0:a.location[0])?"all":{mode:null==(l=Yt.value[0])?void 0:l.locationMode,countries:null==(r=Yt.value[0])?void 0:r.selectedLocations,cities:null==(o=Yt.value[0])?void 0:o.selectedCities,filter:null==(d=Yt.value[0])?void 0:d.locationFilter},target_device:(null==(n=Yt.value[0])?void 0:n.platform[0])||"all",crowd_target:{...wt.unitData.target_config.crowd_target,crowd_pkg:s}}}}),{deep:!0}),s(Ft,(async()=>{St.currentPage=1,Ct.value=[],At.value=[],await It()})),s((()=>Yt.value.map((e=>e.location))),(e=>{Yt.value.forEach(((t,a)=>{const l=e[a];if(l.includes("all")&&l.includes("custom")){const e=l[l.length-1];t.location="all"===e?["all"]:["custom"]}}))}),{deep:!0});const ea=e=>e.selectedNodes&&e.selectedNodes.length>0,ta={multiple:!0,checkStrictly:!1,value:"code",label:"name",children:"children",expandTrigger:"click",emitPath:!1},aa=g((()=>Qt.value.map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.map((e=>{var t;return{code:e.code,name:e.name,children:null==(t=e.children)?void 0:t.map((e=>({code:e.code,name:e.name})))}}))}})))),la=e=>{e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={},(e.selectedNodes||[]).forEach((t=>{const a=e=>{for(const l of e){if(l.code===t)return l;if(l.children){const e=a(l.children);if(e)return e}}return null},l=a(aa.value);if(!l)return;const r=((e,t)=>{for(const a of e)if(a.children)for(const e of a.children){if(e.code===t)return[a,e];if(e.children)for(const l of e.children)if(l.code===t)return[a,e,l]}return[]})(aa.value,l.code);if(0===r.length)e.selectedLocations.some((e=>e.code===l.code))||e.selectedLocations.push(l);else if(2===r.length){const[t,a]=r;e.selectedLocations.some((e=>e.code===t.code))||e.selectedLocations.push(t),e.selectedCities[t.code]||(e.selectedCities[t.code]=[]),e.selectedCities[t.code].some((e=>e.code===a.code))||e.selectedCities[t.code].push(a)}else if(3===r.length){const[t,a,l]=r;e.selectedLocations.some((e=>e.code===t.code))||e.selectedLocations.push(t),e.selectedCities[t.code]||(e.selectedCities[t.code]=[]),e.selectedCities[t.code].some((e=>e.code===a.code))||e.selectedCities[t.code].push(a),e.selectedDistricts[a.code]||(e.selectedDistricts[a.code]=[]),e.selectedDistricts[a.code].some((e=>e.code===l.code))||e.selectedDistricts[a.code].push(l)}}))},ra=e=>{const t=[];return(e.selectedNodes||[]).forEach((e=>{const a=t=>{for(const l of t){if(l.code===e)return l;if(l.children){const e=a(l.children);if(e)return e}}return null},l=a(aa.value);l&&t.push({code:l.code,name:l.name})})),t},oa=(e,t)=>{e.selectedNodes=e.selectedNodes.filter((e=>!((e,t)=>{const a=Array.isArray(t)?t[t.length-1]:t,l=(e,t=[])=>{for(const r of e){if(r.code===a)return[...t,r];if(r.children){const e=l(r.children,[...t,r]);if(e.length>0)return e}}return[]};return l(e)})(aa.value,e).some((e=>e.code===t.code)))),la(e)},da=d(!1),na=d(""),sa=d([]),ia=d(null),ca=()=>{ia.value&&(ia.value.targetPackage=na.value),da.value=!1},ga=()=>{na.value="",da.value=!1},ua=d([]),pa=d(!1),va=async()=>{var e;pa.value=!0;try{const t=await a({advertiser_id:944495,keyword:ka.value,note_ids:yt.value.map((e=>e.id))});990===t.code&&(null==(e=t.data)?void 0:e.data)&&(ua.value=t.data.data)}catch(t){c.error("获取推荐关键词失败")}finally{pa.value=!1}},_a=l.debounce((()=>{va()}),300),ma=(e,t)=>{var a;return(null==(a=e.selectedKeywords)?void 0:a.some((e=>e.id===t.target_word)))??!1};i((()=>{va()})),s((()=>Ft.value),(e=>{"keywords"===e&&va()}));const wa=e=>{e.targetType&&0!==e.targetType?e.targetType=Number(e.targetType):e.targetType=1},fa=()=>({targetType:1,gender:["all"],ageRanges:["all"],location:["all"],platform:["all"],crowds:[],selectedKeywords:[],crowd_target:{crowd_pkg:[]}}),ya=()=>{ft("update:unit-data",{note_ids:yt.value.map((e=>e.noteId)),notes:yt.value,target_cards:Yt.value,bid_form:Tt,...ht})},ha=()=>{var e;return!!(null==(e=wt.campaignData)?void 0:e.plan)};s((()=>wt.campaignData),(e=>{}),{deep:!0});s($t,(()=>{Yt.value.forEach((e=>{(e=>{e.industry_interest_target=Ht.value,e.selectedContentInterests||(e.selectedContentInterests=[]),e.selectedShoppingInterests||(e.selectedShoppingInterests=[])})(e)}))}));const ka=d(""),ba=(e,t)=>{var a,l;if(!(null==(l=null==(a=e.crowd_target)?void 0:a.crowd_pkg)?void 0:l.length))return!1;return e.crowd_target.crowd_pkg.some((e=>e.value===t.value))},Ca=()=>{bt.value=""};return(e,t)=>{const a=u("el-button"),l=u("el-table-column"),o=u("el-image"),d=u("el-table"),n=u("el-radio"),s=u("el-radio-group"),i=u("el-radio-button"),g=u("el-cascader"),T=u("el-cascader-panel"),S=u("el-tab-pane"),at=u("el-input"),lt=u("el-tag"),rt=u("el-tabs"),ot=u("el-switch"),dt=u("el-checkbox"),nt=u("el-checkbox-group"),st=u("el-skeleton"),it=u("el-input-number"),ct=u("el-form-item"),gt=u("el-form"),ut=u("el-pagination"),pt=u("el-drawer"),vt=u("el-option"),_t=u("el-select"),wt=u("el-dialog"),ft=p("loading");return _(),v("div",x,[m(r,{anchors:Rt}),w("div",I,[t[14]||(t[14]=w("div",{class:"section-title"},"推广笔记",-1)),w("div",N,[w("div",U,[m(a,{type:"primary",onClick:Pt},{default:f((()=>t[11]||(t[11]=[y("添加笔记")]))),_:1})]),t[13]||(t[13]=w("p",{class:"section-desc"},"单元层级添加的笔记/商品将自动为您生成多个创意，并用于广告投放。在新建流程中，若想新增或删除笔记/商品，请在单元层级修改。",-1)),w("div",K,[m(d,{data:yt.value,border:"",style:{width:"100%"}},{default:f((()=>[m(l,{prop:"noteId",label:"笔记ID",width:"180"}),m(l,{label:"图片",width:"100"},{default:f((e=>[m(o,{src:e.row.image,preview:!1,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"])])),_:1}),m(l,{prop:"desc",label:"描述","show-overflow-tooltip":"","min-width":"300"},{default:f((e=>[w("div",L,h(e.row.desc),1)])),_:1}),m(l,{prop:"desc",label:"SPU","show-overflow-tooltip":"","min-width":"120"},{default:f((e=>[w("span",z,"累计绑定"+h(e.row.spu)+"个SPU",1)])),_:1}),m(l,{prop:"author",label:"作者",width:"120"}),m(l,{prop:"noteType",label:"笔记形式",width:"100"}),m(l,{prop:"createTime",label:"创建时间",width:"180"}),m(l,{label:"操作",width:"100",fixed:"right"},{default:f((e=>[m(a,{type:"danger",link:"",onClick:t=>{return a=e.$index,yt.value[a],yt.value.splice(a,1),ht.note_ids=yt.value.map((e=>e.noteId)),ht.notes=[...yt.value],void ya();var a}},{default:f((()=>t[12]||(t[12]=[y(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])])],512),w("div",P,[t[50]||(t[50]=w("div",{class:"section-title"},"定向组合",-1)),w("div",E,[w("div",B,[m(a,{type:"primary",onClick:Zt},{default:f((()=>t[15]||(t[15]=[y("添加定向")]))),_:1})]),w("div",F,[(_(!0),v(k,null,b(Yt.value,((e,r)=>(_(),v("div",{key:r,class:"target-card"},[w("div",M,[w("span",null,"定向组合"+h(r+1),1),w("div",$,[m(a,{type:"danger",link:"",onClick:e=>(e=>{Yt.value.length<=1?c.warning("至少保留一个定向组合，不允许删除"):Yt.value.splice(e,1)})(r)},{default:f((()=>t[16]||(t[16]=[y("删除")]))),_:2},1032,["onClick"])])]),w("div",J,[w("div",O,[t[20]||(t[20]=w("div",{class:"section-left"},[w("div",{class:"section-title"},"定向类型")],-1)),w("div",j,[m(s,{"model-value":Number(e.targetType)||1,"onUpdate:modelValue":t=>e.targetType=t},{default:f((()=>[m(n,{label:1},{default:f((()=>t[17]||(t[17]=[y("通投")]))),_:1}),m(n,{label:2},{default:f((()=>t[18]||(t[18]=[y("智能定向")]))),_:1}),m(n,{label:3},{default:f((()=>t[19]||(t[19]=[y("高级定向")]))),_:1})])),_:2},1032,["model-value","onUpdate:modelValue"]),w("div",q,h(mt(e.targetType||1)),1)])]),3===Number(e.targetType)?(_(),v("div",Q,[t[35]||(t[35]=w("div",{class:"section-left"},[w("div",{class:"section-title"},"高级定向")],-1)),w("div",G,[m(rt,null,{default:f((()=>[m(S,{label:"行业兴趣"},{default:f((()=>[w("div",null,[m(s,{modelValue:$t.value,"onUpdate:modelValue":t[0]||(t[0]=e=>$t.value=e),size:"small",class:"switch-group"},{default:f((()=>[m(i,{label:"content_interests"},{default:f((()=>t[21]||(t[21]=[y("行业阅读兴趣")]))),_:1}),m(i,{label:"shopping_interests"},{default:f((()=>t[22]||(t[22]=[y("行业购物兴趣")]))),_:1})])),_:1},8,["modelValue"]),"content_interests"===$t.value?(_(),v(k,{key:0},[m(g,{modelValue:e.selectedContentInterests,"onUpdate:modelValue":t=>e.selectedContentInterests=t,options:Wt.value,props:ta,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"]),m(T,{modelValue:e.selectedContentInterests,"onUpdate:modelValue":t=>e.selectedContentInterests=t,options:Wt.value,props:ta,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"])],64)):(_(),v(k,{key:1},[m(g,{modelValue:e.selectedShoppingInterests,"onUpdate:modelValue":t=>e.selectedShoppingInterests=t,options:Wt.value,props:ta,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"]),m(T,{modelValue:e.selectedShoppingInterests,"onUpdate:modelValue":t=>e.selectedShoppingInterests=t,options:Wt.value,props:ta,"collapse-tags":"",clearable:"",class:"interest-selector"},null,8,["modelValue","onUpdate:modelValue","options"])],64))])])),_:2},1024),m(S,{label:"关键词"},{default:f((()=>{var r;return[w("div",H,[w("div",W,[m(at,{modelValue:ka.value,"onUpdate:modelValue":t[1]||(t[1]=e=>ka.value=e),placeholder:"请输入关键词","prefix-icon":"Search",clearable:"",onInput:A(_a)},{append:f((()=>[m(a,{onClick:A(_a)},{default:f((()=>t[23]||(t[23]=[y("搜索")]))),_:1},8,["onClick"])])),_:1},8,["modelValue","onInput"])]),w("div",X,[w("div",Y,[w("div",Z,[t[25]||(t[25]=w("span",null,"推荐关键词",-1)),m(a,{type:"primary",link:"",onClick:t=>(e=>{e.selectedKeywords||(e.selectedKeywords=[]),ua.value.forEach((t=>{ma(e,t)||e.selectedKeywords.push({id:t.target_word,keyword:t.target_word,cover_num:t.cover_num,recommend_reason:t.recommend_reason})})),c.success("已添加全部关键词")})(e)},{default:f((()=>t[24]||(t[24]=[y(" 全选 ")]))),_:2},1032,["onClick"])]),w("div",ee,[V((_(),D(d,{data:ua.value,style:{width:"100%"},height:"400px"},{default:f((()=>[m(l,{label:"关键词",prop:"target_word"}),m(l,{label:"覆盖人群",prop:"cover_num"},{default:f((e=>{return[y(h((t=e.row.cover_num,t?t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):"0")),1)];var t})),_:1}),m(l,{label:"推荐理由"},{default:f((e=>[(_(!0),v(k,null,b(e.row.recommend_reason,(e=>(_(),D(lt,{key:e,size:"small",style:{"margin-right":"4px"}},{default:f((()=>[y(h(e),1)])),_:2},1024)))),128))])),_:1}),m(l,{label:"操作",width:"100",fixed:"right"},{default:f((t=>[m(a,{type:"primary",link:"",onClick:a=>((e,t)=>{e.selectedKeywords||(e.selectedKeywords=[]),ma(e,t)||(e.selectedKeywords.push({id:t.target_word,keyword:t.target_word,cover_num:t.cover_num,recommend_reason:t.recommend_reason}),c.success("添加关键词成功"))})(e,t.row),disabled:ma(e,t.row)},{default:f((()=>[y(h(ma(e,t.row)?"已添加":"添加"),1)])),_:2},1032,["onClick","disabled"])])),_:2},1024)])),_:2},1032,["data"])),[[ft,pa.value]])])]),w("div",te,[w("div",ae,[t[27]||(t[27]=w("span",null,"已选关键词",-1)),m(a,{type:"primary",link:"",onClick:t=>(e=>{e.selectedKeywords=[],c.success("已清空所有关键词")})(e)},{default:f((()=>t[26]||(t[26]=[y(" 清空 ")]))),_:2},1032,["onClick"])]),w("div",le,[(null==(r=e.selectedKeywords)?void 0:r.length)?(_(!0),v(k,{key:0},b(e.selectedKeywords,(l=>(_(),v("div",{key:l.id,class:"keyword-item"},[w("div",re,[w("span",oe,h(l.keyword),1)]),m(a,{type:"primary",link:"",onClick:t=>((e,t)=>{e.selectedKeywords&&(e.selectedKeywords=e.selectedKeywords.filter((e=>e.id!==t.id)))})(e,l)},{default:f((()=>t[28]||(t[28]=[y(" 删除 ")]))),_:2},1032,["onClick"])])))),128)):(_(),v("div",de," 暂无选中的关键词 "))])])])])]})),_:2},1024),m(S,{label:"人群包"},{default:f((()=>{var r,o;return[w("div",ne,[w("div",se,[w("div",ie,[w("div",ce,[t[30]||(t[30]=w("span",null,null,-1)),m(a,{type:"primary",link:"",onClick:t=>(e=>{var t,a,l;if(!(null==(t=Gt.value)?void 0:t.length))return;const r=(null==(l=null==(a=e.crowd_target)?void 0:a.crowd_pkg)?void 0:l.map((e=>e.value)))||[],o=Gt.value.filter((e=>!r.includes(e.value)));0!==o.length&&(e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),e.crowds||(e.crowds=[]),o.forEach((t=>{e.crowd_target.crowd_pkg.push({name:t.name,value:t.value}),e.crowds.push(t.value)})))})(e)},{default:f((()=>t[29]||(t[29]=[y(" 全选 ")]))),_:2},1032,["onClick"])]),w("div",ge,[V((_(),D(d,{data:Gt.value,style:{width:"100%"},height:"400px"},{default:f((()=>[m(l,{label:"ID",prop:"group_id"}),m(l,{label:"名称",prop:"name"}),m(l,{label:"操作",width:"100",fixed:"right"},{default:f((t=>[m(a,{type:"primary",link:"",onClick:a=>((e,t)=>{ba(e,t)||(e.crowd_target||(e.crowd_target={crowd_pkg:[]}),e.crowd_target.crowd_pkg||(e.crowd_target.crowd_pkg=[]),e.crowd_target.crowd_pkg.push({name:t.name,value:t.value}),e.crowds||(e.crowds=[]),e.crowds.push(t.value))})(e,t.row),disabled:ba(e,t.row)},{default:f((()=>[y(h(ba(e,t.row)?"已添加":"添加"),1)])),_:2},1032,["onClick","disabled"])])),_:2},1024)])),_:2},1032,["data"])),[[ft,Jt.value]])])]),w("div",ue,[w("div",pe,[t[32]||(t[32]=w("span",null,"已选人群包",-1)),m(a,{type:"primary",link:"",onClick:t=>(e=>{var t;e.crowd_target&&((null==(t=e.crowd_target.crowd_pkg)?void 0:t.length)||0)>0&&delete e.crowd_target;e.crowds&&(e.crowds.length,e.crowds=[])})(e)},{default:f((()=>t[31]||(t[31]=[y(" 清空 ")]))),_:2},1032,["onClick"])]),w("div",ve,[(null==(o=null==(r=e.crowd_target)?void 0:r.crowd_pkg)?void 0:o.length)?(_(!0),v(k,{key:0},b(e.crowd_target.crowd_pkg,(l=>(_(),v("div",{key:l.value,class:"crowd-package-item"},[w("div",_e,[w("span",me,h(l.name),1),w("span",we,"(ID: "+h(l.value)+")",1)]),m(a,{type:"primary",link:"",onClick:t=>((e,t)=>{var a,l,r;(null==(l=null==(a=e.crowd_target)?void 0:a.crowd_pkg)?void 0:l.length)&&(e.crowd_target.crowd_pkg.length,e.crowd_target.crowd_pkg=e.crowd_target.crowd_pkg.filter((e=>e.value!==t.value)),0===e.crowd_target.crowd_pkg.length&&delete e.crowd_target,(null==(r=e.crowds)?void 0:r.length)&&(e.crowds.length,e.crowds=e.crowds.filter((e=>e!==t.value))))})(e,l)},{default:f((()=>t[33]||(t[33]=[y(" 删除 ")]))),_:2},1032,["onClick"])])))),128)):(_(),v("div",fe," 暂无选中的人群包 "))])])])])]})),_:2},1024)])),_:2},1024)]),w("div",null,[w("div",ye,[m(ot,{modelValue:e.intelligent_expansion,"onUpdate:modelValue":t=>e.intelligent_expansion=t,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"]),t[34]||(t[34]=w("div",{style:{"margin-left":"10px"}},[w("div",{class:"unit-section-header-top"},[w("span",null,"智能扩量"),w("span",{class:"unit-section-header-top-recommend"},"推荐开启")]),w("div",{class:"unit-section-header-bottom"}," 开启后，系统将基于「高级定向」探索更多人群，可提升整体跑量效果。 ")],-1))])])])):C("",!0),w("div",he,[t[49]||(t[49]=w("div",{class:"section-left"},[w("div",{class:"section-title"},"更多定向")],-1)),w("div",ke,[w("div",be,[t[37]||(t[37]=w("span",{class:"detail-label"},"性别",-1)),m(nt,{modelValue:e.gender,"onUpdate:modelValue":t=>e.gender=t,onChange:t=>(e=>{const t="all",a=Ot.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.gender.length?e.gender.includes(t)?1===e.gender.length?e.gender=[t,...a]:e.gender.length>1&&(e.gender[e.gender.length-1]===t?e.gender=[t,...a]:e.gender=e.gender.filter((e=>e!==t))):e.gender.includes(t)||a.every((t=>e.gender.includes(t)))&&a.length>0&&(e.gender=[t,...a]):e.gender=[t]})(e)},{default:f((()=>[Ot.value.length>0?(_(!0),v(k,{key:0},b(Ot.value,(e=>(_(),D(dt,{key:e.code,label:e.code},{default:f((()=>[y(h(e.name),1)])),_:2},1032,["label"])))),128)):(_(),D(dt,{key:1,label:"all"},{default:f((()=>t[36]||(t[36]=[y("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),w("div",Ce,[t[44]||(t[44]=w("span",{class:"detail-label",style:{"line-height":"32px"}},"地域",-1)),w("div",Ae,[m(nt,{modelValue:e.location,"onUpdate:modelValue":t=>e.location=t,onChange:t=>(e=>{const t="all",a="custom";if(0===e.location.length)return void(e.location=[t]);const l=e.location[e.location.length-1];if(l===t)return e.location=[t],e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={},void(e.selectedNodes=[]);l!==a||(e.location=[a])})(e)},{default:f((()=>[m(dt,{label:"all"},{default:f((()=>t[38]||(t[38]=[y("不限")]))),_:1}),m(dt,{label:"custom"},{default:f((()=>t[39]||(t[39]=[y("自定义")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),e.location.includes("custom")&&!e.location.includes("all")?V((_(),v("div",Ve,[w("div",De,[m(s,{modelValue:e.locationMode,"onUpdate:modelValue":t=>e.locationMode=t},{default:f((()=>[m(n,{label:"country"},{default:f((()=>t[40]||(t[40]=[y("按国家划分")]))),_:1})])),_:2},1032,["modelValue","onUpdate:modelValue"])]),w("div",Re,[w("div",Te,[Jt.value?(_(),v("div",Se,[m(st,{rows:5,animated:""})])):0===Qt.value.length?(_(),v("div",xe," 暂无地域数据 ")):(_(),D(T,{key:2,modelValue:e.selectedNodes,"onUpdate:modelValue":t=>e.selectedNodes=t,options:aa.value,props:ta,onChange:t=>la(e)},null,8,["modelValue","onUpdate:modelValue","options","onChange"]))]),w("div",Ie,[w("div",Ne,[t[42]||(t[42]=w("span",null,"已选",-1)),m(a,{type:"primary",link:"",onClick:t=>(e=>{e.selectedNodes=[],e.selectedLocations=[],e.selectedCities={},e.selectedDistricts={}})(e)},{default:f((()=>t[41]||(t[41]=[y(" 清空 ")]))),_:2},1032,["onClick"])]),w("div",Ue,[ea(e)?(_(!0),v(k,{key:0},b(ra(e),(l=>(_(),v("div",{key:l.code,class:"selected-item"},[w("div",Ke,[w("span",null,h(l.name),1),m(a,{type:"primary",link:"",onClick:t=>oa(e,l)},{default:f((()=>t[43]||(t[43]=[y(" 删除 ")]))),_:2},1032,["onClick"])])])))),128)):(_(),v("div",Le," 暂无选中项 "))])])])])),[[ft,Jt.value]]):C("",!0)])]),w("div",ze,[t[46]||(t[46]=w("span",{class:"detail-label"},"年龄",-1)),m(nt,{modelValue:e.ageRanges,"onUpdate:modelValue":t=>e.ageRanges=t,onChange:t=>(e=>{const t="all",a=jt.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.ageRanges.length?e.ageRanges.includes(t)?1===e.ageRanges.length?e.ageRanges=[t,...a]:e.ageRanges.length>1&&(e.ageRanges[e.ageRanges.length-1]===t?e.ageRanges=[t,...a]:e.ageRanges=e.ageRanges.filter((e=>e!==t))):e.ageRanges.includes(t)||a.every((t=>e.ageRanges.includes(t)))&&a.length>0&&(e.ageRanges=[t,...a]):e.ageRanges=[t]})(e)},{default:f((()=>[jt.value.length>0?(_(!0),v(k,{key:0},b(jt.value,(e=>(_(),D(dt,{key:e.code,label:e.code},{default:f((()=>[y(h(e.name),1)])),_:2},1032,["label"])))),128)):(_(),D(dt,{key:1,label:"all"},{default:f((()=>t[45]||(t[45]=[y("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),w("div",Pe,[t[48]||(t[48]=w("span",{class:"detail-label"},"平台",-1)),m(nt,{modelValue:e.platform,"onUpdate:modelValue":t=>e.platform=t,onChange:t=>(e=>{const t="all",a=qt.value.filter((e=>e.code!==t)).map((e=>e.code));0!==e.platform.length?e.platform.includes(t)?1===e.platform.length?e.platform=[t,...a]:e.platform.length>1&&(e.platform[e.platform.length-1]===t?e.platform=[t,...a]:e.platform=e.platform.filter((e=>e!==t))):e.platform.includes(t)||a.every((t=>e.platform.includes(t)))&&a.length>0&&(e.platform=[t,...a]):e.platform=[t]})(e)},{default:f((()=>[qt.value.length>0?(_(!0),v(k,{key:0},b(qt.value,(e=>(_(),D(dt,{key:e.code,label:e.code},{default:f((()=>[y(h(e.name),1)])),_:2},1032,["label"])))),128)):(_(),D(dt,{key:1,label:"all"},{default:f((()=>t[47]||(t[47]=[y("全部")]))),_:1}))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])])])])])])))),128))])])],512),w("div",Ee,[t[54]||(t[54]=w("div",{class:"section-title"},"出价",-1)),w("div",Be,[m(gt,{model:Tt,"label-width":"120px"},{default:f((()=>[m(ct,{label:"目标成本"},{default:f((()=>[m(it,{modelValue:Tt.bid,"onUpdate:modelValue":t[2]||(t[2]=e=>Tt.bid=e),min:.1,max:100,step:.1,precision:2},null,8,["modelValue"]),t[51]||(t[51]=w("span",{class:"bid-unit"},"元",-1))])),_:1}),Tt.smartBid?(_(),D(ct,{key:0,label:"调价范围"},{default:f((()=>[w("div",Fe,[m(it,{modelValue:Tt.minBid,"onUpdate:modelValue":t[3]||(t[3]=e=>Tt.minBid=e),min:.1,max:Tt.bid,step:.1,precision:2},null,8,["modelValue","max"]),t[52]||(t[52]=w("span",{class:"range-separator"},"至",-1)),m(it,{modelValue:Tt.maxBid,"onUpdate:modelValue":t[4]||(t[4]=e=>Tt.maxBid=e),min:Tt.bid,max:100,step:.1,precision:2},null,8,["modelValue","min"]),t[53]||(t[53]=w("span",{class:"bid-unit"},"元",-1))])])),_:1})):C("",!0)])),_:1},8,["model"])])],512),m(pt,{modelValue:kt.value,"onUpdate:modelValue":t[8]||(t[8]=e=>kt.value=e),size:"1000px","destroy-on-close":!0,direction:"rtl",class:"note-drawer",onClose:Ca},{header:f((()=>[w("div",Me,[w("div",$e,[(_(),v(k,null,b(Mt,(e=>w("div",{key:e.key,class:R(["tab-item",{active:Ft.value===e.key}]),onClick:t=>Ft.value=e.key},h(e.name),11,Je))),64))])])])),default:f((()=>[w("div",Oe,[w("div",je,[m(at,{type:"textarea",modelValue:bt.value,"onUpdate:modelValue":t[5]||(t[5]=e=>bt.value=e),placeholder:"请输入笔记ID，多个ID之间使用回车、英文逗号或空格分隔",style:{width:"460px"},rows:3,autosize:{minRows:3,maxRows:5}},null,8,["modelValue"]),m(a,{type:"primary",onClick:Kt},{default:f((()=>t[55]||(t[55]=[y(" 搜索并选中 ")]))),_:1})]),w("div",qe,[m(a,{type:"primary",onClick:zt},{default:f((()=>t[56]||(t[56]=[y("确定")]))),_:1})])]),w("div",Qe,[w("div",Ge,[V((_(),D(d,{data:Ct.value,border:"","element-loading-text":"加载中....",onSelectionChange:Lt,"row-key":e=>e.noteId,ref_key:"tableRef",ref:Vt,"default-selection":At.value,height:"calc(100% - 54px)"},{default:f((()=>[m(l,{type:"selection",width:"55"}),m(l,{prop:"noteId",label:"笔记ID",width:"180"}),m(l,{label:"图片",width:"100"},{default:f((e=>[m(o,{src:e.row.image,preview:!1,fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src"])])),_:1}),m(l,{prop:"desc",label:"描述","show-overflow-tooltip":"","min-width":"300"},{default:f((e=>[w("div",He,h(e.row.desc),1)])),_:1}),m(l,{prop:"desc",label:"SPU","show-overflow-tooltip":"","min-width":"160"},{default:f((e=>[w("span",We,"累计绑定"+h(e.row.spu)+"个SPU",1)])),_:1}),m(l,{prop:"author",label:"作者",width:"120"}),m(l,{prop:"noteType",label:"笔记形式",width:"100"}),m(l,{prop:"createTime",label:"创建时间",width:"180"}),m(l,{label:"操作",width:"100",fixed:"right"},{default:f((e=>[m(a,{type:"primary",link:"",onClick:t=>{return a=e.row,void(null==(l=Vt.value)||l.toggleRowSelection(a));var a,l}},{default:f((()=>{return[y(h((t=e.row,At.value.some((e=>e.noteId===t.noteId))?"取消选择":"点击选择")),1)];var t})),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","row-key","default-selection"])),[[ft,xt.value]]),w("div",Xe,[m(ut,{"current-page":St.currentPage,"onUpdate:currentPage":t[6]||(t[6]=e=>St.currentPage=e),"page-size":St.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>St.pageSize=e),"page-sizes":[10,20,50,100],total:St.total,onSizeChange:Ut,onCurrentChange:Nt,layout:"total, sizes, prev, pager, next, jumper"},null,8,["current-page","page-size","total"])])])])])),_:1},8,["modelValue"]),m(wt,{modelValue:da.value,"onUpdate:modelValue":t[10]||(t[10]=e=>da.value=e),title:"关联定向包",width:"500px","destroy-on-close":""},{footer:f((()=>[w("span",tt,[m(a,{onClick:ga},{default:f((()=>t[58]||(t[58]=[y("取消")]))),_:1}),m(a,{type:"primary",onClick:ca},{default:f((()=>t[59]||(t[59]=[y(" 确认关联 ")]))),_:1})])])),default:f((()=>[t[60]||(t[60]=w("span",{style:{"font-size":"12px",color:"#999","margin-bottom":"10px"}}," 已有定向包只展示当前广告场景下可用的定向包，高级定向中不包含蒲公英人群和部分平台精选中的人群 ",-1)),w("div",Ye,[w("div",Ze,[t[57]||(t[57]=w("span",{style:{"font-size":"14px",width:"100px"}},"已有定向包",-1)),m(_t,{modelValue:na.value,"onUpdate:modelValue":t[9]||(t[9]=e=>na.value=e),placeholder:"请选择定向包",style:{width:"100%"}},{default:f((()=>[(_(!0),v(k,null,b(sa.value,(e=>(_(),D(vt,{key:e.id,label:e.name,value:e.id},{default:f((()=>[w("span",null,h(e.name),1),w("span",et,h(e.type),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-f381af59"]]);export{at as default};
