import{s as a,r as e,a as s,Q as t,c as l,o,h as d,b as n,w as p,m as u,g as c,d as r}from"./index-BE6Fh1xm.js";import{_ as i}from"./_plugin-vue_export-helper-GSmkUi5K.js";const m={class:"card content-box"},v={class:"box-content"},x=a({name:"copyDirect"}),_=i(a({...x,setup(a){const i=e("我是被复制的内容 🍒 🍉 🍊");return(a,e)=>{const x=s("el-button"),_=s("el-input"),b=t("copy");return o(),l("div",m,[e[2]||(e[2]=d("span",{class:"text"},"复制指令 🍇🍇🍇🍓🍓🍓",-1)),d("div",v,[n(_,{modelValue:i.value,"onUpdate:modelValue":e[0]||(e[0]=a=>i.value=a),placeholder:"请输入内容",style:{width:"500px"}},{append:p((()=>[u((o(),c(x,null,{default:p((()=>e[1]||(e[1]=[r(" 复制 ")]))),_:1})),[[b,i.value]])])),_:1},8,["modelValue"])])])}}}),[["__scopeId","data-v-984db9d4"]]);export{_ as default};
