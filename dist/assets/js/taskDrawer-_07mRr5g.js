import{s as e,a6 as l,r as a,a as t,g as s,o,w as r,h as i,b as n,c as d,F as p,i as c,d as m,t as u,E as f}from"./index-BE6Fh1xm.js";import{_ as g}from"./_plugin-vue_export-helper-GSmkUi5K.js";const _={style:{height:"100%"}},b=e({name:"UserDrawer"}),x=g(e({...b,setup(e,{expose:g}){l({project_name:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],custom_name:[{required:!0,message:"请选择客户",trigger:"blur"}]}),a([]);const b=a(!1),x=a({isView:!1,title:"",row:{}}),y=l({project_id:"",task_name:"",promotion_platforms_genres:1,task_type:1,settlement_method:1}),h=a();return g({acceptParams:e=>{x.value=e,b.value=!0}}),(e,l)=>{const a=t("Close"),g=t("el-icon"),v=t("el-option"),w=t("el-select"),k=t("el-form-item"),j=t("el-input"),z=t("el-button"),V=t("el-form"),C=t("el-drawer");return o(),s(C,{modelValue:b.value,"onUpdate:modelValue":l[4]||(l[4]=e=>b.value=e),"destroy-on-close":!0,size:"450px",title:`${x.value.title}任务`},{default:r((()=>[i("button",{class:"el-drawer__close-btn",style:{display:"flex","justify-content":"center","align-items":"center",color:"aliceblue"},onClick:l[0]||(l[0]=(...l)=>e.closeDrawer&&e.closeDrawer(...l)),type:"button"},[n(g,{class:"el-drawer__close"},{default:r((()=>[n(a)])),_:1})]),i("div",_,[n(V,{model:e.form,rules:e.rule,ref_key:"ruleForm",ref:h,"label-width":"auto",style:{"max-width":"700px",height:"98%",display:"flex","flex-direction":"column","justify-content":"space-between"},class:"form-style"},{default:r((()=>[i("div",null,[l[5]||(l[5]=i("span",{class:"form-span"},"基本信息",-1)),n(k,{label:"所属项目",prop:"project_id"},{default:r((()=>[n(w,{modelValue:y.project_id,"onUpdate:modelValue":l[1]||(l[1]=e=>y.project_id=e),filterable:"",remote:"","reserve-keyword":"",placeholder:"请搜索项目名称","remote-method":e.remoteMethod,loading:e.loading2,style:{width:"240px"},onChange:e.handleSelectChange},{default:r((()=>[(o(!0),d(p,null,c(e.options,(e=>(o(),s(v,{key:e.custom_id,label:e.project_name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","remote-method","loading","onChange"])])),_:1}),n(k,{label:"任务名称",prop:"task_name"},{default:r((()=>[n(j,{modelValue:y.task_name,"onUpdate:modelValue":l[2]||(l[2]=e=>y.task_name=e),placeholder:"请输入任务名称"},null,8,["modelValue"])])),_:1}),l[6]||(l[6]=i("span",{class:"form-span"},"任务类型",-1)),l[7]||(l[7]=i("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*推广平台和体裁",-1)),l[8]||(l[8]=i("div",{class:"flex task-label"},[i("div",{style:{width:"30px",height:"30px",background:"linear-gradient(rgba(179, 98, 205, 1), rgba(179, 98, 205, 0.2))","margin-right":"10px"}}),i("div",null,[i("p",{style:{color:"#b362cd","font-size":"14px","margin-bottom":"5px"}},"抖音"),i("p",{style:{color:"#999","font-size":"12px"}},"王牌渠道锁定新生代消费主力")])],-1)),l[9]||(l[9]=i("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*任务类型",-1)),l[10]||(l[10]=i("div",{class:"flex mb-5 task-label"},[i("div",{style:{width:"30px",height:"30px",background:"linear-gradient(rgba(179, 98, 205, 1), rgba(179, 98, 205, 0.2))","margin-right":"10px"}}),i("div",null,[i("p",{style:{color:"#b362cd","font-size":"14px","margin-bottom":"5px"}},"指派"),i("p",{style:{color:"#999","font-size":"12px"}},"有明确达人或想指定达人接单")])],-1)),l[11]||(l[11]=i("span",{class:"form-span"},"结算方式",-1)),l[12]||(l[12]=i("p",{class:"mt-8 mb-2",style:{"font-size":"14px",color:"#333"}},"*结算方式",-1)),l[13]||(l[13]=i("div",{class:"flex task-label"},[i("div",null,[i("p",{style:{color:"#b362cd","font-size":"14px","margin-bottom":"5px"}},"按一口价结算"),i("p",{style:{color:"#999","font-size":"12px"}},"以固定价格与达人合作")])],-1))]),i("div",null,[n(k,{class:"mt-5 form-btn"},{default:r((()=>[n(z,{onClick:e.closeDrawer},{default:r((()=>l[14]||(l[14]=[m("取消")]))),_:1},8,["onClick"]),n(z,{type:"primary",onClick:l[3]||(l[3]=e=>(async e=>{e&&await e.validate(((e,l)=>{if(e){let e=JSON.parse(JSON.stringify(form.value)),l=options.value.filter((l=>l.id===e.project_id));e.project_name=l[0].project_name,0==projectDrawerType.value?addTasks(e).then((e=>{990==e.code&&(f.success("创建成功！"),projectAddDrawer.value=!1,getTaskList())})):updateTasks(e).then((e=>{990==e.code&&(f.success("修改成功！"),projectAddDrawer.value=!1,getTaskList())}))}}))})(h.value))},{default:r((()=>[m(u(0==e.projectDrawerType?"创建任务":"编辑任务"),1)])),_:1})])),_:1})])])),_:1},8,["model","rules"])])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-aec6df90"]]);export{x as default};
