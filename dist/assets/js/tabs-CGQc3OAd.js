import{D as e,S as s,ap as t,aq as i}from"./index-BE6Fh1xm.js";const a=e({id:"geeker-keepAlive",state:()=>({keepAliveName:[]}),actions:{async addKeepAliveName(e){!this.keepAliveName.includes(e)&&this.keepAliveName.push(e)},async removeKeepAliveName(e){this.keepAliveName=this.keepAliveName.filter((s=>s!==e))},async setKeepAliveName(e=[]){this.keepAliveName=e}}}),n=a(),h=e({id:"geeker-tabs",state:()=>({tabsMenuList:[]}),actions:{async addTabs(e){this.tabsMenuList.every((s=>s.path!==e.path))&&this.tabsMenuList.push(e),!n.keepAliveName.includes(e.name)&&e.isKeepAlive&&n.addKeepAliveName(e.path)},async removeTabs(e,s=!0){s&&this.tabsMenuList.forEach(((s,t)=>{if(s.path!==e)return;const a=this.tabsMenuList[t+1]||this.tabsMenuList[t-1];a&&i.push(a.path)}));const t=this.tabsMenuList.find((s=>s.path===e));(null==t?void 0:t.isKeepAlive)&&n.removeKeepAliveName(t.path),this.tabsMenuList=this.tabsMenuList.filter((s=>s.path!==e))},async closeTabsOnSide(e,s){const t=this.tabsMenuList.findIndex((s=>s.path===e));if(-1!==t){const e="left"===s?[0,t]:[t+1,this.tabsMenuList.length];this.tabsMenuList=this.tabsMenuList.filter(((s,t)=>t<e[0]||t>=e[1]||!s.close))}const i=this.tabsMenuList.filter((e=>e.isKeepAlive));n.setKeepAliveName(i.map((e=>e.path)))},async closeMultipleTab(e){this.tabsMenuList=this.tabsMenuList.filter((s=>s.path===e||!s.close));const s=this.tabsMenuList.filter((e=>e.isKeepAlive));n.setKeepAliveName(s.map((e=>e.path)))},async setTabs(e){this.tabsMenuList=e},async setTabsTitle(e){this.tabsMenuList.forEach((s=>{s.path==t()&&(s.title=e)}))}},persist:s("geeker-tabs")});export{a,h as u};
