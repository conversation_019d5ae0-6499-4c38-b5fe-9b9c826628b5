import{k as o}from"./index-BE6Fh1xm.js";const t=t=>o.post("/getCommercialOrderDetail",t),e=t=>o.post("/getCommercialOrder",t),a=t=>o.post("/getCommercialKol",t),s=t=>o.post("/getCommercialKolDetail",t),m=t=>o.post("/getCommercialKolStruct",t),r=t=>o.post("/getCommercialKolHot",t),i=t=>o.post("/getCommercialKolDistribution",t),l=t=>o.post("/getCommercialKolTouchDistribution",t),c=t=>o.get("/getCommercialKolIndustry",t),p=t=>o.post("/getCommercialKolCard",t),g=t=>o.post("/getCommercialKolLink",t),C=t=>o.post("/getCommercialKolFans",t),K=t=>o.post("/getCommercialKolVideoDistribution",t),n=t=>o.post("/getCommercialKolSpread",t),d=t=>o.post("/getCommercialKolShowInfo",t),u=t=>o.post("/exportCommercialOrder",t);export{d as a,n as b,r as c,i as d,l as e,m as f,K as g,c as h,p as i,g as j,C as k,e as l,u as m,a as n,s as o,t as p};
