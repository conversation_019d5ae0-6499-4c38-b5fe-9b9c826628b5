import{H as e,r as a,f as l,p as t,a as r,c as o,o as u,b as n,y as d,w as i,h as s,g as _,n as m,F as p,i as v,d as c,t as f,E as h}from"./index-BE6Fh1xm.js";import k from"./addProject-gW6259RI.js";import{a as b,p as g,e as y,c as w,b as V,t as x}from"./index-BxStTrC5.js";import{_ as j}from"./_plugin-vue_export-helper-GSmkUi5K.js";const S={class:"pb-20"},D={key:0,class:"container_form mt-2 px-2"},C={class:"flex items-center"},U={class:"flex",style:{width:"98%","justify-content":"space-between"}},E={class:"grey-6"},Y={class:"mt-2"},H={key:1,class:"container_form mt-2"},F={class:"grey-6"},I={class:"mt-2"},L={class:"overflow-auto"},M=j({__name:"xtTaskDetailStatus1",props:["type","id","taskDetail","kolList","platform","isShow"],emits:["addKolHandler","closeDrawer"],setup(j,{expose:M,emit:N}){e();const q=a(""),$=a(""),z=a([]),A=a([]),J=a({}),O=a([]),R=a(0),K=a(null);a(""),a(""),a("");const P=a(""),Q=a(0),T=a(!1),B=a(!1),G=j,W=N;let X=a("");const Z=a(""),ee=a(1);G&&(X.value=G.type,Z.value=G.id,ee.value=G.platform),l([()=>G.taskDetail],(e=>{$.value=G.taskDetail,ae.value=G.taskDetail,J.value={alliance_personnel:G.taskDetail.alliance_personnel,alliance_personnel_id:G.taskDetail.alliance_personnel_id}})),l((()=>G.kolList),(e=>{q.value=e,fe()})),l((()=>G.type),(e=>{X.value=e})),l((()=>G.id),(e=>{Z.value=e})),l((()=>G.platform),(e=>{ee.value=e}));const ae=a({area:"",short_name:"",brand_name:"",task_name:"",task_time:"",media_platform:"",anticipation_time:"",marketing_target:"",other_demand:"",project_id:"",task_type:"",platform_status:1,alliance_personnel_id:"",alliance_personnel:""}),le={1:"指派任务",2:"招募任务",3:"投稿任务",4:"星广联投"},te={5:"小红星",6:"小红盟",7:"蒲公英"},re={1:{label:"1-20S视频",value:"price_1_20"},2:{label:"21-60S视频",value:"price_20_60"},3:{label:"60S以上视频",value:"price_60"},4:{label:"图文笔记一口价",value:"redbook_graphic"},5:{label:"视频笔记一口价",value:"redbook_video"}},oe={project_id:[{required:!0,message:"请选择项目名称",trigger:"change"}],task_type:[{required:!0,message:"请选择任务类型",trigger:"change"}],task_date:[{required:!0,message:"请选择期望发布时间",trigger:"change",validator:(e,a,l)=>{ae.value.anticipation_time?l():l(new Error("请选择期望发布时间"))}}],anticipation_time:[{type:"array",required:!0,message:"请选择任务发布时间",trigger:"change"}],task_name:[{required:!0,trigger:"blur",validator:(e,a,l)=>{ae.value.area?ae.value.brand_name?ae.value.short_name?ae.value.task_time?l():l(new Error("请填写任务时间")):l(new Error("请填写客户简称")):l(new Error("请填写品牌")):l(new Error("请选择地区"))}}]},ue=()=>{ae.value.project_id?(B.value=!0,de()):h.error("请先选择项目")},ne=()=>{let e=A.value.filter((e=>e.id==P.value))[0];ae.value.marketing_target=e.marketing_target,ae.value.task_type=e.task_type,ae.value.area=e.area,ae.value.brand_name=e.brand_name,ae.value.short_name=e.short_name,ae.value.task_time=e.task_time,ae.value.anticipation_time=e.anticipation_time,ae.value.other_demand=e.other_demand,J.value={alliance_personnel:e.alliance_personnel,alliance_personnel_id:e.alliance_personnel_id},B.value=!1},de=()=>{x({project_id:ae.value.project_id,page:1,page_size:60}).then((e=>{A.value=e.data.list}))},ie=JSON.parse(localStorage.getItem("taskDetail"));"add"==X.value?(q.value=JSON.parse(localStorage.getItem("kolList"))?JSON.parse(localStorage.getItem("kolList")):[],R.value=R.value++,5==ee.value?ae.value.task_type=1:ae.value.task_type=7):ie&&($.value=ie,ae.value=ie,q.value=ie.kol_select_list,J.value={alliance_personnel:ie.alliance_personnel,alliance_personnel_id:ie.alliance_personnel_id});const se=()=>{W("addKolHandler")},_e=e=>{T.value=!1,"refresh"==e&&fe()},me=async e=>{let a=ve();if(!a.kol_data.length>0)return h.warning("请先添加达人"),!1;K.value&&await K.value.validate(((l,t)=>{l&&("edit"==X.value||"reEdit1"==X.value||"reEdit2"==X.value?(a.id=Z.value,a.status=e,y(a).then((e=>{K.value.resetFields(),ae.value={area:"",short_name:"",brand_name:"",task_name:"",task_time:"",media_platform:"",anticipation_time:"",marketing_target:"",other_demand:"",project_id:"",task_type:"",platform_status:1,alliance_personnel_id:"",alliance_personnel:""},h.success("提交成功"),1==Q.value?5==a.platform?window.location.href="/business/management?key=2-5":window.location.href="/business/management?key=2-106":W("closeDrawer","refresh")}))):(a.status=e,w(a).then((e=>{K.value.resetFields(),ae.value={area:"",short_name:"",brand_name:"",task_name:"",task_time:"",media_platform:"",anticipation_time:"",marketing_target:"",other_demand:"",project_id:"",task_type:"",platform_status:1,alliance_personnel_id:"",alliance_personnel:""},h.success("提交成功"),1==Q.value?5==a.platform?window.location.href="/business/management?key=2-5":window.location.href="/business/management?key=2-106":W("closeDrawer","refresh")}))))}))},pe=()=>{W("closeDrawer","back")},ve=e=>{const a=[];let l="";return l="add"==X.value?ae.value.area+"-"+ae.value.short_name+"-"+ae.value.brand_name+"-"+ae.value.task_time:ae.value.task_name.split("-")[0]+ae.value.area+"-"+ae.value.short_name+"-"+ae.value.brand_name+"-"+ae.value.task_time,q.value.map((e=>{a.push({kol_name:e.kol_name,platform_uid:e.platform_uid,price:e.price,rebate_ratio:e.rebate_ratio?e.rebate_ratio:0,homepage:e.homepage?e.homepage:"",placement_type:e.placement_type})})),{status:e,task_name:l,area:ae.value.area,task_time:ae.value.task_time,short_name:ae.value.short_name,brand_name:ae.value.brand_name,project_id:ae.value.project_id,other_demand:ae.value.other_demand,marketing_target:ae.value.marketing_target,task_type:ae.value.task_type,alliance_personnel:J.value.alliance_personnel,alliance_personnel_id:J.value.alliance_personnel_id,platform:"add"==X.value?ee.value:$.value.platform,anticipation_time:ae.value.anticipation_time,platform_status:ae.value.platform_status,kol_data:a}},ce=()=>{he(ae.value.project_id)};b({role_id:85}).then((e=>{z.value=e.data.list}));const fe=()=>{g({project_type:"add"==X.value?ee.value:$.value.platform}).then((e=>{O.value=e.data.list}))};fe(),l((()=>G.isShow),(e=>{e&&fe()}));const he=e=>{V({project_id:e}).then((e=>{ae.value.short_name=e.data.short_customer}))};return t((()=>{let e=new Date,a=e.getFullYear().toString().substring(2),l=(e.getMonth()+1).toString().padStart(2,"0"),t=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0");ae.value.task_time=`${a}-${l}${t}-${r}:${o}`})),M({onSubmit:me,projectSearchList:fe}),(e,a)=>{var l;const t=r("el-option"),h=r("el-select"),b=r("el-button"),g=r("el-form-item"),y=r("el-col"),w=r("el-row"),V=r("QuestionFilled"),x=r("el-icon"),j=r("el-tooltip"),M=r("el-input"),N=r("el-radio"),R=r("el-radio-group"),G=r("el-date-picker"),W=r("el-table-column"),Z=r("el-popconfirm"),de=r("el-table"),ie=r("el-form"),ve=r("el-dialog"),fe=r("Close"),he=r("el-drawer");return u(),o("div",S,["add"==d(X)||0===(null==(l=$.value)?void 0:l.status)&&"edit"==d(X)||"reEdit1"==d(X)?(u(),o("div",D,[n(ie,{model:ae.value,ref_key:"ruleFormRef",ref:K,rules:oe,"label-width":"110px","label-position":"right"},{default:i((()=>[a[31]||(a[31]=s("div",{class:"form_title"},[s("h3",null,"基本信息")],-1)),n(w,{gutter:24},{default:i((()=>[n(y,{span:13},{default:i((()=>[n(g,{label:"项目名称",prop:"project_id"},{default:i((()=>[n(h,{style:{width:"200px"},modelValue:ae.value.project_id,"onUpdate:modelValue":a[0]||(a[0]=e=>ae.value.project_id=e),placeholder:"请选择项目名称",onChange:ce},{default:i((()=>[(u(!0),o(p,null,v(O.value,(e=>(u(),_(t,{label:e.project_name,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"]),"add"==d(X)?(u(),_(b,{key:0,style:{"margin-left":"20px"}},{default:i((()=>[s("a",{onClick:a[1]||(a[1]=e=>T.value=!0)},"新建项目")])),_:1})):m("",!0)])),_:1})])),_:1})])),_:1}),n(w,{gutter:24,class:""},{default:i((()=>[n(y,{span:24},{default:i((()=>[n(g,{label:"任务名称",prop:"task_name"},{default:i((()=>[s("div",C,[n(j,{class:"relative right-24",effect:"dark",content:"任务名称由自增码_地区_客户简称_品牌名称_时间构成，自增码系统会自动生成，无需填写",placement:"top"},{default:i((()=>[n(x,{color:"#333333",class:"no-inherit"},{default:i((()=>[n(V)])),_:1})])),_:1}),n(h,{modelValue:ae.value.area,"onUpdate:modelValue":a[2]||(a[2]=e=>ae.value.area=e),style:{width:"90px","margin-left":"-15px"},placeholder:"地区"},{default:i((()=>[n(t,{value:"华南"}),n(t,{value:"华北"}),n(t,{value:"华东"}),n(t,{value:"伊利"})])),_:1},8,["modelValue"]),n(M,{style:{width:"130px"},placeholder:"简称",modelValue:ae.value.short_name,"onUpdate:modelValue":a[3]||(a[3]=e=>ae.value.short_name=e)},null,8,["modelValue"]),n(M,{style:{width:"180px"},placeholder:"请填写品牌",modelValue:ae.value.brand_name,"onUpdate:modelValue":a[4]||(a[4]=e=>ae.value.brand_name=e)},null,8,["modelValue"]),n(M,{style:{width:"130px"},modelValue:ae.value.task_time,"onUpdate:modelValue":a[5]||(a[5]=e=>ae.value.task_time=e)},null,8,["modelValue"]),s("span",{class:"ml-2 cursor-pointer text-blue-500",onClick:ue},"复制任务")])])),_:1})])),_:1})])),_:1}),n(w,{gutter:24,class:""},{default:i((()=>[n(y,{span:10},{default:i((()=>[n(g,{label:"任务类型"},{default:i((()=>[5==ee.value&&"add"==d(X)||5==ae.value.platform?(u(),_(h,{key:0,style:{width:"200px"},modelValue:ae.value.task_type,"onUpdate:modelValue":a[6]||(a[6]=e=>ae.value.task_type=e),placeholder:"请选择任务类型"},{default:i((()=>[(u(),o(p,null,v(le,((e,a)=>n(t,{label:e,value:Number(a)},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])):m("",!0),106==ee.value&&"add"==d(X)||106==ae.value.platform?(u(),_(h,{key:1,style:{width:"200px"},modelValue:ae.value.task_type,"onUpdate:modelValue":a[7]||(a[7]=e=>ae.value.task_type=e),placeholder:"请选择任务类型"},{default:i((()=>[(u(),o(p,null,v(te,((e,a)=>n(t,{label:e,value:Number(a)},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])):m("",!0)])),_:1})])),_:1}),n(y,{span:10},{default:i((()=>[n(g,{label:"营销目标",prop:"marketing_target","label-width":"110px"},{default:i((()=>[n(M,{"show-word-limit":"",maxlength:"20",modelValue:ae.value.marketing_target,"onUpdate:modelValue":a[8]||(a[8]=e=>ae.value.marketing_target=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(w,{gutter:24,class:""},{default:i((()=>[n(y,{span:12},{default:i((()=>[n(g,{label:"平台任务","label-width":"110px"},{default:i((()=>[n(R,{modelValue:ae.value.platform_status,"onUpdate:modelValue":a[9]||(a[9]=e=>ae.value.platform_status=e)},{default:i((()=>[n(N,{label:1},{default:i((()=>a[22]||(a[22]=[c("是")]))),_:1}),n(N,{label:0},{default:i((()=>a[23]||(a[23]=[c("否")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),a[32]||(a[32]=s("div",{class:"form_title"},[s("h3",null,"任务要求")],-1)),n(w,{gutter:24,class:""},{default:i((()=>[n(y,{span:13},{default:i((()=>[n(g,{label:"期望发布时间",prop:"task_date"},{default:i((()=>[n(G,{modelValue:ae.value.anticipation_time,"onUpdate:modelValue":a[10]||(a[10]=e=>ae.value.anticipation_time=e),type:"datetime",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"期望发布时间"},null,8,["modelValue"])])),_:1})])),_:1}),n(y,{span:9},{default:i((()=>[n(g,{label:"其他要求","label-width":"90px"},{default:i((()=>[n(M,{modelValue:ae.value.other_demand,"onUpdate:modelValue":a[11]||(a[11]=e=>ae.value.other_demand=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),a[33]||(a[33]=s("div",{class:"form_title"},[s("h3",null,"达人信息")],-1)),s("div",U,[s("p",E," 媒体平台："+f("edit"==d(X)||"reEdit1"==d(X)||"reEdit2"==d(X)?5==ae.value.platform?"抖音":"小红书":5==ee.value?"抖音":"小红书"),1),"info"!==d(X)?(u(),_(b,{key:0,size:"small",onClick:se},{default:i((()=>a[24]||(a[24]=[c("新增达人")]))),_:1})):m("",!0)]),s("div",Y,[n(de,{data:q.value,border:"",style:{width:"98%"},"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"}},{default:i((()=>[n(W,{prop:"kol_name",label:"达人名称",width:"180"}),n(W,{prop:"platform_uid",label:"达人ID",width:"180"}),n(W,{prop:"name",label:"合作形式",width:"180"},{default:i((e=>{var a,l;return[c(f(null==(l=re[null==(a=e.row)?void 0:a.placement_type])?void 0:l.label),1)]})),_:1}),n(W,{prop:"name",label:"平台裸价"},{default:i((e=>{var a;return[s("div",null,f(null==(a=e.row)?void 0:a.price),1)]})),_:1}),n(W,{prop:"name",label:"合作返点"},{default:i((e=>{var a;return[c(f(100*(null==(a=e.row)?void 0:a.rebate_ratio))+"% ",1)]})),_:1}),n(W,{prop:"name",label:"操作"},{default:i((e=>[n(Z,{title:"确定删除当前达人么?",onConfirm:a=>{var l;return(e=>{let a=q.value.findIndex((a=>a.id==e));q.value.splice(a,1),localStorage.setItem("kolList",JSON.stringify(q.value))})(null==(l=e.row)?void 0:l.id)},onCancel:a[12]||(a[12]=()=>{})},{reference:i((()=>[n(b,{size:"small"},{default:i((()=>a[25]||(a[25]=[c("删除")]))),_:1})])),_:2},1032,["onConfirm"])])),_:1})])),_:1},8,["data"])]),a[34]||(a[34]=s("div",{class:"form_title mt-2"},[s("h3",null,"指派建联人员")],-1)),n(g,{label:"指派建联人员"},{default:i((()=>[n(h,{style:{width:"200px"},modelValue:J.value,"onUpdate:modelValue":a[13]||(a[13]=e=>J.value=e),"value-key":"alliance_personnel_id",clearabley:"",placeholder:"请选择指派建联人员"},{default:i((()=>[(u(!0),o(p,null,v(z.value,((e,a)=>(u(),_(t,{label:e.name,value:{alliance_personnel_id:e.id,alliance_personnel:e.name,index:a}},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),n(g,{label:"跳转至任务列表"},{default:i((()=>[n(R,{modelValue:Q.value,"onUpdate:modelValue":a[14]||(a[14]=e=>Q.value=e)},{default:i((()=>[n(N,{label:1},{default:i((()=>a[26]||(a[26]=[c("是")]))),_:1}),n(N,{label:0},{default:i((()=>a[27]||(a[27]=[c("否")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),n(g,{class:"form-footer"},{default:i((()=>["reEdit1"!=d(X)?(u(),_(b,{key:0,onClick:a[15]||(a[15]=e=>me(0))},{default:i((()=>a[28]||(a[28]=[c("存为草稿")]))),_:1})):m("",!0),n(b,{type:"primary",onClick:a[16]||(a[16]=e=>me(1))},{default:i((()=>a[29]||(a[29]=[c("提交")]))),_:1}),n(b,{type:"primary",onClick:a[17]||(a[17]=e=>pe())},{default:i((()=>a[30]||(a[30]=[c("返回")]))),_:1})])),_:1})])),_:1},8,["model"])])):(u(),o("div",H,[n(ie,{model:e.form,"label-width":"120px"},{default:i((()=>{var e,l;return[a[36]||(a[36]=s("div",{class:"form_title"},[s("h3",null,"项目信息")],-1)),n(g,{label:"项目名称"},{default:i((()=>{var e;return[c(f(null==(e=$.value)?void 0:e.project_name),1)]})),_:1}),a[37]||(a[37]=s("div",{class:"form_title"},[s("h3",null,"营销目标")],-1)),n(g,{label:"营销目标"},{default:i((()=>{var e,a;return[c(f((null==(e=$.value)?void 0:e.marketing_target)?null==(a=$.value)?void 0:a.marketing_target:"无"),1)]})),_:1}),a[38]||(a[38]=s("div",{class:"form_title"},[s("h3",null,"任务要求")],-1)),n(g,{label:"期望发布时间"},{default:i((()=>{var e,a;return[c(f(null==(e=$.value)?void 0:e.task_create_time)+" - "+f(null==(a=$.value)?void 0:a.task_end_time),1)]})),_:1}),n(g,{label:"其他要求"},{default:i((()=>{var e,a;return[c(f((null==(e=$.value)?void 0:e.other_demand)?null==(a=$.value)?void 0:a.other_demand:"无"),1)]})),_:1}),a[39]||(a[39]=s("div",{class:"form_title"},[s("h3",null,"达人信息")],-1)),s("p",F,"媒体平台："+f("5"==(null==(e=$.value)?void 0:e.platform)?"抖音":"小红书"),1),s("div",I,[n(de,{data:null==(l=$.value)?void 0:l.kol_select_list,border:"",style:{width:"80%"},"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"}},{default:i((()=>[n(W,{prop:"kol_name",label:"昵称",width:"180"}),n(W,{label:"合作形式",width:"180"},{default:i((e=>{var a;return[c(f(re[null==(a=e.row)?void 0:a.placement_type].label),1)]})),_:1}),n(W,{prop:"name",label:"金额"},{default:i((e=>{var a;return[c(f(null==(a=e.row)?void 0:a.price),1)]})),_:1}),n(W,{prop:"name",label:"服务费返点"},{default:i((e=>{var a;return[c(f(100*(null==(a=e.row)?void 0:a.rebate_ratio))+"% ",1)]})),_:1})])),_:1},8,["data"])]),a[40]||(a[40]=s("div",{class:"form_title mt-2"},[s("h3",null,"任务名称")],-1)),n(g,{label:"任务名称"},{default:i((()=>{var e;return[c(f(null==(e=$.value)?void 0:e.task_name),1)]})),_:1}),n(g,{class:"form-footer"},{default:i((()=>[n(b,{type:"primary",onClick:a[18]||(a[18]=e=>pe())},{default:i((()=>a[35]||(a[35]=[c("返回")]))),_:1})])),_:1})]})),_:1},8,["model"])])),n(ve,{modelValue:B.value,"onUpdate:modelValue":a[20]||(a[20]=e=>B.value=e),width:"400px",title:"复制任务"},{default:i((()=>[n(ie,{model:e.copyForm},{default:i((()=>[n(g,{label:"任务名称"},{default:i((()=>[n(h,{style:{width:"200px"},modelValue:P.value,"onUpdate:modelValue":a[19]||(a[19]=e=>P.value=e)},{default:i((()=>[(u(!0),o(p,null,v(A.value,(e=>(u(),_(t,{label:e.task_name,value:e.id},null,8,["label","value"])))),256))])),_:1},8,["modelValue"])])),_:1}),n(g,null,{default:i((()=>[n(b,{class:"float-right",onClick:ne,type:"primary"},{default:i((()=>a[41]||(a[41]=[c("确定")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),n(he,{ref:"drawerRef",title:"新建项目",modelValue:T.value,"onUpdate:modelValue":a[21]||(a[21]=e=>T.value=e),"before-close":e.handleClose,class:"demo-drawer",size:"600px"},{default:i((()=>[s("button",{class:"el-drawer__close-btn",onClick:_e,type:"button"},[n(x,null,{default:i((()=>[n(fe)])),_:1})]),s("div",L,[n(k,{ref:"addProjectRef",onCloseDrawer:_e,type:ee.value},null,8,["type"])])])),_:1},8,["modelValue","before-close"])])}}},[["__scopeId","data-v-75672829"]]);export{M as default};
