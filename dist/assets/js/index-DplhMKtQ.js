import{k as e,H as t,r as a,a6 as n,p as o,f as l,a as r,c as i,o as s,h as c,d as u,F as d,i as m,g as h,w as b,t as p,z as v,y as g,n as _,b as k,at as f,E as w,a3 as y}from"./index-C2bfFjZ1.js";import{_ as D}from"./_plugin-vue_export-helper-BXFjo1rG.js";const C={customers:{key:"customers",title:"总客户/品牌数",headerColor:"#67c23a",unit:"",showBreakdown:!1,card_id:1},orders:{key:"orders",title:"总下单量",headerColor:"#4468bd",unit:"单",showBreakdown:!0,breakdown:{machine:{label:"机构达人下单量",color:"#409eff"},nonMachine:{label:"非机构达人下单量",color:"#f56c6c"}},card_id:2},amount:{key:"amount",title:"总下单金额",headerColor:"#9254de",unit:"元",showBreakdown:!0,breakdown:{machine:{label:"机构达人下单金额",color:"#409eff"},nonMachine:{label:"非机构达人下单金额",color:"#f56c6c"}},card_id:3},posts:{key:"posts",title:"总发布量",headerColor:"#e6a23c",unit:"单",showBreakdown:!0,breakdown:{machine:{label:"机构达人发布量",color:"#409eff"},nonMachine:{label:"非机构达人发布量",color:"#f56c6c"}},card_id:4},completed:{key:"completed",title:"总结算量",headerColor:"#f56c6c",unit:"单",showBreakdown:!0,breakdown:{machine:{label:"机构达人结算量",color:"#409eff"},nonMachine:{label:"非机构达人结算量",color:"#f56c6c"}},card_id:5},settlement_amount:{key:"settlement_amount",title:"总结算金额",headerColor:"#722ed1",unit:"元",showBreakdown:!0,breakdown:{machine:{label:"机构达人结算金额",color:"#409eff"},nonMachine:{label:"非机构达人结算金额",color:"#f56c6c"}},card_id:6}},M={ordersDistribution:{title:"机构下单量分布",type:"pie",colors:["#5B8FF9","#8378EA"],card_id:7,kol_attributes:2},amountDistribution:{title:"机构下单金额分布",type:"pie",colors:["#41D9C7","#5AD8A6"],card_id:8,kol_attributes:2},topAgencies:{title:"下单金额TOP10机构",type:"bar",card_id:9,kol_attributes:2}},x={platforms:[{key:"1",label:"抖音"},{key:"2",label:"小红书"}],timeRanges:[{key:"week",label:"近一周"},{key:"month",label:"近一个月"},{key:"three_months",label:"近三个月"},{key:"custom",label:"自定义时间"}]},A={class:"data-dashboard-container"},S={class:"filter-wrapper"},V={class:"filter-options"},z={class:"platform-filter"},L={class:"platform-buttons"},F={class:"time-filter"},O={class:"time-buttons"},W={key:0,class:"date-picker-wrapper"},$={class:"dashboard-body"},B={key:0,class:"loading-container"},R={class:"data-cards"},T=["onClick","title"],E={class:"card-body"},H={class:"main-value"},j={key:0,style:{"font-size":"60px","font-weight":"660"}},I={key:1},G={key:2,class:"unit"},N={key:0,class:"breakdown"},P={class:"breakdown-item"},q={class:"item-label"},U={class:"item-value"},Y={class:"item-percent"},J={class:"breakdown-item"},K={class:"item-label"},Q={class:"item-value"},X={class:"item-percent"},Z={class:"chart-section"},ee={class:"chart-title"},te={class:"chart-title"},ae={class:"chart-title"},ne=D({__name:"index",setup(D){const ne=t(),oe=a("1"),le=a("week"),re=a([]),ie=a(!1),se=a(""),ce=a(""),ue=a(!1),de=a(!1),me=n({customers:{mainValue:0},orders:{mainValue:0,breakdown:{machine:{value:0,percentage:0},nonMachine:{value:0,percentage:0}}},amount:{mainValue:0,breakdown:{machine:{value:0,percentage:0},nonMachine:{value:0,percentage:0}}},posts:{mainValue:0,breakdown:{machine:{value:0,percentage:0},nonMachine:{value:0,percentage:0}}},completed:{mainValue:0,breakdown:{machine:{value:0,percentage:0},nonMachine:{value:0,percentage:0}}},settlement_amount:{mainValue:0,breakdown:{machine:{value:0,percentage:0},nonMachine:{value:0,percentage:0}}}}),he=n({ordersDistribution:[],amountDistribution:[],topAgencies:[]}),be=a(null),pe=a(null),ve=a(null),ge=n({ordersDistributionChart:null,amountDistributionChart:null,topAgenciesChart:null}),_e=e=>e.getTime()>Date.now(),ke=e=>null==e?"0":"number"==typeof e?e.toLocaleString():"0",fe=(e,t)=>t?+(e/t*100).toFixed(1):0,we=e=>{if(ie.value||le.value===e)return;le.value=e,de.value=!0,ce.value=e;const t=new Date;let a=new Date;"week"===e?a.setDate(t.getDate()-7):"month"===e?a.setMonth(t.getMonth()-1):"three_months"===e&&a.setMonth(t.getMonth()-3),"custom"!==e&&(re.value=[a,t]),Ce()},ye=e=>{let t="",a="";if(re.value&&2===re.value.length)t=Me(re.value[0]),a=Me(re.value[1]);else{const e=new Date;let n=new Date;"week"===le.value?n.setDate(e.getDate()-7):"month"===le.value?n.setMonth(e.getMonth()-1):"three_months"===le.value&&n.setMonth(e.getMonth()-3),t=Me(n),a=Me(e)}ne.push({path:"/DataDashboard/details",query:{type:e.title,platform:oe.value,timeRange:le.value,startDate:t,endDate:a,cardId:e.card_id,platformType:oe.value,kol_attributes:e.kol_attributes}})},De=()=>{Ce()},Ce=async()=>{try{ie.value=!0;const a={platform_type:oe.value,start_date:"",end_date:""};if("0"===oe.value&&delete a.platform_type,"custom"===le.value&&re.value&&2===re.value.length)a.start_date=Me(re.value[0]),a.end_date=Me(re.value[1]);else{const e=new Date;let t=new Date;"week"===le.value?t.setDate(e.getDate()-7):"month"===le.value?t.setMonth(e.getMonth()-1):"three_months"===le.value&&t.setMonth(e.getMonth()-3),a.start_date=Me(t),a.end_date=Me(e)}const n=await(t=a,e.get("/xingtu/total_order",t));990===n.code&&n.data?((e=>{e&&(me.customers.mainValue=e.total_company||0,me.orders.mainValue=e.total_orders||0,me.orders.breakdown.machine.value=e.mcn_orders||0,me.orders.breakdown.nonMachine.value=e.non_mcn_orders||0,me.orders.breakdown.machine.percentage=fe(e.mcn_orders,e.total_orders),me.orders.breakdown.nonMachine.percentage=fe(e.non_mcn_orders,e.total_orders),me.amount.mainValue=e.total_amount||0,me.amount.breakdown.machine.value=e.mcn_amount||0,me.amount.breakdown.nonMachine.value=e.non_mcn_amount||0,me.amount.breakdown.machine.percentage=fe(e.mcn_amount,e.total_amount),me.amount.breakdown.nonMachine.percentage=fe(e.non_mcn_amount,e.total_amount),me.posts.mainValue=e.total_publish||0,me.posts.breakdown.machine.value=e.mcn_publish||0,me.posts.breakdown.nonMachine.value=e.non_mcn_publish||0,me.posts.breakdown.machine.percentage=fe(e.mcn_publish,e.total_publish),me.posts.breakdown.nonMachine.percentage=fe(e.non_mcn_publish,e.total_publish),me.completed.mainValue=e.total_settlement||0,me.completed.breakdown.machine.value=e.mcn_settlement||0,me.completed.breakdown.nonMachine.value=e.non_mcn_settlement||0,me.completed.breakdown.machine.percentage=fe(e.mcn_settlement,e.total_settlement),me.completed.breakdown.nonMachine.percentage=fe(e.non_mcn_settlement,e.total_settlement),me.settlement_amount.mainValue=parseFloat(e.total_settlement_amount)||0,me.settlement_amount.breakdown.machine.value=parseFloat(e.mcn_settlement_amount)||0,me.settlement_amount.breakdown.nonMachine.value=parseFloat(e.non_mcn_settlement_amount)||0,me.settlement_amount.breakdown.machine.percentage=fe(e.mcn_settlement_amount,parseFloat(e.total_settlement_amount)),me.settlement_amount.breakdown.nonMachine.percentage=fe(e.non_mcn_settlement_amount,parseFloat(e.total_settlement_amount)),he.ordersDistribution=[{value:1===e.other_mcn_orders?100:0,name:"其他合作机构"}],he.amountDistribution=[{value:1===e.other_mcn_orders?100:0,name:"其他合作机构"}],e.top10_order_amounts&&e.top10_order_amounts.length>0?he.topAgencies=e.top10_order_amounts.map(((e,t)=>{const a=2===e.kol_attributes?"非机构达人":"机构达人";return{name:e.mcn_short_name,value:+e.actual_amount_price,originalId:e.id,agencyType:a}})):he.topAgencies=[])})(n.data),setTimeout((()=>{xe()}),100)):w.error(n.msg||"获取数据失败")}catch(a){w.error("获取数据失败："+(a.message||"未知错误"))}finally{ie.value=!1,ue.value=!1,de.value=!1,se.value="",ce.value=""}var t},Me=e=>{if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},xe=()=>{if(be.value)if(ge.ordersDistributionChart=y.init(be.value),0===he.ordersDistribution[0].value){const e={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#909399",fontSize:16,fontWeight:"normal"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",show:!1},yAxis:{type:"category",show:!1},series:[]};ge.ordersDistributionChart.setOption(e)}else{const e={tooltip:{trigger:"item",formatter:"{b}: {c}%"},legend:{orient:"horizontal",bottom:10,left:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"下单量分布",type:M.ordersDistribution.type,radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:16,fontWeight:"bold"}},labelLine:{show:!1},data:he.ordersDistribution,color:M.ordersDistribution.colors}]};ge.ordersDistributionChart.setOption(e)}if(pe.value)if(ge.amountDistributionChart=y.init(pe.value),0===he.amountDistribution[0].value){const e={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#909399",fontSize:16,fontWeight:"normal"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",show:!1},yAxis:{type:"category",show:!1},series:[]};ge.amountDistributionChart.setOption(e)}else{const e={tooltip:{trigger:"item",formatter:"{b}: {c}%"},legend:{orient:"horizontal",bottom:10,left:"center",itemWidth:10,itemHeight:10,textStyle:{fontSize:12}},series:[{name:"金额分布",type:M.amountDistribution.type,radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:16,fontWeight:"bold"}},labelLine:{show:!1},data:he.amountDistribution,color:M.amountDistribution.colors}]};ge.amountDistributionChart.setOption(e)}if(ve.value)if(ge.topAgenciesChart=y.init(ve.value),he.topAgencies.length>0){const e=(e,t)=>{const a=Math.max(.3,e/t);return new y.graphic.LinearGradient(0,0,1,0,[{offset:0,color:`rgba(64, 158, 255, ${.5*a})`},{offset:1,color:`rgba(64, 158, 255, ${a})`}])},t=Math.max(...he.topAgencies.map((e=>e.value))),a=e=>{if(!e)return"";return e.length>10?e.substring(0,10)+"...":e},n=he.topAgencies.map((e=>a(e.name))).reverse();he.topAgencies.map((e=>e.value)).reverse();const o={tooltip:{axisPointer:{type:"shadow"},formatter:function(e){return`${e.name}<br/>${e.seriesName}：${e.value.toLocaleString()}元`}},grid:{left:"3%",right:"4%",bottom:"15%",top:"3%",containLabel:!0},xAxis:{type:"value",boundaryGap:[0,.01],axisLabel:{formatter:"{value}",rotate:45,interval:0}},yAxis:{type:"category",data:n,axisLabel:{formatter:function(e){return e},margin:12,interval:0,textStyle:{fontSize:12,lineHeight:16}}},series:[{name:"下单金额",type:"bar",data:he.topAgencies.map(((a,n)=>({value:a.value,itemStyle:{color:e(a.value,t)},originalName:a.name}))).reverse()}]};ge.topAgenciesChart.setOption(o)}else{const e={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#909399",fontSize:16,fontWeight:"normal"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value",show:!1},yAxis:{type:"category",show:!1},series:[]};ge.topAgenciesChart.setOption(e)}},Ae=()=>{Object.values(ge).forEach((e=>{e&&e.resize()}))};return window.addEventListener("resize",Ae),o((()=>{we("week"),Ce()})),l((()=>[be.value,pe.value,ve.value]),(()=>Ae())),(e,t)=>{const a=r("el-button"),n=r("el-date-picker"),o=r("el-loading");return s(),i("div",A,[c("div",S,[c("div",V,[c("div",z,[t[4]||(t[4]=c("span",{class:"filter-icon"},[c("i",{class:"el-icon-monitor"}),u(" 平台筛选")],-1)),c("div",L,[(s(!0),i(d,null,m(g(x).platforms,(e=>(s(),h(a,{key:e.key,class:v({active:oe.value===e.key}),onClick:t=>(e=>{ie.value||oe.value===e||(oe.value=e,ue.value=!0,se.value=e,Ce())})(e.key),loading:se.value===e.key},{default:b((()=>[u(p(e.label),1)])),_:2},1032,["class","onClick","loading"])))),128))])]),c("div",F,[t[5]||(t[5]=c("span",{class:"filter-icon"},[c("i",{class:"el-icon-time"}),u(" 时间筛选")],-1)),c("div",O,[(s(!0),i(d,null,m(g(x).timeRanges,(e=>(s(),h(a,{key:e.key,class:v({active:le.value===e.key}),onClick:t=>we(e.key),loading:ce.value===e.key},{default:b((()=>[u(p(e.label),1)])),_:2},1032,["class","onClick","loading"])))),128)),"custom"===le.value?(s(),i("div",W,[k(n,{modelValue:re.value,"onUpdate:modelValue":t[0]||(t[0]=e=>re.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","disabled-date":_e,onChange:De,disabled:ie.value},null,8,["modelValue","disabled"])])):_("",!0)])])])]),c("div",$,[ie.value?(s(),i("div",B,[k(o),t[6]||(t[6]=c("div",{class:"loading-text"},"数据加载中...",-1))])):(s(),i(d,{key:1},[t[7]||(t[7]=c("h2",{class:"dashboard-title"},"核心数据指标",-1)),c("div",R,[(s(!0),i(d,null,m(me,((e,t)=>(s(),i("div",{key:t,class:v(["data-card",`${t}-card`]),onClick:e=>ye(g(C)[t]),title:"查看"+g(C)[t].title+"详情"},[c("div",{class:"card-header",style:f({backgroundColor:g(C)[t].headerColor})},p(g(C)[t].title),5),c("div",E,[c("div",H,["总客户/品牌数"===g(C)[t].title?(s(),i("div",j,p(ke(e.mainValue)),1)):(s(),i("span",I,p(ke(e.mainValue)),1)),g(C)[t].unit?(s(),i("span",G,p(g(C)[t].unit),1)):_("",!0)]),e.breakdown?(s(),i("div",N,[c("div",P,[c("div",{class:"item-bar machine-bar",style:f({backgroundColor:g(C)[t].breakdown.machine.color})},null,4),c("div",q,[c("i",{class:"el-icon-user machine-icon",style:f({color:g(C)[t].breakdown.machine.color})},null,4),u(" "+p(g(C)[t].breakdown.machine.label),1)]),c("div",U,p(ke(e.breakdown.machine.value)),1),c("div",Y,"("+p(e.breakdown.machine.percentage)+"%)",1)]),c("div",J,[c("div",{class:"item-bar non-machine-bar",style:f({backgroundColor:g(C)[t].breakdown.nonMachine.color})},null,4),c("div",K,[c("i",{class:"el-icon-user non-machine-icon",style:f({color:g(C)[t].breakdown.nonMachine.color})},null,4),u(" "+p(g(C)[t].breakdown.nonMachine.label),1)]),c("div",Q,p(ke(e.breakdown.nonMachine.value)),1),c("div",X,"("+p(e.breakdown.nonMachine.percentage)+"%)",1)])])):_("",!0)])],10,T)))),128))]),t[8]||(t[8]=c("h2",{class:"dashboard-title"},"机构达人合作数据分析",-1)),c("div",Z,[c("div",{class:"chart-container",onClick:t[1]||(t[1]=e=>ye(g(M).ordersDistribution))},[c("div",ee,p(g(M).ordersDistribution.title),1),c("div",{id:"ordersDistributionChart",ref_key:"ordersDistributionChart",ref:be,class:"chart"},null,512)]),c("div",{class:"chart-container",onClick:t[2]||(t[2]=e=>ye(g(M).amountDistribution))},[c("div",te,p(g(M).amountDistribution.title),1),c("div",{id:"amountDistributionChart",ref_key:"amountDistributionChart",ref:pe,class:"chart"},null,512)]),c("div",{class:"chart-container",onClick:t[3]||(t[3]=e=>ye(g(M).topAgencies))},[c("div",ae,p(g(M).topAgencies.title),1),c("div",{id:"topAgenciesChart",ref_key:"topAgenciesChart",ref:ve,class:"chart"},null,512)])])],64))])])}}},[["__scopeId","data-v-cbac6432"]]);export{ne as default};
