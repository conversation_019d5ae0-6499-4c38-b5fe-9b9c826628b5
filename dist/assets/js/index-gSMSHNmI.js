import{g as e,b as a,c as t,d as l,e as n}from"./getDate-rBIvyffV.js";import{_ as r}from"./_plugin-vue_export-helper-BXFjo1rG.js";import{r as o,a as d,c as u,o as s,b as c,w as i,d as p,E as v}from"./index-C2bfFjZ1.js";const _={class:"card content-box"},m=r({__name:"index",setup(r){const m=o(null),g=o({}),h=o([]),f=o([]),b=o([]),y=o([]);b.value=e(),y.value=e();const Y=({rowIndex:e,columnIndex:a})=>{if(3===a)return 0===e?{rowspan:h.value.length,colspan:1}:{rowspan:0,colspan:0}},D=({rowIndex:e,columnIndex:a})=>{if(5===a)return 0===e?{rowspan:f.value.length,colspan:1}:{rowspan:0,colspan:0}},w=o(!1),x=o({page:1,page_size:10,order_personnel:"",created_at:{start_time:b.value[0],end_time:b.value[1]}}),C=(e,a)=>{try{if(Object.keys(e).length>0)for(let a in K.value)K.value[a]="created_at"!=a?e[a]:{start_time:e.created_at[0],end_time:e.created_at[1]};else K.value={order_personnel:"",task_name:"",created_at:{start_time:b.value[0],end_time:b.value[1]},page:1,page_size:10}}catch(t){}0==j.value?B():U(),a()},k=(e,a)=>{try{if(Object.keys(e).length>0)for(let a in x.value)x.value[a]="created_at"!=a?e[a]:{start_time:e.created_at[0],end_time:e.created_at[1]};else x.value={page:1,page_size:10,order_personnel:"",created_at:{start_time:b.value[0],end_time:b.value[1]}}}catch(t){}0==j.value?B():U(),a()},M=o({total:0,currentPage:1,pageSize:10});function P(e){x.value.page=e,0==j.value?B():U()}function V(e){x.value.page=1,x.value.page_size=e,0==j.value?B():U()}let z=o([]);const I=o({headerAlign:"center",align:"center",gridBtn:!1,menu:!1,border:!0,searchEnter:!0,index:!0,searchSpan:8,rowKey:"id",addBtn:!1,searchMenuPosition:"right",dialogDrag:!0,rowParentKey:"parentId",column:[{label:"姓名",prop:"order_personnel",search:!0},{label:"下单任务数",prop:"order_sum",align:"center"},{label:"创建时间",prop:"created_at",type:"daterange",align:"center",search:!0,searchRange:!0,format:"YYYY-MM-DD",span:24,defaultTime:b.value,valueFormat:"YYYY-MM-DD",startPlaceholder:b.value[0],endPlaceholder:b.value[1]}]}),S=o({headerAlign:"center",align:"center",menu:!1,border:!0,index:!0,searchSpan:6,searchEnter:!0,rowKey:"id",addBtn:!1,searchMenuPosition:"right",dialogDrag:!0,rowParentKey:"parentId",column:[{label:"下单人员",prop:"order_personnel",search:!0},{label:"任务名称",prop:"task_name",search:!0,align:"center"},{label:"任务ID",prop:"demand_id",align:"center"},{label:"任务金额",prop:"payment_info",align:"center"},{label:"创建时间",prop:"created_at",type:"daterange",align:"center",search:!0,searchRange:!0,format:"YYYY-MM-DD",span:24,defaultTime:b.value,valueFormat:"YYYY-MM-DD",startPlaceholder:b.value[0],endPlaceholder:b.value[1]}]}),j=o("0"),E=(e,a)=>{j.value=e.index,0==e.index?B():U()},U=()=>{w.value=!0,n(K.value).then((e=>{990===e.code&&(e.data.list.forEach((e=>{K.value.created_at?e.created_at=[K.value.created_at.start_time,K.value.created_at.end_time]:e.created_at=b.value})),z.value=e.data.list,f.value=e.data.list,M.value.total=e.data.count),w.value=!1}))};let K=o({order_personnel:"",task_name:"",created_at:{start_time:b.value[0],end_time:b.value[1]},page:1,page_size:10});const B=()=>{w.value=!0,a(x.value).then((e=>{990===e.code&&(e.data.list.forEach((e=>{x.value.created_at?e.created_at=[x.value.created_at.start_time,x.value.created_at.end_time]:e.created_at=b.value})),z.value=e.data.list,h.value=e.data.list,M.value.total=e.data.count),w.value=!1}))};B();const T=()=>{l(K.value).then((e=>{990===e.code&&v({type:"success",message:"导出成功，请到下载记录中下载文件"})}))},A=()=>{t(K.value).then((e=>{990===e.code&&v({type:"success",message:"导出成功，请到下载记录中下载文件"})}))};return(e,a)=>{const t=d("el-button"),l=d("avue-crud"),n=d("el-tab-pane"),r=d("el-tabs");return s(),u("div",_,[c(r,{class:"demo-tabs",modelValue:j.value,"onUpdate:modelValue":a[4]||(a[4]=e=>j.value=e),onTabClick:E},{default:i((()=>[c(n,{name:"0",label:"下单人员统计"},{default:i((()=>[c(l,{onSearchChange:k,modelValue:g.value,"onUpdate:modelValue":a[0]||(a[0]=e=>g.value=e),"table-loading":w.value,option:I.value,data:h.value,ref_key:"crud",ref:m,page:M.value,"onUpdate:page":a[1]||(a[1]=e=>M.value=e),onCurrentChange:P,onSizeChange:V,"span-method":Y},{"menu-left":i((()=>[c(t,{type:"primary",icon:"Download",onClick:A},{default:i((()=>a[5]||(a[5]=[p("导出")]))),_:1})])),_:1},8,["modelValue","table-loading","option","data","page"])])),_:1}),c(n,{name:"1",label:"详情数据"},{default:i((()=>[c(l,{onSearchChange:C,modelValue:g.value,"onUpdate:modelValue":a[2]||(a[2]=e=>g.value=e),"table-loading":w.value,option:S.value,data:f.value,ref_key:"crud",ref:m,page:M.value,"onUpdate:page":a[3]||(a[3]=e=>M.value=e),onCurrentChange:P,onSizeChange:V,"span-method":D},{"menu-left":i((()=>[c(t,{type:"primary",icon:"Download",onClick:T},{default:i((()=>a[6]||(a[6]=[p("导出")]))),_:1})])),_:1},8,["modelValue","table-loading","option","data","page"])])),_:1})])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-0f889744"]]);export{m as default};
