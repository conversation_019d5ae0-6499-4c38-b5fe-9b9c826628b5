import{r as e,H as l,u as a,e as t,p as n,a as s,Q as i,m as d,c as o,o as c,h as u,n as r,b as v,t as p,w as m,d as y,g as k,y as _,F as f,i as b,v as x,C as g,E as h}from"./index-BE6Fh1xm.js";import{h as w,j as C,r as D,k as Y,l as z,m as j,n as I,t as V,o as M,s as S}from"./order-VDf-VPrU.js";import{u as O}from"./tabs-CGQc3OAd.js";import{h as U}from"./moment-f4tm6vQK.js";import{_ as q}from"./_plugin-vue_export-helper-GSmkUi5K.js";const J={style:{width:"100%"}},N={key:0},L={class:"my-task-detail-card"},P={class:"my-task-detail-card-flex"},E={style:{"font-weight":"bold"}},F={class:"my-task-detail-item-tag",style:{"background-color":"#e8f5ff",color:"#49a7ff"}},H={class:"tablerow",style:{display:"flex","align-items":"center"}},Q={class:"tablerow-content"},R={key:0,class:"flex tablerow"},W={class:"tablerow-content"},T={style:{display:"flex","align-items":"center",cursor:"pointer"},class:"tablerow-title ml-5"},A={key:1,class:"my-task-detail-card-btn"},B={style:{color:"#ff2e64"}},G={key:2,class:"my-task-detail-card-btn"},K={key:3,class:"my-task-detail-card-btn"},X={key:4,class:"my-task-detail-card-btn"},Z={key:5,class:"my-task-detail-card-btn"},$={style:{color:"#333","font-size":"14px"}},ee={style:{color:"#ff2e64"}},le={class:"my-task-detail-card-connten"},ae={class:"my-task-detail-card-connten-value"},te={class:"my-task-detail-card"},ne={class:"my-task-detail-card-connten"},se={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},ie={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},de={style:{display:"flex","flex-direction":"column"}},oe={style:{display:"flex","align-items":"center","justify-content":"center",width:"28px",height:"28px","border-radius":"50%",overflow:"hidden","margin-right":"5px"}},ce=["src"],ue=["onClick"],re={key:0},ve={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},pe={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},me={style:{display:"flex","flex-direction":"column"}},ye={style:{margin:"0"}},ke={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},_e={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},fe={style:{display:"flex","flex-direction":"column"}},be={style:{margin:"0"}},xe={class:"my-task-detail-card-connten"},ge={class:"my-task-detail-card-connten-content"},he={class:"my-task-detail-card-connten-content-item product-item"},we={class:"my-task-detail-card-connten-content"},Ce={class:"my-task-detail-card-connten-content-item product-item"},De={class:"my-task-detail-card-connten-content"},Ye={class:"my-task-detail-card-connten-content-item product-item"},ze={class:"my-task-detail-card-connten-content"},je={class:"my-task-detail-card-connten-content-item product-item"},Ie={class:"my-task-detail-card"},Ve={class:"my-task-detail-card-connten"},Me={class:"my-task-detail-card-connten-content"},Se={class:"my-task-detail-card-connten-content-item product-item"},Oe={class:"my-task-detail-card-connten-content"},Ue={class:"my-task-detail-card-connten-content-item product-item"},qe={class:"my-task-detail-card-connten-content"},Je={class:"my-task-detail-card-connten-content-item product-item"},Ne={class:"my-task-detail-card-connten-content"},Le={class:"my-task-detail-card-connten-content-item product-item"},Pe={class:"flex items-center cursor"},Ee={class:"my-task-detail-card"},Fe={class:"my-task-detail-card-connten"},He={class:"my-task-detail-card-connten-content"},Qe={class:"my-task-detail-card-connten-content-item product-item"},Re={class:"my-task-detail-card-connten-content"},We={class:"my-task-detail-card-connten-content-item product-item"},Te={class:"my-task-detail-card-connten-content"},Ae={class:"my-task-detail-card-connten-content-item product-item"},Be={style:{"max-width":"500px"}},Ge={class:"my-task-detail-card-connten-content"},Ke={class:"my-task-detail-card-connten-content-item product-item"},Xe={class:"my-task-detail-card-connten-content"},Ze={class:"my-task-detail-card-connten-content-item product-item"},$e={class:"my-task-detail-card-connten-content"},el={class:"my-task-detail-card-connten-content-item product-item"},ll={class:"my-task-detail-card-connten-content"},al={class:"my-task-detail-card-connten-content-item product-item"},tl={class:"my-task-detail-card-connten-content"},nl={class:"my-task-detail-card-connten-content-item product-item"},sl={class:"my-task-detail-card-connten-content"},il={class:"my-task-detail-card-connten-content-item product-item"},dl={class:"my-task-detail-card-connten-content-item-value"},ol={key:0},cl={key:0,class:"mb-standard"},ul={key:1,class:"mb-standard"},rl={key:2,class:"mb-standard"},vl={key:3,class:"mb-standard"},pl={key:4},ml={key:1},yl={class:"my-task-detail-card"},kl={class:"my-task-detail-card-connten"},_l={class:"my-task-detail-card-connten-content"},fl={class:"my-task-detail-card-connten-content-item product-item"},bl={class:"my-task-detail-card-connten-content"},xl={class:"my-task-detail-card-connten-content-item product-item"},gl={class:"my-task-detail-card",style:{"margin-bottom":"var(--standard-spacing)"}},hl={class:"my-task-detail-card-connten"},wl={key:0},Cl={class:"my-task-detail-card-connten-content"},Dl={class:"my-task-detail-card-connten-content-item product-item"},Yl={class:"my-task-detail-card-connten-content"},zl={class:"my-task-detail-card-connten-content-item product-item"},jl={class:"my-task-detail-card-connten-content-item-value"},Il={class:"mb-standard"},Vl={class:"mb-standard"},Ml={key:1},Sl={class:"my-task-detail-card-connten-content"},Ol={class:"my-task-detail-card-connten-content-item product-item"},Ul={class:"my-task-detail-card-connten-content"},ql={class:"my-task-detail-card-connten-content-item product-item"},Jl={class:"my-task-detail-card-connten-content-item-value"},Nl={class:"mb-standard"},Ll={class:"mb-standard"},Pl={key:2},El={class:"my-task-detail-card-connten-content"},Fl={class:"my-task-detail-card-connten-content-item product-item"},Hl={class:"my-task-detail-card-connten-content-item-value"},Ql={class:"mb-standard"},Rl=["onClick"],Wl={key:3},Tl={key:1},Al={class:"flex"},Bl={style:{flex:"3","padding-bottom":"30px"}},Gl={style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Kl={class:"my-task-detail-item"},Xl={style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Zl={class:"my-task-detail-item"},$l={key:0,class:"my-task-detail-item-tag loading"},ea={key:1,class:"my-task-detail-item-tag"},la={key:2,class:"my-task-detail-item-tag"},aa={key:0,class:"my-task-detail-item-tip"},ta={key:0,class:"my-task-detail-item"},na={class:"my-task-detail-item-connten-item"},sa={class:"my-task-detail-item-connten-item-title"},ia={style:{color:"#333","font-weight":"bold"},class:"ml-3"},da={class:"my-task-detail-item-connten-item-content"},oa={class:"content"},ca={class:"text"},ua=["onClick"],ra=["onClick"],va={key:0,class:"btn-group"},pa={style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},ma={class:"my-task-detail-item"},ya={key:0,class:"my-task-detail-item-tag loading"},ka={key:1,class:"my-task-detail-item-tag"},_a={key:0,class:"my-task-detail-item-tip"},fa={key:1,class:"my-task-detail-item-tip"},ba={key:0,class:"my-task-detail-item"},xa={class:"my-task-detail-item-connten-item"},ga={class:"my-task-detail-item-connten-item-title"},ha={key:0,style:{color:"#333","font-weight":"bold"},class:"ml-3"},wa={class:"my-task-detail-item-connten-item-content"},Ca={class:"content"},Da={class:"video"},Ya={class:"text"},za=["onClick"],ja={key:0,class:"btn-group"},Ia={style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Va={class:"my-task-detail-item"},Ma={key:0,class:"my-task-detail-item-tag"},Sa={key:0,class:"my-task-detail-item-tip"},Oa={key:1,class:"my-task-detail-item-tip"},Ua={key:2},qa={key:0,style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Ja={class:"my-task-detail-item"},Na={key:0,class:"my-task-detail-item-tag loading"},La={key:0,class:"my-task-detail-item-tip"},Pa={key:1},Ea={key:0},Fa={style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Ha={key:0,style:{"margin-top":"10px","border-radius":"5px",overflow:"hidden"}},Qa={style:{flex:"1"}},Ra={class:"my-task-detail-right"},Wa={style:{"font-size":"14px"}},Ta={style:{"font-weight":"bold","font-size":"13px",margin:"10px 0"}},Aa={class:"my-task-detail-right-text"},Ba={style:{"font-size":"18px","font-weight":"bold",color:"#fd316a"}},Ga={style:{"font-size":"18px","font-weight":"bold",color:"#fd316a"}},Ka={class:"text-blue-500 cursor-pointer flex align-center",style:{"font-size":"14px"}},Xa={style:{"font-size":"14px"}},Za={style:{"font-size":"14px"}},$a={style:{"font-size":"14px"}},et={style:{"font-size":"14px"}},lt={style:{display:"flex","justify-content":"center","margin-top":"20px","border-top":"1px solid #eee",padding:"10px 0 0 0"}},at={style:{"border-top":"1px solid #eee",padding:"10px 0","margin-top":"10px",display:"flex","justify-content":"center"}},tt={class:"overflow-auto"},nt={style:{padding:"0px 20px 20px 20px","border-bottom":"1px solid #eee",color:"#999","font-size":"14px"}},st={style:{display:"flex","margin-bottom":"10px"}},it={style:{flex:"1"}},dt={style:{flex:"3"}},ot={style:{display:"flex"}},ct={style:{flex:"3"}},ut={style:{padding:"20px","border-bottom":"1px solid #eee"}},rt={style:{"font-size":"12px",color:"#666"}},vt={style:{padding:"20px","border-bottom":"1px solid #eee"}},pt={class:"ml-1"},mt={style:{padding:"20px","border-bottom":"1px solid #eee"}},yt={style:{display:"flex","font-size":"14px","align-items":"center","background-color":"#fafbfc",padding:"20px"}},kt={style:{flex:"1",color:"#333"}},_t={class:"overflow-auto"},ft={style:{padding:"0px 20px 20px 20px","border-bottom":"1px solid #eee",color:"#999","font-size":"14px"}},bt={style:{display:"flex","margin-bottom":"10px"}},xt={style:{flex:"1"}},gt={style:{flex:"3","margin-left":"16px"}},ht={style:{display:"flex"}},wt={style:{flex:"3","margin-left":"16px"}},Ct={style:{padding:"20px","border-bottom":"1px solid #eee"}},Dt={class:"ml-1"},Yt={style:{padding:"20px","border-bottom":"1px solid #eee"}},zt={style:{display:"flex","margin-top":"5px","font-size":"14px","margin-bottom":"10px"}},jt={style:{color:"#999"}},It={style:{"font-size":"16px"}},Vt={style:{"font-weight":"bold"}},Mt={class:"dialog-footer"},St={style:{"font-size":"16px"}},Ot={style:{"font-weight":"bold"}},Ut={class:"dialog-footer"},qt={class:"dialog-footer"},Jt={class:"float-right"},Nt={class:"my-task-detail-card"},Lt={class:"my-task-detail-card-flex"},Pt={style:{"font-weight":"bold"}},Et={class:"my-task-detail-item-tag",style:{"background-color":"#e8f5ff",color:"#49a7ff"}},Ft={class:"flex tablerow"},Ht={class:"tablerow-content"},Qt={class:"my-task-detail-card-connten"},Rt={class:"my-task-detail-card-connten-value"},Wt={class:"my-task-detail-card"},Tt={class:"my-task-detail-card-connten"},At={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},Bt={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},Gt={style:{display:"flex","flex-direction":"column"}},Kt={style:{display:"flex","align-items":"center","justify-content":"center",width:"28px",height:"28px","border-radius":"50%",overflow:"hidden","margin-right":"5px"}},Xt=["src"],Zt=["onClick"],$t={key:0},en={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},ln={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},an={style:{display:"flex","flex-direction":"column"}},tn={style:{margin:"0"}},nn={class:"my-task-detail-card-connten-content",style:{"margin-top":"4px"}},sn={class:"my-task-detail-card-connten-content-item product-item",style:{"margin-bottom":"4px"}},dn={style:{display:"flex","flex-direction":"column"}},on={style:{margin:"0"}},cn={class:"my-task-detail-card-connten"},un={class:"my-task-detail-card-connten-content"},rn={class:"my-task-detail-card-connten-content-item product-item"},vn={class:"my-task-detail-card-connten-content"},pn={class:"my-task-detail-card-connten-content-item product-item"},mn={class:"my-task-detail-card-connten-content"},yn={class:"my-task-detail-card-connten-content-item product-item"},kn={class:"my-task-detail-card-connten-content"},_n={class:"my-task-detail-card-connten-content-item product-item"},fn={class:"my-task-detail-card"},bn={class:"my-task-detail-card-connten"},xn={class:"my-task-detail-card-connten-content"},gn={class:"my-task-detail-card-connten-content-item product-item"},hn={class:"my-task-detail-card-connten-content"},wn={class:"my-task-detail-card-connten-content-item product-item"},Cn={class:"my-task-detail-card-connten-content"},Dn={class:"my-task-detail-card-connten-content-item product-item"},Yn={class:"my-task-detail-card-connten-content"},zn={class:"my-task-detail-card-connten-content-item product-item"},jn={class:"flex items-center cursor"},In={class:"my-task-detail-card"},Vn={class:"my-task-detail-card-connten"},Mn={class:"my-task-detail-card-connten-content"},Sn={class:"my-task-detail-card-connten-content-item product-item"},On={class:"my-task-detail-card-connten-content"},Un={class:"my-task-detail-card-connten-content-item product-item"},qn={class:"my-task-detail-card-connten-content"},Jn={class:"my-task-detail-card-connten-content-item product-item"},Nn={style:{"max-width":"500px"}},Ln={class:"my-task-detail-card-connten-content"},Pn={class:"my-task-detail-card-connten-content-item product-item"},En={class:"my-task-detail-card-connten-content"},Fn={class:"my-task-detail-card-connten-content-item product-item"},Hn={class:"my-task-detail-card-connten-content"},Qn={class:"my-task-detail-card-connten-content-item product-item"},Rn={class:"my-task-detail-card-connten-content"},Wn={class:"my-task-detail-card-connten-content-item product-item"},Tn={class:"my-task-detail-card-connten-content"},An={class:"my-task-detail-card-connten-content-item product-item"},Bn={class:"my-task-detail-card-connten-content"},Gn={class:"my-task-detail-card-connten-content-item product-item"},Kn={class:"my-task-detail-card-connten-content-item-value"},Xn={key:0},Zn={key:0,class:"mb-standard"},$n={key:1,class:"mb-standard"},es={key:2,class:"mb-standard"},ls={key:3,class:"mb-standard"},as={key:4},ts={key:1},ns={class:"my-task-detail-card"},ss={class:"my-task-detail-card-connten"},is={class:"my-task-detail-card-connten-content"},ds={class:"my-task-detail-card-connten-content-item product-item"},os={class:"my-task-detail-card-connten-content"},cs={class:"my-task-detail-card-connten-content-item product-item"},us={class:"my-task-detail-card",style:{"margin-bottom":"var(--standard-spacing)"}},rs={class:"my-task-detail-card-connten"},vs={key:0},ps={class:"my-task-detail-card-connten-content"},ms={class:"my-task-detail-card-connten-content-item product-item"},ys={class:"my-task-detail-card-connten-content"},ks={class:"my-task-detail-card-connten-content-item product-item"},_s={class:"my-task-detail-card-connten-content-item-value"},fs={class:"mb-standard"},bs={class:"mb-standard"},xs={key:1},gs={class:"my-task-detail-card-connten-content"},hs={class:"my-task-detail-card-connten-content-item product-item"},ws={class:"my-task-detail-card-connten-content"},Cs={class:"my-task-detail-card-connten-content-item product-item"},Ds={class:"my-task-detail-card-connten-content-item-value"},Ys={class:"mb-standard"},zs={class:"mb-standard"},js={key:2},Is={class:"my-task-detail-card-connten-content"},Vs={class:"my-task-detail-card-connten-content-item product-item"},Ms={class:"my-task-detail-card-connten-content-item-value"},Ss={class:"mb-standard"},Os=["onClick"],Us={key:3},qs=q({__name:"taskDetail",setup(q){const qs=e([]),Js=l(),Ns=a(),Ls=O(),Ps=e(!0),Es=e({1:"落地页",2:"短视频小程序",3:"原生落地页",4:"直播落地页",5:"直播小程序",6:"直播下载组件",7:"直播引流组件",10:"抽奖"}),Fs=t((()=>{var e,l;try{const a="string"==typeof(null==(e=$s.value)?void 0:e.component_info)?JSON.parse($s.value.component_info):{},t="string"==typeof(null==(l=ti.value[0])?void 0:l.component_info)?JSON.parse(ti.value[0].component_info):{};return(null==a?void 0:a.ecom_cart)||(null==t?void 0:t.ecom_cart)||[]}catch(a){return[]}})),Hs=e({1:"电商",2:"汽车",3:"网服",4:"保险",5:"教育",6:"家装",7:"剪映锚点",8:"招商加盟组件",9:"旅游",10:"游戏行业组件",11:"通信行业组件",12:"房产行业组件",13:"西瓜行业组件",14:"轻颜行业组件",15:"醒图行业组件"}),Qs=e("0"),Rs=e("0"),Ws=e(0),Ts=e("0"),As=e("-1"),Bs=e("0"),Gs=e("0"),Ks=e(!1),Xs=e(!1),Zs=e(""),$s=e({}),ei=e({}),li=e([]),ai=e([]),ti=e([]),ni=e({}),si=e({}),ii=e({}),di=e({}),oi=e(!1),ci=e(!1);e({1:"新上传",2:"已驳回",3:"已确认",4:"非最新无意见",5:"已删除"});const ui=e({0:"审核不通过",99:"审核中",1:"审核通过",3:"作者删除",4:"跳过审核"}),ri=e({1:"抖音内容审核",7:"广告素材审核",8:"电商审核",15:"广告审核",16:"千川审核",17:"DOU+审核",22:"网服、游戏行业锚点相关性审核",23:"搜索组件相关性审核"}),vi=e({}),pi=e({}),mi=e({}),yi=e(),ki=e(!1),_i=e({1:"1-20S视频",2:"21-60S视频",71:"60S以上视频",100:"招募任务一口价"}),fi=e(!1),bi=e(!1),xi=e(!1),gi=e("");e(0),e({balance:0,precise_balance:0,total_pay:0,precise_total_pay:0,total_task_cost:0,precise_total_task_cost:0,total_platform_fee:0,precise_total_platform_fee:0,order_bill_list:0,total_remaining:0,precise_total_remaining:0});const hi=e=>{let l={},a=window.location.href.split("?")[1];if(a){let e=a.split("&");for(let a=0;a<e.length;a++){let t=e[a].split("="),n=decodeURIComponent(t[0]),s=decodeURIComponent(t[1]);l[n]=s}}return l};let wi=hi().task_id,Ci=hi().order_id,Di=hi().star_id,Yi=hi().id;const zi=()=>{ci.value=!0},ji=e(0),Ii=e=>{Ks.value=!1},Vi=e=>{Xs.value=!1},Mi=()=>{oi.value=!0},Si=()=>{fi.value=!0,ki.value=!0,Ji()},Oi=e=>{window.open(e,"_blank")};function Ui(e){let l=U.duration(e,"seconds"),a=null==Math?void 0:Math.floor(l.asMinutes()),t=(null==Math?void 0:Math.floor(l.asSeconds()))%60;return(null==a?void 0:a.toString().padStart(2,"0"))+":"+(null==t?void 0:t.toString().padStart(2,"0"))}const qi=e=>{const l=document.createElement("textarea");l.value=e,document.body.appendChild(l),l.select();try{document.execCommand("copy");h.success("复制成功")}catch(a){h.warning("该浏览器不支持自动复制")}document.body.removeChild(l)},Ji=()=>{let e={star_id:Di,order_id:Ci,campaign_id:$s.value.campaign_id};try{S(e).then((e=>{990==e.code&&(ji.value=e.data.precise_cost,Ps.value=!1),ki.value=!1})).catch((e=>{ki.value=!1}))}catch(l){ki.value=!1}},Ni=e=>{window.open(e,"_blank")},Li=()=>{let e={star_id:Di,order_id:Ci,reason_type:3};ki.value=!0,Y(e).then((e=>{fi.value=!1,Qs.value="1",ki.value=!1})).catch((e=>{ki.value=!1,fi.value=!1}))},Pi=()=>{let e={star_id:Di,order_id:Ci,campaign_id:$s.value.campaign_id};z(e).then((e=>{bi.value=!1,Rs.value="1"}))},Ei=(e,l)=>{D({star_id:Di,order_id:Ci,resource_type:e,resource_id:l,ad_sync_decision:""}).then((e=>{Ts.value="3"}))},Fi=()=>{},Hi=()=>{C({star_id:Di,task_id:wi,order_id:Ci,id:Yi}).then((e=>{var l,a;let t=e.data;$s.value=t.order_base_data[0],ti.value=t.order_kol_data,Zs.value=U.unix(null==(l=null==t?void 0:t.order_kol_data[0])?void 0:l.release_time).format("YYYY-MM-DD HH:mm:ss"),ei.value=JSON.parse((null==(a=null==t?void 0:t.order_base_data[0])?void 0:a.ad_sync_conf)||"{}"),li.value=t.order_component_data,ai.value=t.order_industry_component_data,ni.value=null==t?void 0:t.order_kol_befor_data[0],ii.value=null==t?void 0:t.order_script_data,vi.value=null==t?void 0:t.order_video_data,pi.value=null==t?void 0:t.order_video_data.filter((e=>1===e.audit_status))[0],Qi(t.order_kol_data[0])}))},Qi=e=>{Ri(e),si.value=e},Ri=e=>{switch(e.order_status){case-1:Qs.value="0";break;case 66:Qs.value="66";break;case-3:case 10:Qs.value="3",Gi();break;case 1:Qs.value="3",Rs.value="1";break;case 2:Qs.value="2";break;case 4:Qs.value="1";break;case 41:Qs.value="3",Rs.value="1",Ws.value=1,Ts.value="1";break;case 42:Qs.value="3",Rs.value="1",Ws.value=1,Ts.value="3";break;case 43:Qs.value="3",Rs.value="1",Ws.value=1,Ts.value="2",As.value="0";break;case 44:Qs.value="3",Rs.value="1",Ws.value=1,Ts.value="4",As.value="0";break;case 51:Qs.value="3",Rs.value="1",Ws.value=2,Ts.value="2",As.value="1";break;case 52:Qs.value="3",Rs.value="1",Ws.value=2,Ts.value="2",As.value="3";break;case 53:Qs.value="3",Rs.value="1",Ws.value=2,Ts.value="2",As.value="2";break;case 54:Qs.value="3",Rs.value="1",Ws.value=3,Ts.value="2",As.value="2",Bs.value="1";break;case 3:Qs.value="3",Ws.value=4,Rs.value="1",Ts.value="2",As.value="2",Bs.value="1",Gs.value="1"}},Wi=()=>{M({star_id:Di,order_id:Ci}).then((e=>{Rs.value="2",Gs.value="2"}))},Ti=()=>{bi.value=!0,Gi()},Ai=()=>{xi.value=!1},Bi=()=>{Ls.removeTabs(Ns.fullPath),Js.push({path:"/business/task"})},Gi=()=>{let e={star_id:Di,campaign_id:$s.value.campaign_id,order_id:Ci};I(e).then((e=>{}))};w().then((e=>{qs.value=e.data}));const Ki=()=>{const e={order_id:Ci,executor_id:yi.value.id,executor_name:yi.value.name};j(e).then((e=>{oi.value=!1,Hi()}))};return n((()=>{Hi()})),(e,l)=>{var a,t,n,h,w,C,D,Y,z,j,I,M,S,O,q,Js,Ns,Ls,hi,wi,Yi,Ji,Hi,Qi,Ri,Gi,Xi,Zi,$i,ed,ld,ad,td,nd,sd,id,dd,od,cd,ud,rd,vd,pd,md,yd,kd,_d,fd,bd,xd,gd,hd,wd,Cd,Dd,Yd,zd,jd,Id,Vd,Md,Sd,Od,Ud,qd,Jd,Nd,Ld,Pd,Ed,Fd,Hd,Qd,Rd;const Wd=s("QuestionFilled"),Td=s("el-icon"),Ad=s("el-tooltip"),Bd=s("el-button"),Gd=s("Edit"),Kd=s("CopyDocument"),Xd=s("el-step"),Zd=s("el-steps"),$d=s("VideoPlay"),eo=s("el-avatar"),lo=s("Close"),ao=s("SuccessFilled"),to=s("el-drawer"),no=s("el-dialog"),so=s("el-input"),io=s("el-option"),oo=s("el-select"),co=i("loading");return d((c(),o("div",J,[u("div",{class:"my-task-title"},[u("span",{class:"my-task-title-text cursor-pointer",onClick:Bi},"我的任务 "),l[20]||(l[20]=u("span",{class:"my-task-title-text-sub"},"/ 任务详情",-1))]),"0"===Rs.value?(c(),o("div",N,[u("div",L,[u("div",P,[u("div",null,[u("div",null,[l[21]||(l[21]=u("span",{class:"my-task-detail-item-tag mr-2",style:{"font-size":"14px","margin-left":"0"}},"抖音短视频",-1)),u("span",E,p(null==(a=$s.value)?void 0:a.task_name),1),u("span",F,p(1==(null==(t=$s.value)?void 0:t.task_type)?"指派":"无"),1),l[22]||(l[22]=u("span",{class:"my-task-detail-item-tag"},"按一口价结算",-1))]),u("div",H,[l[23]||(l[23]=u("p",{class:"tablerow-title"},"视频金额",-1)),u("span",Q,"¥ "+p(null==(w=(null==(h=null==(n=ti.value[0])?void 0:n.payment_info)?void 0:h.precise_total)||0)?void 0:w.toLocaleString()),1)]),2===(null==(C=$s.value)?void 0:C.pay_type)?(c(),o("div",R,[l[27]||(l[27]=u("span",{class:"tablerow-title"},"已付定金",-1)),u("span",W,"¥ "+p(null==(Y=+(null==(D=ti.value[0])?void 0:D.payment_info.precise_total_paid)||0)?void 0:Y.toLocaleString()),1),v(Ad,{class:"box-item",placement:"top",effect:"light"},{content:m((()=>l[24]||(l[24]=[y(" 达人接收任务后，您需在14天内支付尾款，若逾"),u("br",null,null,-1),y("期未支付，任务将取消，定金将作为任务金额发放"),u("br",null,null,-1),y("给达人。若订单取消，支付尾款后立即发起，"),u("br",null,null,-1),y("可免费取消")]))),default:m((()=>[u("div",T,[l[25]||(l[25]=u("span",null,null,-1)),l[26]||(l[26]=y("定金规则")),v(Td,null,{default:m((()=>[v(Wd)])),_:1})])])),_:1})])):r("",!0)]),u("div",null,["0"===Qs.value?(c(),k(Bd,{key:0,type:"primary",size:"default",onClick:Si},{default:m((()=>l[28]||(l[28]=[y("取消任务")]))),_:1})):r("",!0),"1"===Qs.value?(c(),o("div",A,[l[31]||(l[31]=u("p",null,"任务已取消",-1)),u("p",null,[l[29]||(l[29]=y(" 赔付金额")),u("span",B,"¥ "+p((null==(j=null==(z=ti.value[0])?void 0:z.payment_info)?void 0:j.precise_deduct_amount)||0),1),l[30]||(l[30]=y(" 赔付规则 "))]),l[32]||(l[32]=u("p",null,"取消原因：经协商一致取消任务",-1))])):r("",!0),"2"===Qs.value?(c(),o("div",G,l[33]||(l[33]=[u("p",null,"任务已关闭",-1)]))):r("",!0),"4"===Qs.value?(c(),o("div",K,l[34]||(l[34]=[u("p",null,"任务已完成",-1)]))):r("",!0),"66"===Qs.value?(c(),o("div",X,[v(Bd,{type:"primary",size:"default"},{default:m((()=>l[35]||(l[35]=[y("下单失败")]))),_:1})])):r("",!0),"3"===Qs.value?(c(),o("div",Z,[l[40]||(l[40]=u("div",{class:"mb-standard"},"达人已接收任务，请在2024-07-19 16:05前支付尾款",-1)),u("div",null,[v(Bd,{type:"primary",size:"default",onClick:Ti},{default:m((()=>l[36]||(l[36]=[y("支付尾款")]))),_:1}),v(Bd,{type:"default",size:"default",onClick:Si},{default:m((()=>l[37]||(l[37]=[y("取消任务")]))),_:1}),u("p",$,[l[38]||(l[38]=y(" 待付尾款")),u("span",ee,"¥"+p(null==(S=(+(null==(I=ti.value[0])?void 0:I.payment_info.total)||0)-(+(null==(M=ti.value[0])?void 0:M.payment_info.precise_total_paid)||0))?void 0:S.toLocaleString()),1)]),l[39]||(l[39]=u("p",{class:"mt-standard",style:{color:"#333","font-size":"12px"}},"逾期未支付，任务将取消，定金将不退还",-1))])])):r("",!0)])]),u("div",le,[l[41]||(l[41]=u("div",{class:"my-task-detail-card-connten-label"},[u("span",null,"任务ID"),u("span",null,"任务创建"),u("span",null,"服务类型"),u("span",null,"期望发布"),u("span",null,"星图IP活动")],-1)),u("div",ae,[u("span",null,p(_(Ci)||"0"),1),u("span",null,p((null==(O=$s.value)?void 0:O.created_at)||"1970-01-01"),1),u("span",null,p(_i.value[si.value.cooperation_type]),1),u("span",null,p(_(U)(null==(q=$s.value)?void 0:q.expiration_time).format("YYYY-MM-DD"))+" 至 "+p(_(U)(null==(Js=$s.value)?void 0:Js.expiration_time_end).format("YYYY-MM-DD")),1),u("span",null,p($s.value.star_ip_activity||"无"),1)])])]),u("div",te,[u("div",ne,[u("div",null,[l[45]||(l[45]=u("div",{class:"my-task-detail-card-connten-title"},"达人信息",-1)),u("div",se,[u("div",ie,[l[42]||(l[42]=u("span",{class:"product-label"},"达人昵称",-1)),u("div",de,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("div",oe,[u("img",{style:{height:"28px",width:"28px"},src:e.author_info.avatar_uri,alt:""},null,8,ce)]),u("span",{class:"text-blue-500 cursor-pointer",onClick:l=>Ni(e.homepage)},p(e.kol_name),9,ue),e.author_info.contact_phone?(c(),o("span",re,"（"+p(e.author_info.contact_phone)+"）",1)):r("",!0)])))),128))])])]),u("div",ve,[u("div",pe,[l[43]||(l[43]=u("span",{class:"product-label"},"任务返点",-1)),u("div",me,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("p",ye,p(e.this_rebate_ratio)+"%",1)])))),128))])])]),u("div",ke,[u("div",_e,[l[44]||(l[44]=u("span",{class:"product-label"},"历史返点",-1)),u("div",fe,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("p",be,p(e.rebate_ratio)+"%",1)])))),128))])])])])]),u("div",xe,[u("div",null,[l[50]||(l[50]=u("div",{class:"my-task-detail-card-connten-title"},"产品介绍",-1)),u("div",ge,[u("div",he,[l[46]||(l[46]=u("span",{class:"product-label"},"品牌名称",-1)),u("span",null,p(null==(Ns=$s.value)?void 0:Ns.xingtu_brand),1)])]),u("div",we,[u("div",Ce,[l[47]||(l[47]=u("span",{class:"product-label"},"所属行业",-1)),u("span",null,p(JSON.parse((null==(Ls=$s.value)?void 0:Ls.xingtu_industry)||"[]").join("、")),1)])]),u("div",De,[u("div",Ye,[l[48]||(l[48]=u("span",{class:"product-label"},"产品名称",-1)),u("span",null,p(null==(hi=$s.value)?void 0:hi.product_name),1)])]),u("div",ze,[u("div",je,[l[49]||(l[49]=u("span",{class:"product-label"},"产品介绍",-1)),u("span",null,p(null==(wi=$s.value)?void 0:wi.product_information),1)])])])])]),u("div",Ie,[u("div",Ve,[u("div",null,[l[55]||(l[55]=u("div",{class:"my-task-detail-card-connten-title"},"参与人员",-1)),u("div",Me,[u("div",Se,[l[51]||(l[51]=u("span",{class:"product-label"},"选号人员",-1)),u("span",null,p(null==(Yi=ni.value)?void 0:Yi.created_name),1)])]),u("div",Oe,[u("div",Ue,[l[52]||(l[52]=u("span",{class:"product-label"},"建联人员",-1)),u("span",null,p(null==(Ji=ni.value)?void 0:Ji.alliance_personnel),1)])]),u("div",qe,[u("div",Je,[l[53]||(l[53]=u("span",{class:"product-label"},"信息填写人员",-1)),u("span",null,p(null==(Hi=ti.value[0])?void 0:Hi.created_name),1)])]),u("div",Ne,[u("div",Le,[l[54]||(l[54]=u("span",{class:"product-label"},"执行人员",-1)),u("span",Pe,[y(p(null==(Qi=ti.value[0])?void 0:Qi.executor_name)+" ",1),v(Td,{class:"ml-2",onClick:Mi},{default:m((()=>[v(Gd)])),_:1})])])])])])]),u("div",Ee,[u("div",Fe,[u("div",null,[l[70]||(l[70]=u("div",{class:"my-task-detail-card-connten-title"},"任务要求",-1)),u("div",He,[u("div",Qe,[l[56]||(l[56]=u("span",{class:"product-label"},"期望发布时间",-1)),u("span",null,p(_(U)(null==(Ri=$s.value)?void 0:Ri.expiration_time).format("YYYY-MM-DD"))+" 至 "+p(_(U)(null==(Gi=$s.value)?void 0:Gi.expiration_time_end).format("YYYY-MM-DD")),1)])]),u("div",Re,[u("div",We,[l[57]||(l[57]=u("span",{class:"product-label"},"期望保留时间",-1)),u("span",null,p(null==(Xi=$s.value)?void 0:Xi.expect_remain_time)+"天",1)])]),u("div",Te,[u("div",Ae,[l[58]||(l[58]=u("span",{class:"product-label"},"详细要求",-1)),u("span",Be,p(null==(Zi=$s.value)?void 0:Zi.video_requirements),1)])]),u("div",Ge,[u("div",Ke,[l[59]||(l[59]=u("span",{class:"product-label"},"背景音乐",-1)),u("span",null,p((null==($i=$s.value)?void 0:$i.designated_music)||"无"),1)])]),u("div",Xe,[u("div",Ze,[l[60]||(l[60]=u("span",{class:"product-label"},"道具要求",-1)),u("span",null,p((null==(ed=$s.value)?void 0:ed.prop_requirements)||"无"),1)])]),u("div",$e,[u("div",el,[l[61]||(l[61]=u("span",{class:"product-label"},"场景要求",-1)),u("span",null,p((null==(ld=$s.value)?void 0:ld.scene_requirements)||"无"),1)])]),u("div",ll,[u("div",al,[l[62]||(l[62]=u("span",{class:"product-label"},"其他要求",-1)),u("span",null,p((null==(ad=$s.value)?void 0:ad.other_requirements)||"无"),1)])]),u("div",tl,[u("div",nl,[l[63]||(l[63]=u("span",{class:"product-label"},"是否跳过脚本",-1)),u("span",null,p(1===(null==(td=$s.value)?void 0:td.ignore_script)?"是":"否"),1)])]),u("div",sl,[u("div",il,[l[69]||(l[69]=u("span",{class:"product-label"},"投放广告",-1)),u("div",dl,[Object.keys(ei.value).length>0?(c(),o("div",ol,[(null==(sd=null==(nd=ei.value)?void 0:nd.ocean_engine)?void 0:sd.advertiser_id)?(c(),o("div",cl,[l[64]||(l[64]=u("p",null,"你可将视频投放至巨量引擎广告投放平台",-1)),u("p",null,"巨量引擎ID："+p((null==(dd=null==(id=ei.value)?void 0:id.ocean_engine)?void 0:dd.advertiser_id)||"无"),1)])):r("",!0),(null==(cd=null==(od=ei.value)?void 0:od.qianchuan)?void 0:cd.advertiser_id)?(c(),o("div",ul,[l[65]||(l[65]=u("p",null,"你可将视频投放至巨量千川平台(原视频投放 视频素材投放)",-1)),u("p",null,"巨量千川ID："+p((null==(rd=null==(ud=ei.value)?void 0:ud.qianchuan)?void 0:rd.advertiser_id)||"无"),1)])):r("",!0),(null==(pd=null==(vd=ei.value)?void 0:vd.content_marketing)?void 0:pd.advertiser_id)?(c(),o("div",rl,[l[66]||(l[66]=u("p",null,"你可将视频投放至内容热推",-1)),u("p",null,"巨量引擎ID："+p((null==(yd=null==(md=ei.value)?void 0:md.content_marketing)?void 0:yd.advertiser_id)||"无"),1)])):r("",!0),(null==(_d=null==(kd=ei.value)?void 0:kd.content_marketing)?void 0:_d.advertiser_id)?(c(),o("div",vl,[l[67]||(l[67]=u("p",null,"你可将视频投放至内容服务",-1)),u("p",null,"巨量引擎ID："+p((null==(bd=null==(fd=ei.value)?void 0:fd.content_marketing)?void 0:bd.advertiser_id)||"无"),1)])):r("",!0),(null==(gd=null==(xd=ei.value)?void 0:xd.dou_plus)?void 0:gd.advertiser_id)||(null==(wd=null==(hd=ei.value)?void 0:hd.dou_plus)?void 0:wd.dou_plus_uid)?(c(),o("div",pl,[l[68]||(l[68]=u("p",null,"你可对视频进行Dou+加热（帮上热门）",-1)),u("p",null,"Dou+广告主抖音UID："+p((null==(Dd=null==(Cd=ei.value)?void 0:Cd.dou_plus)?void 0:Dd.dou_plus_uid)||"无"),1),u("p",null,"Dou+广告主ID："+p((null==(zd=null==(Yd=ei.value)?void 0:Yd.dou_plus)?void 0:zd.advertiser_id)||"无"),1)])):r("",!0)])):(c(),o("div",ml,"无"))])])])])])]),u("div",yl,[u("div",kl,[u("div",null,[u("div",_l,[u("div",fl,[l[71]||(l[71]=u("span",{class:"product-label"},"附件",-1)),u("span",null,p((null==(jd=$s.value)?void 0:jd.attachment_text)||"无"),1)])]),u("div",bl,[u("div",xl,[l[72]||(l[72]=u("span",{class:"product-label"},"挂载搜索词",-1)),u("span",null,p((()=>{var e,l;try{const a="string"==typeof(null==(e=$s.value)?void 0:e.component_info)?JSON.parse($s.value.component_info):{},t="string"==typeof(null==(l=ti.value[0])?void 0:l.component_info)?JSON.parse(ti.value[0].component_info):{};return(null==a?void 0:a.link_component_ids)?null==a?void 0:a.search_word:(null==t?void 0:t.search_word)||"无"}catch(a){return"无"}})()),1)])])])])]),u("div",gl,[u("div",hl,[u("div",null,[l[80]||(l[80]=u("div",{class:"my-task-detail-card-connten-title"},"组件信息",-1)),(null==(Id=li.value)?void 0:Id.length)>0?(c(),o("div",wl,[(c(!0),o(f,null,b(li.value,(e=>(c(),o("div",null,[u("div",Cl,[u("div",Dl,[l[73]||(l[73]=u("span",{class:"product-label"},"组件类型",-1)),u("span",null,p(Es.value[null==e?void 0:e.link_type]),1)])]),u("div",Yl,[u("div",zl,[l[74]||(l[74]=u("span",{class:"product-label"},"组件信息",-1)),u("div",jl,[u("div",null,[u("p",Il,p(null==e?void 0:e.link_name),1),u("p",Vl,p(null==e?void 0:e.link_title),1),u("p",null,[y(" 组件ID："+p(null==e?void 0:e.component_id)+" ",1),v(Td,{class:"ml-1 cursor-pointer",onClick:l=>qi(null==e?void 0:e.component_id)},{default:m((()=>[v(Kd)])),_:2},1032,["onClick"])])])])])])])))),256))])):Object.keys(ai.value).length>0?(c(),o("div",Ml,[u("div",Sl,[u("div",Ol,[l[75]||(l[75]=u("span",{class:"product-label"},"组件类型",-1)),u("span",null,p(Hs.value[null==(Vd=ai.value)?void 0:Vd.anchor_type]),1)])]),u("div",Ul,[u("div",ql,[l[76]||(l[76]=u("span",{class:"product-label"},"组件信息",-1)),u("div",Jl,[u("div",null,[u("p",Nl,p(null==(Md=ai.value)?void 0:Md.anchor_name),1),u("p",Ll,p(null==(Sd=ai.value)?void 0:Sd.anchor_title),1),u("p",null,[y(" 组件ID："+p(null==(Od=ai.value)?void 0:Od.industry_anchor_id)+" ",1),v(Td,{class:"ml-1 cursor-pointer",onClick:l[0]||(l[0]=e=>{var l;return qi(null==(l=ai.value)?void 0:l.industry_anchor_id)})},{default:m((()=>[v(Kd)])),_:1})])])])])])])):Fs.value.length>0?(c(),o("div",Pl,[l[78]||(l[78]=u("div",{class:"my-task-detail-card-connten-content"},[u("div",{class:"my-task-detail-card-connten-content-item product-item"},[u("span",{class:"product-label"},"组件类型"),u("span",null,"购物车组件")])],-1)),u("div",El,[u("div",Fl,[l[77]||(l[77]=u("span",{class:"product-label"},"组件信息",-1)),u("div",Hl,[u("div",null,[(c(!0),o(f,null,b(Fs.value,((e,l)=>(c(),o("div",{key:l,class:"mb-standard"},[u("p",Ql,"商品"+p(l+1)+": "+p(e.title||"未命名商品"),1),u("p",{class:"text-link",onClick:l=>Oi(e.product_link)},p(e.product_link||"无链接"),9,Rl)])))),128))])])])])])):(c(),o("div",Wl,l[79]||(l[79]=[u("p",{style:{color:"#999"}},"无",-1)])))])])])])):r("",!0),"1"==Rs.value||"2"==Rs.value?(c(),o("div",Tl,[u("div",Al,[u("div",Bl,[u("div",Gl,[u("div",Kl,[v(Zd,{active:Ws.value,"finish-status":"success"},{default:m((()=>[v(Xd,{title:"制作脚本"}),v(Xd,{title:"制作视频"}),v(Xd,{title:"发布视频"}),v(Xd,{title:"任务完成"})])),_:1},8,["active"])])]),u("div",Xl,[u("div",Zl,[u("div",null,[l[81]||(l[81]=u("span",{class:"my-task-detail-item-title"},"制作脚本",-1)),"0"===Ts.value||"1"===Ts.value||"3"===Ts.value?(c(),o("span",$l,"进行中")):r("",!0),"2"===Ts.value?(c(),o("span",ea,"已完成")):r("",!0),"4"===Ts.value||1===(null==(Ud=$s.value)?void 0:Ud.ignore_script)?(c(),o("span",la,"已跳过")):r("",!0)]),"0"===Ts.value?(c(),o("span",aa,"等待达人上传脚本")):r("",!0)]),"1"===Ts.value||"2"===Ts.value||"3"===Ts.value?d((c(),o("div",ta,[(c(!0),o(f,null,b(ii.value,(e=>(c(),o("div",{key:e.resource_id,class:"my-task-detail-item-connten"},[u("div",na,[u("div",sa,[u("span",null,"版本0"+p(e.id),1),u("span",ia,p(ui.value[e.audit_status]),1)]),u("div",da,[u("div",oa,[l[82]||(l[82]=u("div",{class:"img"},"W",-1)),u("div",ca,[u("p",null,p(e.resource_filename),1),u("p",null,p(e.created_at),1),u("p",null,[u("span",{class:"text-blue-500 cursor-pointer",onClick:l=>Oi(e.resource_url)},"下载文件",8,ua),u("span",{class:"text-blue-500 cursor-pointer ml-2",onClick:l=>{return a=e,di.value=a,void(Ks.value=!0);var a}},"查看详情",8,ra)])])]),99==e.audit_status&&3!==e.status?(c(),o("div",va,[v(Bd,{type:"default",onClick:l=>Ei(2,e.resource_id)},{default:m((()=>l[83]||(l[83]=[y("驳回脚本")]))),_:2},1032,["onClick"]),v(Bd,{type:"primary",onClick:l=>{return a=2,t=e.resource_id,void V({star_id:Di,order_id:Ci,resource_type:a,resource_id:t,ad_sync_decision:""}).then((e=>{Ts.value="2"}));var a,t}},{default:m((()=>l[84]||(l[84]=[y("确认脚本")]))),_:2},1032,["onClick"])])):r("",!0)])])])))),128))],512)),[[x,1!==(null==(qd=$s.value)?void 0:qd.ignore_script)]]):r("",!0)]),u("div",pa,[u("div",ma,[u("div",null,[l[85]||(l[85]=u("span",{class:"my-task-detail-item-title"},"制作视频",-1)),"0"===As.value||"1"===As.value||"3"===As.value?(c(),o("span",ya,"进行中")):r("",!0),"2"===As.value?(c(),o("span",ka,"已完成")):r("",!0)]),"-1"===As.value?(c(),o("span",_a,"未开始")):r("",!0),"0"===As.value?(c(),o("span",fa,"等待达人上传视频")):r("",!0)]),"1"===As.value||"2"===As.value||"3"===As.value?(c(),o("div",ba,[(c(!0),o(f,null,b(vi.value,(e=>(c(),o("div",{class:"my-task-detail-item-connten",key:e.resource_id},[u("div",xa,[u("div",ga,[u("span",null,"版本0"+p(e.id),1),ui.value[e.audit_status]?(c(),o("span",ha,"视频已确认")):r("",!0)]),u("div",wa,[u("div",Ca,[u("div",Da,[v(Td,{size:"25"},{default:m((()=>[v($d)])),_:1})]),u("div",Ya,[u("p",null,p(e.created_at),1),u("p",null,[u("span",{class:"text-blue-500 cursor-pointer",onClick:l=>{return a=e,mi.value=a,void(Xs.value=!0);var a}},"查看详情",8,za)])])]),"1"===As.value?(c(),o("div",ja,[v(Bd,{type:"default",onClick:l[1]||(l[1]=e=>Ei(3))},{default:m((()=>l[86]||(l[86]=[y("驳回视频")]))),_:1}),v(Bd,{type:"primary"},{default:m((()=>l[87]||(l[87]=[y("确认视频")]))),_:1})])):r("",!0)])])])))),128))])):r("",!0)]),u("div",Ia,[u("div",Va,[u("div",null,[l[88]||(l[88]=u("span",{class:"my-task-detail-item-title"},"发布视频",-1)),"1"===Bs.value?(c(),o("span",Ma,"已完成")):r("",!0)]),"0"===Bs.value?(c(),o("span",Sa,"未开始")):r("",!0),"1"===Bs.value?(c(),o("span",Oa," 视频已发布 "+p(Zs.value),1)):r("",!0),"1"===Bs.value?(c(),o("p",Ua,[u("span",{class:"text-blue-500 cursor-pointer",onClick:l[2]||(l[2]=e=>Oi("https://www.douyin.com/video/"+pi.value.item_id))},"查看视频")])):r("",!0)])]),1==Gs.value?(c(),o("div",qa,[u("div",Ja,[u("div",null,[l[89]||(l[89]=u("span",{class:"my-task-detail-item-title"},"任务完成",-1)),"1"===Gs.value?(c(),o("span",Na,"进行中")):r("",!0)]),"0"===Gs.value?(c(),o("span",La,"未开始")):r("",!0),"1"===Gs.value?(c(),o("div",Pa,[l[93]||(l[93]=u("span",{class:"my-task-detail-item-tip"},"视频已发布，请尽快验收",-1)),"1"===Gs.value?(c(),o("div",Ea,[l[92]||(l[92]=u("p",{class:"text-blue-500 cursor-pointer"},"营销数据",-1)),v(Bd,{type:"primary",onClick:Wi},{default:m((()=>l[90]||(l[90]=[y(" 确认验收 ")]))),_:1}),v(Bd,{type:"primary",onClick:Fi},{default:m((()=>l[91]||(l[91]=[y(" 推送到广告平台投放 ")]))),_:1})])):r("",!0)])):r("",!0)])])):r("",!0),u("div",Fa,[l[95]||(l[95]=g('<div class="my-task-detail-item" data-v-55252eab><div data-v-55252eab><span class="my-task-detail-item-title" data-v-55252eab>素材费用</span></div><div style="margin-top:10px;" data-v-55252eab><span style="color:#333;margin-right:10px;" data-v-55252eab>广告消耗</span><span style="color:#ff2e64;" data-v-55252eab>¥0</span><span style="color:#333;margin:0 10px 0 40px;" data-v-55252eab>素材费用</span><span style="color:#ff2e64;" data-v-55252eab>免费</span></div></div>',1)),2==Rs.value?(c(),o("div",Ha,l[94]||(l[94]=[g('<div class="my-task-detail-item" data-v-55252eab><div data-v-55252eab><span class="my-task-detail-item-title" data-v-55252eab>任务完成</span><span class="my-task-detail-item-tag" data-v-55252eab>已完成</span></div><p style="color:#999;font-size:12px;" data-v-55252eab>视频已验收</p><p class="cursor-pointer text-blue-500" data-v-55252eab>营销数据</p></div>',1)]))):r("",!0)])]),u("div",Qa,[u("div",Ra,[l[98]||(l[98]=u("div",{class:"my-task-detail-right-text"},"所属项目",-1)),u("div",Wa,p(null==(Jd=$s.value)?void 0:Jd.project_name),1),l[99]||(l[99]=u("div",null,[u("span",{class:"my-task-detail-item-tag mr-2",style:{"font-size":"14px","margin-left":"0"}},"抖音短视频")],-1)),l[100]||(l[100]=u("div",null,[u("span",{class:"my-task-detail-item-tag",style:{"background-color":"#e8f5ff",color:"#49a7ff"}},"指派"),u("span",{class:"my-task-detail-item-tag"},"按一口价结算")],-1)),u("div",Ta,p(null==(Nd=$s.value)?void 0:Nd.task_name),1),u("div",Aa,"任务ID："+p(_(Ci)),1),l[101]||(l[101]=u("div",{class:"my-task-detail-right-text",style:{"margin-top":"18px"}},"视频金额",-1)),u("div",Ba," ¥"+p((null==(Pd=null==(Ld=ti.value[0])?void 0:Ld.payment_info)?void 0:Pd.precise_total)||0),1),l[102]||(l[102]=u("div",{class:"my-task-detail-right-text",style:{"margin-top":"18px"}},"平台服务费",-1)),u("div",Ga,"¥"+p((null==(Fd=null==(Ed=ti.value[0])?void 0:Ed.payment_info)?void 0:Fd.precise_platform_fee)||0),1),l[103]||(l[103]=u("div",{class:"my-task-detail-right-text mt-5"},"结算方式",-1)),l[104]||(l[104]=u("div",{style:{"font-size":"14px",color:"#333"}},"按一口价结算",-1)),l[105]||(l[105]=u("div",{class:"my-task-detail-right-text mt-5"},"达人信息",-1)),u("div",Ka,[v(eo,{class:"mr-2",shape:"circle",size:"small",src:si.value.author_info.avatar_uri},null,8,["src"]),y(" "+p(si.value.author_info.author_name),1)]),l[106]||(l[106]=u("div",{class:"my-task-detail-right-text mt-5"},"期望发布时间",-1)),u("div",Xa,p(_(U)(null==(Hd=$s.value)?void 0:Hd.expiration_time).format("YYYY-MM-DD"))+" 至 "+p(_(U)(null==(Qd=$s.value)?void 0:Qd.expiration_time_end).format("YYYY-MM-DD")),1),l[107]||(l[107]=u("div",{class:"my-task-detail-right-text mt-5"},"期望保留时间",-1)),u("div",Za,p(null==(Rd=$s.value)?void 0:Rd.expect_remain_time)+" 天",1),l[108]||(l[108]=u("div",{class:"my-task-detail-right-text mt-5"},"服务类型",-1)),u("div",$a,p(_i.value[si.value.cooperation_type]),1),l[109]||(l[109]=u("div",{class:"my-task-detail-right-text mt-5"},"活动IP信息",-1)),u("div",et,p("ID:"+($s.value.star_ip_activity||"无")),1),u("div",lt,[v(Bd,{type:"text",onClick:zi},{default:m((()=>l[96]||(l[96]=[y(" 详细任务要求 ")]))),_:1})]),u("div",at,[[54,3].includes(ti.value[0].order_status)?r("",!0):(c(),k(Bd,{key:0,type:"primary",onClick:Si},{default:m((()=>l[97]||(l[97]=[y(" 取消任务 ")]))),_:1}))])])])])])):r("",!0),u("div",null,[v(to,{title:"查看脚本",modelValue:Ks.value,"onUpdate:modelValue":l[4]||(l[4]=e=>Ks.value=e),size:"800px"},{default:m((()=>{var e;return[u("button",{class:"el-drawer__close-btn",style:{display:"flex","justify-content":"center","align-items":"center",color:"aliceblue"},onClick:Ii,type:"button"},[v(Td,{class:"el-drawer__close"},{default:m((()=>[v(lo)])),_:1})]),u("div",tt,[u("div",nt,[u("div",st,[u("p",it,"版本0"+p(di.value.id),1),u("span",dt,p(di.value.created_at),1)]),u("div",ot,[l[110]||(l[110]=u("p",{style:{flex:"1"}},"ID",-1)),u("span",ct,p(null==(e=di.value.resource_id)?void 0:e.toString()),1)])]),u("div",ut,[l[111]||(l[111]=u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",{style:{color:"#333"}},"审核结果")],-1)),u("p",rt,p(ui.value[di.value.audit_status]),1)]),d(u("div",vt,[l[112]||(l[112]=u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"内容审核")],-1)),(c(!0),o(f,null,b(di.value.detail_audit_info,(e=>d((c(),o("div",{class:"video_info_item",key:e.audit_source},[u("p",null,[1==e.audit_status?(c(),k(Td,{key:0,style:{color:"#35d78a"}},{default:m((()=>[v(ao)])),_:1})):(c(),k(Td,{key:1},{default:m((()=>[v(ao)])),_:1})),u("span",pt,p(ri.value[e.audit_source]),1)]),u("p",null,p(ri.value[e.audit_source])+p(ui.value[e.audit_status]),1)])),[[x,ri.value[e.audit_source]]]))),128))],512),[[x,di.value.detail_audit_info.length]]),l[115]||(l[115]=u("div",{style:{padding:"20px","border-bottom":"1px solid #eee"}},[u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"反馈意见")]),u("p",{style:{"font-size":"12px",color:"#666"}},"您暂未反馈意见")],-1)),u("div",mt,[l[114]||(l[114]=u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"脚本文件")],-1)),u("div",yt,[l[113]||(l[113]=u("div",{style:{width:"60px",height:"60px","background-color":"#4faafe","border-radius":"5px","margin-right":"10px",color:"#fff","font-size":"20px",display:"flex","align-items":"center","justify-content":"center"}},[u("span",null," W ")],-1)),u("div",kt,[u("p",null,p(di.value.resource_filename),1),u("p",{class:"text-blue-500 cursor-pointer",onClick:l[3]||(l[3]=e=>Oi(di.value.resource_url))},"下载文件")])])])])]})),_:1},8,["modelValue"]),v(to,{title:"查看视频",modelValue:Xs.value,"onUpdate:modelValue":l[6]||(l[6]=e=>Xs.value=e),size:"800px"},{default:m((()=>{var e;return[u("button",{class:"el-drawer__close-btn",style:{display:"flex","justify-content":"center","align-items":"center",color:"aliceblue"},onClick:Vi,type:"button"},[v(Td,{class:"el-drawer__close"},{default:m((()=>[v(lo)])),_:1})]),u("div",_t,[u("div",ft,[u("div",bt,[u("p",xt,"版本0"+p(mi.value.id),1),u("span",gt,p(mi.value.created_at),1)]),u("div",ht,[l[116]||(l[116]=u("p",{style:{flex:"1"}},"ID",-1)),u("span",wt,p(null==(e=mi.value.resource_id)?void 0:e.toString()),1)])]),d(u("div",Ct,[l[117]||(l[117]=u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"内容审核")],-1)),(c(!0),o(f,null,b(mi.value.detail_audit_info,(e=>d((c(),o("div",{class:"video_info_item",key:e.audit_source},[u("p",null,[ri.value[e.audit_source]?(c(),k(Td,{key:0,style:{color:"#35d78a"}},{default:m((()=>[v(ao)])),_:1})):(c(),k(Td,{key:1},{default:m((()=>[v(ao)])),_:1})),u("span",Dt,p(ri.value[e.audit_source]),1)]),u("p",null,p(ri.value[e.audit_source])+p(ui.value[e.audit_status]),1)])),[[x,1==e.audit_status]]))),128))],512),[[x,mi.value.detail_audit_info.length]]),l[119]||(l[119]=u("div",{style:{padding:"20px","border-bottom":"1px solid #eee"}},[u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"反馈意见")]),u("p",{style:{"font-size":"12px",color:"#666"}},"您暂未反馈意见")],-1)),u("div",Yt,[l[118]||(l[118]=u("div",{style:{display:"flex","justify-content":"space-between","margin-bottom":"10px","font-size":"14px"}},[u("p",null,"视频内容")],-1)),u("div",zt,[u("p",jt,"视频时长 "+p(Ui(mi.value.duration)),1),u("p",{class:"text-blue-500 cursor-pointer ml-5",onClick:l[5]||(l[5]=e=>Oi("https://www.douyin.com/video/"+pi.value.item_id))}," 全屏查看 ")])])])]})),_:1},8,["modelValue"]),v(no,{modelValue:fi.value,"onUpdate:modelValue":l[8]||(l[8]=e=>fi.value=e),title:"任务取消",width:"400px","before-close":e.handleClose},{footer:m((()=>[u("div",Mt,[v(Bd,{onClick:l[7]||(l[7]=e=>fi.value=!1)},{default:m((()=>l[122]||(l[122]=[y("取消")]))),_:1}),v(Bd,{type:"primary",onClick:Li,disabled:Ps.value},{default:m((()=>l[123]||(l[123]=[y(" 确认取消 ")]))),_:1},8,["disabled"])])])),default:m((()=>{var e;return[d((c(),o("span",It,[l[120]||(l[120]=y("取消赔付金额： ")),u("span",Vt,"¥ "+p(null==(e=ji.value)?void 0:e.toLocaleString()),1),l[121]||(l[121]=y("元"))])),[[co,ki.value]])]})),_:1},8,["modelValue","before-close"]),v(no,{modelValue:bi.value,"onUpdate:modelValue":l[10]||(l[10]=e=>bi.value=e),title:"支付尾款",width:"400px","before-close":e.handleClose},{footer:m((()=>[u("div",Ut,[v(Bd,{onClick:l[9]||(l[9]=e=>bi.value=!1)},{default:m((()=>l[125]||(l[125]=[y("取消")]))),_:1}),v(Bd,{type:"primary",onClick:Pi},{default:m((()=>l[126]||(l[126]=[y(" 确认支付 ")]))),_:1})])])),default:m((()=>{var e,a,t;return[u("span",St,[l[124]||(l[124]=y("支付金额： ")),u("span",Ot,"¥ "+p(null==(t=(+(null==(e=ti.value[0])?void 0:e.payment_info.total)||0)-(+(null==(a=ti.value[0])?void 0:a.payment_info.precise_total_paid)||0))?void 0:t.toLocaleString()),1)])]})),_:1},8,["modelValue","before-close"]),v(no,{modelValue:xi.value,"onUpdate:modelValue":l[13]||(l[13]=e=>xi.value=e),title:"修改商品链接",width:"400px","before-close":e.handleClose},{footer:m((()=>[u("div",qt,[v(Bd,{onClick:l[12]||(l[12]=e=>xi.value=!1)},{default:m((()=>l[127]||(l[127]=[y("取消")]))),_:1}),v(Bd,{type:"primary",onClick:Ai},{default:m((()=>l[128]||(l[128]=[y(" 确定 ")]))),_:1})])])),default:m((()=>[v(so,{modelValue:gi.value,"onUpdate:modelValue":l[11]||(l[11]=e=>gi.value=e),placeholder:"请输入商品链接"},null,8,["modelValue"])])),_:1},8,["modelValue","before-close"])]),v(no,{modelValue:oi.value,"onUpdate:modelValue":l[16]||(l[16]=e=>oi.value=e),title:"修改执行人"},{default:m((()=>[l[130]||(l[130]=y(" 执行人： ")),v(oo,{style:{width:"200px"},modelValue:yi.value,"onUpdate:modelValue":l[14]||(l[14]=e=>yi.value=e),filterable:"",onChange:l[15]||(l[15]=l=>e.roleChange(l)),"value-key":"id",placeholder:"请选择执行人员"},{default:m((()=>[(c(!0),o(f,null,b(qs.value,(e=>(c(),k(io,{key:e.id,label:e.name,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),u("div",Jt,[v(Bd,{type:"primary",class:"ml-2",onClick:Ki},{default:m((()=>l[129]||(l[129]=[y("提交")]))),_:1})])])),_:1},8,["modelValue"]),v(to,{title:"任务要求",modelValue:ci.value,"onUpdate:modelValue":l[19]||(l[19]=e=>ci.value=e),size:"800px"},{default:m((()=>{var e,a,t,n,s,i,d,k,x,g,h,w,C,D,Y,z,j,I,V,M,S,O,q,J,N,L,P,E,F,H,Q,R,W,T,A,B,G,K,X,Z,$,ee,le,ae,te,ne,se,ie,de,oe,ce,ue,re,ve,pe;return[u("button",{class:"el-drawer__close-btn",style:{display:"flex","justify-content":"center","align-items":"center",color:"aliceblue"},onClick:l[17]||(l[17]=e=>ci.value=!1),type:"button"},[v(Td,{class:"el-drawer__close"},{default:m((()=>[v(lo)])),_:1})]),u("div",null,[u("div",Nt,[u("div",Lt,[u("div",null,[u("div",null,[l[131]||(l[131]=u("span",{class:"my-task-detail-item-tag mr-2",style:{"font-size":"14px","margin-left":"0"}},"抖音短视频",-1)),u("span",Pt,p(null==(e=$s.value)?void 0:e.task_name),1),u("span",Et,p(1==(null==(a=$s.value)?void 0:a.task_type)?"指派":"无"),1),l[132]||(l[132]=u("span",{class:"my-task-detail-item-tag"},"按一口价结算",-1))]),u("div",Ft,[l[133]||(l[133]=u("p",{class:"tablerow-title"},"视频金额",-1)),u("span",Ht,"¥ "+p(null==(s=(null==(n=null==(t=ti.value[0])?void 0:t.payment_info)?void 0:n.precise_total)||0)?void 0:s.toLocaleString()),1)])])]),u("div",Qt,[l[134]||(l[134]=u("div",{class:"my-task-detail-card-connten-label"},[u("span",null,"任务ID"),u("span",null,"任务创建"),u("span",null,"服务类型"),u("span",null,"期望发布"),u("span",null,"星图IP活动")],-1)),u("div",Rt,[u("span",null,p(_(Ci)||"0"),1),u("span",null,p((null==(i=$s.value)?void 0:i.created_at)||"1970-01-01"),1),u("span",null,p(_i.value[si.value.cooperation_type]),1),u("span",null,p(_(U)(null==(d=$s.value)?void 0:d.expiration_time).format("YYYY-MM-DD"))+" 至 "+p(_(U)(null==(k=$s.value)?void 0:k.expiration_time_end).format("YYYY-MM-DD")),1),u("span",null,p($s.value.star_ip_activity||"无"),1)])])]),u("div",Wt,[u("div",Tt,[u("div",null,[l[138]||(l[138]=u("div",{class:"my-task-detail-card-connten-title"},"达人信息",-1)),u("div",At,[u("div",Bt,[l[135]||(l[135]=u("span",{class:"product-label"},"达人昵称",-1)),u("div",Gt,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("div",Kt,[u("img",{style:{height:"28px",width:"28px"},src:e.author_info.avatar_uri,alt:""},null,8,Xt)]),u("span",{class:"text-blue-500 cursor-pointer",onClick:l=>Ni(e.homepage)},p(e.kol_name),9,Zt),e.author_info.contact_phone?(c(),o("span",$t,"（"+p(e.author_info.contact_phone)+"）",1)):r("",!0)])))),128))])])]),u("div",en,[u("div",ln,[l[136]||(l[136]=u("span",{class:"product-label"},"任务返点",-1)),u("div",an,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("p",tn,p(e.this_rebate_ratio)+"%",1)])))),128))])])]),u("div",nn,[u("div",sn,[l[137]||(l[137]=u("span",{class:"product-label"},"历史返点",-1)),u("div",dn,[(c(!0),o(f,null,b(ti.value,(e=>(c(),o("div",{style:{display:"flex","align-items":"center","margin-bottom":"4px"},key:e.id},[u("p",on,p(e.rebate_ratio)+"%",1)])))),128))])])])])]),u("div",cn,[u("div",null,[l[143]||(l[143]=u("div",{class:"my-task-detail-card-connten-title"},"产品介绍",-1)),u("div",un,[u("div",rn,[l[139]||(l[139]=u("span",{class:"product-label"},"品牌名称",-1)),u("span",null,p(null==(x=$s.value)?void 0:x.xingtu_brand),1)])]),u("div",vn,[u("div",pn,[l[140]||(l[140]=u("span",{class:"product-label"},"所属行业",-1)),u("span",null,p(JSON.parse((null==(g=$s.value)?void 0:g.xingtu_industry)||"[]").join("、")),1)])]),u("div",mn,[u("div",yn,[l[141]||(l[141]=u("span",{class:"product-label"},"产品名称",-1)),u("span",null,p(null==(h=$s.value)?void 0:h.product_name),1)])]),u("div",kn,[u("div",_n,[l[142]||(l[142]=u("span",{class:"product-label"},"产品介绍",-1)),u("span",null,p(null==(w=$s.value)?void 0:w.product_information),1)])])])])]),u("div",fn,[u("div",bn,[u("div",null,[l[148]||(l[148]=u("div",{class:"my-task-detail-card-connten-title"},"参与人员",-1)),u("div",xn,[u("div",gn,[l[144]||(l[144]=u("span",{class:"product-label"},"选号人员",-1)),u("span",null,p(null==(C=ni.value)?void 0:C.created_name),1)])]),u("div",hn,[u("div",wn,[l[145]||(l[145]=u("span",{class:"product-label"},"建联人员",-1)),u("span",null,p(null==(D=ni.value)?void 0:D.alliance_personnel),1)])]),u("div",Cn,[u("div",Dn,[l[146]||(l[146]=u("span",{class:"product-label"},"信息填写人员",-1)),u("span",null,p(null==(Y=ti.value[0])?void 0:Y.created_name),1)])]),u("div",Yn,[u("div",zn,[l[147]||(l[147]=u("span",{class:"product-label"},"执行人员",-1)),u("span",jn,[y(p(null==(z=ti.value[0])?void 0:z.executor_name)+" ",1),v(Td,{class:"ml-2",onClick:Mi},{default:m((()=>[v(Gd)])),_:1})])])])])])]),u("div",In,[u("div",Vn,[u("div",null,[l[163]||(l[163]=u("div",{class:"my-task-detail-card-connten-title"},"任务要求",-1)),u("div",Mn,[u("div",Sn,[l[149]||(l[149]=u("span",{class:"product-label"},"期望发布时间",-1)),u("span",null,p(_(U)(null==(j=$s.value)?void 0:j.expiration_time).format("YYYY-MM-DD"))+" 至 "+p(_(U)(null==(I=$s.value)?void 0:I.expiration_time_end).format("YYYY-MM-DD")),1)])]),u("div",On,[u("div",Un,[l[150]||(l[150]=u("span",{class:"product-label"},"期望保留时间",-1)),u("span",null,p(null==(V=$s.value)?void 0:V.expect_remain_time)+"天",1)])]),u("div",qn,[u("div",Jn,[l[151]||(l[151]=u("span",{class:"product-label"},"详细要求",-1)),u("span",Nn,p(null==(M=$s.value)?void 0:M.video_requirements),1)])]),u("div",Ln,[u("div",Pn,[l[152]||(l[152]=u("span",{class:"product-label"},"背景音乐",-1)),u("span",null,p((null==(S=$s.value)?void 0:S.designated_music)||"无"),1)])]),u("div",En,[u("div",Fn,[l[153]||(l[153]=u("span",{class:"product-label"},"道具要求",-1)),u("span",null,p((null==(O=$s.value)?void 0:O.prop_requirements)||"无"),1)])]),u("div",Hn,[u("div",Qn,[l[154]||(l[154]=u("span",{class:"product-label"},"场景要求",-1)),u("span",null,p((null==(q=$s.value)?void 0:q.scene_requirements)||"无"),1)])]),u("div",Rn,[u("div",Wn,[l[155]||(l[155]=u("span",{class:"product-label"},"其他要求",-1)),u("span",null,p((null==(J=$s.value)?void 0:J.other_requirements)||"无"),1)])]),u("div",Tn,[u("div",An,[l[156]||(l[156]=u("span",{class:"product-label"},"是否跳过脚本",-1)),u("span",null,p(1===(null==(N=$s.value)?void 0:N.ignore_script)?"是":"否"),1)])]),u("div",Bn,[u("div",Gn,[l[162]||(l[162]=u("span",{class:"product-label"},"投放广告",-1)),u("div",Kn,[Object.keys(ei.value).length>0?(c(),o("div",Xn,[(null==(P=null==(L=ei.value)?void 0:L.ocean_engine)?void 0:P.advertiser_id)?(c(),o("div",Zn,[l[157]||(l[157]=u("p",null,"你可将视频投放至巨量引擎广告投放平台",-1)),u("p",null,"巨量引擎ID："+p((null==(F=null==(E=ei.value)?void 0:E.ocean_engine)?void 0:F.advertiser_id)||"无"),1)])):r("",!0),(null==(Q=null==(H=ei.value)?void 0:H.qianchuan)?void 0:Q.advertiser_id)?(c(),o("div",$n,[l[158]||(l[158]=u("p",null,"你可将视频投放至巨量千川平台(原视频投放 视频素材投放)",-1)),u("p",null,"巨量千川ID："+p((null==(W=null==(R=ei.value)?void 0:R.qianchuan)?void 0:W.advertiser_id)||"无"),1)])):r("",!0),(null==(A=null==(T=ei.value)?void 0:T.content_marketing)?void 0:A.advertiser_id)?(c(),o("div",es,[l[159]||(l[159]=u("p",null,"你可将视频投放至内容热推",-1)),u("p",null,"巨量引擎ID："+p((null==(G=null==(B=ei.value)?void 0:B.content_marketing)?void 0:G.advertiser_id)||"无"),1)])):r("",!0),(null==(X=null==(K=ei.value)?void 0:K.content_marketing)?void 0:X.advertiser_id)?(c(),o("div",ls,[l[160]||(l[160]=u("p",null,"你可将视频投放至内容服务",-1)),u("p",null,"巨量引擎ID："+p((null==($=null==(Z=ei.value)?void 0:Z.content_marketing)?void 0:$.advertiser_id)||"无"),1)])):r("",!0),(null==(le=null==(ee=ei.value)?void 0:ee.dou_plus)?void 0:le.advertiser_id)||(null==(te=null==(ae=ei.value)?void 0:ae.dou_plus)?void 0:te.dou_plus_uid)?(c(),o("div",as,[l[161]||(l[161]=u("p",null,"你可对视频进行Dou+加热（帮上热门）",-1)),u("p",null,"Dou+广告主抖音UID："+p((null==(se=null==(ne=ei.value)?void 0:ne.dou_plus)?void 0:se.dou_plus_uid)||"无"),1),u("p",null,"Dou+广告主ID："+p((null==(de=null==(ie=ei.value)?void 0:ie.dou_plus)?void 0:de.advertiser_id)||"无"),1)])):r("",!0)])):(c(),o("div",ts,"无"))])])])])])]),u("div",ns,[u("div",ss,[u("div",null,[u("div",is,[u("div",ds,[l[164]||(l[164]=u("span",{class:"product-label"},"附件",-1)),u("span",null,p((null==(oe=$s.value)?void 0:oe.attachment_text)||"无"),1)])]),u("div",os,[u("div",cs,[l[165]||(l[165]=u("span",{class:"product-label"},"挂载搜索词",-1)),u("span",null,p((()=>{var e,l;try{const a="string"==typeof(null==(e=$s.value)?void 0:e.component_info)?JSON.parse($s.value.component_info):{},t="string"==typeof(null==(l=ti.value[0])?void 0:l.component_info)?JSON.parse(ti.value[0].component_info):{};return(null==a?void 0:a.link_component_ids)?null==a?void 0:a.search_word:(null==t?void 0:t.search_word)||"无"}catch(a){return"无"}})()),1)])])])])]),u("div",us,[u("div",rs,[u("div",null,[l[173]||(l[173]=u("div",{class:"my-task-detail-card-connten-title"},"组件信息",-1)),(null==(ce=li.value)?void 0:ce.length)>0?(c(),o("div",vs,[(c(!0),o(f,null,b(li.value,(e=>(c(),o("div",null,[u("div",ps,[u("div",ms,[l[166]||(l[166]=u("span",{class:"product-label"},"组件类型",-1)),u("span",null,p(Es.value[null==e?void 0:e.link_type]),1)])]),u("div",ys,[u("div",ks,[l[167]||(l[167]=u("span",{class:"product-label"},"组件信息",-1)),u("div",_s,[u("div",null,[u("p",fs,p(null==e?void 0:e.link_name),1),u("p",bs,p(null==e?void 0:e.link_title),1),u("p",null,[y(" 组件ID："+p(null==e?void 0:e.component_id)+" ",1),v(Td,{class:"ml-1 cursor-pointer",onClick:l=>qi(null==e?void 0:e.component_id)},{default:m((()=>[v(Kd)])),_:2},1032,["onClick"])])])])])])])))),256))])):Object.keys(ai.value).length>0?(c(),o("div",xs,[u("div",gs,[u("div",hs,[l[168]||(l[168]=u("span",{class:"product-label"},"组件类型",-1)),u("span",null,p(Hs.value[null==(ue=ai.value)?void 0:ue.anchor_type]),1)])]),u("div",ws,[u("div",Cs,[l[169]||(l[169]=u("span",{class:"product-label"},"组件信息",-1)),u("div",Ds,[u("div",null,[u("p",Ys,p(null==(re=ai.value)?void 0:re.anchor_name),1),u("p",zs,p(null==(ve=ai.value)?void 0:ve.anchor_title),1),u("p",null,[y(" 组件ID："+p(null==(pe=ai.value)?void 0:pe.industry_anchor_id)+" ",1),v(Td,{class:"ml-1 cursor-pointer",onClick:l[18]||(l[18]=e=>{var l;return qi(null==(l=ai.value)?void 0:l.industry_anchor_id)})},{default:m((()=>[v(Kd)])),_:1})])])])])])])):Fs.value.length>0?(c(),o("div",js,[l[171]||(l[171]=u("div",{class:"my-task-detail-card-connten-content"},[u("div",{class:"my-task-detail-card-connten-content-item product-item"},[u("span",{class:"product-label"},"组件类型"),u("span",null,"购物车组件")])],-1)),u("div",Is,[u("div",Vs,[l[170]||(l[170]=u("span",{class:"product-label"},"组件信息",-1)),u("div",Ms,[u("div",null,[(c(!0),o(f,null,b(Fs.value,((e,l)=>(c(),o("div",{key:l,class:"mb-standard"},[u("p",Ss,"商品"+p(l+1)+": "+p(e.title||"未命名商品"),1),u("p",{class:"text-link",onClick:l=>Oi(e.product_link)},p(e.product_link||"无链接"),9,Os)])))),128))])])])])])):(c(),o("div",Us,l[172]||(l[172]=[u("p",{style:{color:"#999"}},"无",-1)])))])])])])]})),_:1},8,["modelValue"])])),[[co,ki.value]])}}},[["__scopeId","data-v-55252eab"]]);export{qs as default};
