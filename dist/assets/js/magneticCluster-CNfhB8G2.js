import{s as e,H as t,r as l,a6 as a,b as r,d as o,a as i,c as p,o as s,w as _,y as d,ak as n,E as u,aX as c,aY as m}from"./index-C2bfFjZ1.js";import{_ as h}from"./index.vue_vue_type_script_setup_true_name_ProTable_lang-BSD3Vs5K.js";import{_ as b}from"./_plugin-vue_export-helper-BXFjo1rG.js";import"./index-BMv7HewC.js";import"./sortable.esm-DeWNWKFU.js";const v={class:"table-box"},w=b(e({...e({name:"useProTable"}),setup(e){t(),l("second");const b=l();a({type:1});const w=e=>{if((null==e?void 0:e.video_actual_publish_time)&&e.video_actual_publish_time.length>0){let t={start_time:e.video_actual_publish_time[0],end_time:e.video_actual_publish_time[1]};e.created_at=t,delete e.video_actual_publish_time}return m(e)},x=a([{prop:"视频信息",label:"视频信息 ",showOverflowTooltip:!1,render:e=>{var t,l,a,i;return r("div",{style:{display:"flex"}},[r("img",{src:null==(t=e.row)?void 0:t.video_img,height:"90px",width:"50px",style:"height: 80px;width: 50px;",alt:""},null),r("div",{style:"text-align: left;margin-left: 1rem;"},[r("p",{class:"text-xs"},[o("视频标题："),r("a",{target:"_blank",class:"text-blue-500 cursor-pointer",style:"text-decoration: none;color:rgba(59,130,246)",href:null==(l=e.row)?void 0:l.video_url},[null==(a=e.row)?void 0:a.video_name])]),r("div",null,[o("订单ID: "),null==(i=e.row)?void 0:i.order_id])])])},width:280},{prop:"达人信息",label:"达人信息",width:180,showOverflowTooltip:!1,render:e=>{var t,l;return r("div",{class:"flex"},[r("div",{class:"p-1"},[r("p",{class:"text-xs"},[o("达人昵称："),null==(t=e.row)?void 0:t.kol_name]),r("p",{class:"text-xs mt-2"},[o("达人ID："),null==(l=e.row)?void 0:l.kol_id])])])},width:150},{label:"发布时间",prop:"video_actual_publish_time",width:180,search:{el:"date-picker",span:1,props:{type:"daterange",valueFormat:"YYYY-MM-DD"}}},{label:"任务名称",width:180,prop:"task_name",search:{el:"input"}},{label:"任务ID",width:160,prop:"task_id",search:{el:"input"}},{label:"播放量(W)",width:180,prop:"play_count"},{label:"点赞量（W）",width:180,prop:"like_count"},{label:"评论量",width:150,prop:"comment_count"},{label:"转发量",prop:"share_count"},{label:"互动数（W）",width:180,prop:"interactions_w"},{label:"5s播放率",width:180,prop:"play_5s_rate"},{label:"完播率",prop:"completion_rate"},{label:"播放时长（S）",width:180,prop:"average_play_duration"},{label:"触达人数",prop:"reach"},{label:"触达率",prop:"reach_rare"},{label:"平台裸价",width:180,prop:"platform_bare_price"},{label:"含5%平台服务费",width:200,prop:"platform_bare_price_service"},{label:"执行价（报价）",width:180,prop:"exercise_price"},{label:"CPM",prop:"cpm"},{label:"CPE",prop:"cpe"},{label:"组件曝光量（W）",width:200,prop:"component_exposure_count",with:"200px"},{label:"组件曝光率",width:180,prop:"component_exposure_count_rate"},{label:"组件点击量",width:180,prop:"component_click_count"},{label:"CTR",prop:"component_click_through_rate"},{label:"数据更新时间",width:180,prop:"data_last_updated_time"}]),f=()=>{var e,t;if(!b.value)return void u({type:"error",message:"表格数据未加载，请稍后再试"});let l=null==(e=b.value)?void 0:e.searchParam;try{let e={start_time:null==l?void 0:l.order_time[0],end_time:null==l?void 0:l.order_time[1]},t={start_time:null==l?void 0:l.operation_time[0],end_time:null==l?void 0:l.operation_time[1]},r={start_time:null==l?void 0:l.video_actual_publish_time[0],end_time:null==l?void 0:l.video_actual_publish_time[1]};var a=JSON.parse(JSON.stringify(l));a.operation_time=t,a.created_at=r,a.order_time=e}catch(o){}let r=null==(t=b.value)?void 0:t.pageable;c({...r,...a}).then((e=>{990===Number(e.code)&&u({type:"success",message:"导出成功，请到消息中导出记录查收"})}))};return(e,t)=>{const l=i("el-button");return s(),p("div",v,[r(h,{ref_key:"proTable",ref:b,columns:x,"request-api":w},{tableHeader:_((e=>[r(l,{type:"primary",icon:d(n),onClick:f},{default:_((()=>t[0]||(t[0]=[o("导出")]))),_:1},8,["icon"])])),_:1},8,["columns"])])}}}),[["__scopeId","data-v-f8e19299"]]);export{w as default};
